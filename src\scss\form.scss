.form__wrapper {
  padding: 90px 0px 80px 0px;
  font-family: Poppins;

  .form__section {
    margin-bottom: 10px;

    .form__section-title {
      margin-bottom: 5px;
      white-space: nowrap;
    }

    .form__section-line {
      width: 100%;
      height: 1px;
      background-color: #e7e7e7;
      margin-left: 10px;
    }
  }

  .form__input-row {
    .ant_selector_with_error {
      .ant-select-selector{
        border: 1px solid red !important;
        background: rgb(255, 239, 242)!important;
        border-bottom: none !important;
        height: 28px !important;
      }
    }

    .form-seller__selector,
    .form-seller__selector .ant-select,
    .product-filter__wrapper .ant-select {
      border: none !important;
      height: 28px !important;
      border-radius: 4px;
    }
    .form__input-row__input,
    .form__input-row__text,
    .product-filter__wrapper .ant-select{
      width: calc(100% - 15px) !important;
      height: 28px;
      border-radius: 4px;
      border: none;
      background-color: #efefef;
      font-size: 12px;
      margin-bottom: 12px;
      padding: 0px !important;

      input {
        width: 100%;
      }

      .ant-picker {
        background-color: #efefef !important;
      }

      .ant-picker-input {
        height: 28px;
      }

      .input-error {
        display: none;
      }
    }

    div div .form__input-row__input {
      padding: 0px 10px !important;

      .ant-select {
        width: calc(100% + 20px) !important;
        margin-left: -10px !important;
      }
    }

    div div .ant-picker {
      padding: 0px !important;
    }

    .ant-picker-input {
      input {
        margin-left: 5px;
      }
    }

    .input-error {
      display: none;
    }

    .form__input-row__input {
      input {
        height: 28px;
        border-radius: 4px;
        border: none;
        background-color: #efefef;
        font-size: 12px;
      }

      .ant-picker,
      .ant-picker-input,
      .ant-select {
        border: none !important;
        border-radius: 4px !important;
        padding: 0px 0px !important;
        background-color: #efefef;

        .ant-select-selection-item {
          margin-top: 0px !important;
        }

        .ant-select-selection-placeholder {
          margin-top: 0px !important;
        }
      }

      .ant-select-selector {
        border: none !important;
        height: 28px;
        background-color: #efefef;
      }
    }
    .ant-upload{
      button{
        height: 28px;
        line-height: 25px;
        background-color: #efefef;
        padding: 0px 10px;
        border: none;
        border-radius: 4px;
        font-size: 13px !important;
        width: 100px;
      }
    }

    .form__input-row__input-wrapper {
      width: 100%;
    }
  }

  .form__input-row__label {
    font-size: 12px;
    margin-top: 6px !important;
    margin-bottom: 3px !important;
    font-weight: 500;
    display: block !important;
  }

  .form__input-row__text {
    font-size: 13px;
    white-space: nowrap;
  }

  .form__input-row__address__wrapper {
    width: calc(100% - 15px);
    margin-bottom: 5px;

    .form__input-row__address {
      width: 100%;
      background-color: rgba(45, 124, 247, 0.1);
      padding: 10px;
      border-radius: 5px;
      display: flex;
      font-size: 13px;
      height: 100px;

      .form__input-row__address-info {
        width: calc(100% - 60px);
        line-height: 20px;
        margin-top: -2px;
        display: -webkit-box;
        -webkit-line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
        font-size: 12px;
      }

      .form__input-row__address-icon {
        margin-left: auto;
        background-color: #2d7cf7;
        font-size: 11px;
        height: 20px;
        border-radius: 10px;
        padding: 3px 7px;
        color: white;
        display: flex;
        cursor: pointer;

        .anticon {
          margin-top: 1px;
          margin-right: 4px;
        }
      }

      .form__input-row__address-placeholder {
        color: #6e6e6e;
      }
    }
  }

  .form__input-row__address-disabled {
    .form__input-row__address {
      background-color: #f5f5f5 !important;
    }

    .form__input-row__address-icon {
      background-color: #b0b0b0 !important;
      cursor: not-allowed !important;
    }
  }

  .form__input-row__address-error {
    .form__input-row__address {
      background-color: #ffeff2 !important;
      border: 1px solid #f55a64 !important;
      border-width: 1px 1px 1px 1px !important;
    }
  }

  .form__input-row__input-error {
    background-color: rgb(255, 239, 242) !important;
    border-width: 1px 1px 1px 1px !important;
    width: calc(100% - 15px);
    height: 28px !important;
    border-radius: 4px;
    font-size: 12px;
    padding: 0px 10px;

    .input-error {
      display: none !important;
    }

    .ant-picker .ant-picker-input {
      background-color: #ffeff2 !important;

      input {
        background-color: #ffeff2 !important;
      }
    }

    .ant-picker-input {
      border: 1px solid #f55a64 !important;
      background-color: #ffeff2 !important;
      border-width: 1px 1px 1px 1px;

      input {
        background-color: #ffeff2 !important;
      }
    }

    .ant-select {
      border: 1px solid #f55a64 !important;
      background-color: #ffeff2 !important;
      border-width: 1px 1px 1px 1px;
      height: 28px;
    }

    input {
      background-color: rgb(255, 239, 242) !important;
    }
  }

  .form__recipients__input-error {
    .ant-select-selector {
      border: 1px solid #f55a64;
      background-color: #ffeff2;
    }
  }

  .form__input__select-error {
    width: calc(100% - 15px) !important;
      .ant-select-selector {
        border: 1px solid #f55a64 !important;
        background-color: #ffeff2;
      }
  }

  .form__input-row__text {
    line-height: 28px;
    padding-left: 5px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .ant-table-thead {
    white-space: nowrap !important;
  }

  .ant-table {
    .orgInputContainer {
      margin-bottom: 0px;
    }
  }

  .ant-picker-input {
    input {
      font-size: 13px !important;
    }

    .ant-picker-suffix {
      margin-right: 8px;
    }
  }

  .ant-table-thead {
    white-space: nowrap !important;
  }
}

.form__wrapper__collapsed {
  padding: 20px 0px 80px 0px !important;
}

.form__loading__wrapper {
  margin-top: 90px;

  .form__loading__section {
    margin-bottom: 10px;

    .form__loading__heading {
      height: 32px;
      width: calc(100% - 15px);
      margin-bottom: 5px;
    }

    .form__loading__row {
      width: calc(100% - 15px);

      .form__loading__label {
        height: 12px;
        margin-bottom: 3px;
      }

      .form__loading__input {
        height: 28px;
        margin-bottom: 12px;
      }

      .form__loading__input2 {
        height: 112px;
        margin-bottom: 12px;
      }

    }
  }

}

.form__lines-wrapper {
  margin-top: 20px;
  width: 100%;

  .form-title__wrapper {
    display: flex;
    justify-content: space-between;

    .form-title-left {
      display: flex;
      align-items: center;
    }
  }

  .input-error {
    font-size: 9px;
    font-weight: 600;
  }
}

.form__data-wrapper {
  .form__data-tc {
    margin-top: 10px;
    margin-bottom: 0px;
  }

  .form__data-attachment {
    margin-top: 10px;
    margin-bottom: 0px;
  }

  .form-calculator__wrapper {
    display: flex;
    margin-left: 20px;
    margin-top: 17px;

    .form-calculator {
      width: 100%;
      margin-left: auto;
      background-color: rgba(45, 125, 247, 0.1);
      margin-top: 10px;
      padding: 10px;
      border-radius: 5px;

      .form-calculator__field {
        display: flex;
        line-height: 32px;
        font-size: 14px;

        .ant-select,
        input {
          border-radius: 4px !important;
          background-color: white;
        }

        .form-calculator__field-name {
          width: 50%;
          font-weight: 500;

          .select_extra_charge_wrapper {
            display: flex;
            gap: 10px;
            justify-content: flex-start;
            align-items: flex-start;
            width: fit-content !important;
          }

          .orgInputContainer,
          .orgFormInput {
            width: calc(100% - 50px);
          }
        }

        .form-calculator__field-value {
          padding-left: 20px;
          width: 50%;

          .orgInputContainer {
            width: 80%;
          }

          .form-calculator__delete-line-button {
            margin-left: 5px;
          }

          .form-calculator__discount-type {
            .ant-select {
              border: 1px dashed rgba(68, 130, 218, 0.2);
              border-radius: 2px;
              height: 28px;
              width: 50px;
              font-size: 13px;

              .ant-select-selector {
                // color: rgba(0, 0, 0, 0.25);
                background: rgba(0, 0, 0, 0.04);
                height: 28px !important;
                // cursor: not-allowed;
              }
            }
          }

        }
      }

      .form-calculator__field-total {
        font-size: 18px;
        border-top: 1px solid grey;
        margin-top: 10px;
        padding-top: 10px;
      }
    }
  }
}

.form__footer {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  background-color: white;
  width: calc(100% - 82px);
  margin-left: -15px;
  border-top: 1px solid #f1f1f1;
  padding: 15px;
  z-index: 14;

  .button-default {
    border-radius: 3px;
    white-space: nowrap;
    width: auto;
    padding: 8px 10px;
  }

  .form__footer-draft {
    margin-right: 10px;
    border: 1px solid #2D7DF7;
    background: white;
    min-width: 120px;

    .button-wrapper__text {
      color: #2D7DF7 !important;
    }

    .anticon-loading {
      color: #2D7DF7 !important;
    }
  }

  .form__footer-submit {
    min-width: 160px;
  }

  .form-barcode__wrapper {
    margin-left: auto;
  }
}

.form__footer-fixed {
  width: calc(100% - 230px) !important;
}

.form-error__input {
  .orgFormInput {
    border: 1px solid red !important;
  }

  .input-error {
    display: none;
  }
}

.form__delete-line-button {
  background-color: red;
  display: inline-block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
  border-radius: 22px;
  color: white;

  &:hover {
    cursor: pointer;
  }
}

.prz-input-blue {
  border: 1px solid rgba(68, 130, 218, 0.2) !important;
  border-radius: 2px !important;
  height: 28px;
}