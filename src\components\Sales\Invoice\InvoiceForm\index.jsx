import React, { Fragment, useEffect, useState } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Checkbox, DatePicker, Drawer, Input, notification, Popconfirm, Radio, Select, Tooltip, Upload } from 'antd';
import { EditFilled, InfoCircleOutlined, PlusCircleFilled, PlusOutlined, CloseOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleXmark, faCircleInfo } from '@fortawesome/free-solid-svg-icons';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import Decimal from 'decimal.js';

// Constants & Helpers
import Constants, { QUANTITY, DEFAULT_CUR_ROUND_OFF } from '@Apis/constants';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import FormHelpers from '@Helpers/FormHelpers';

// UI Components
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import H3Image from '@Uilib/h3Image';
import PRZButton from '../../../Common/UI/PRZButton';
import PRZConfirmationPopover from '../../../Common/UI/PRZConfirmationPopover';
import PRZInput from '../../../Common/UI/PRZInput';
import PRZSelect from '../../../Common/UI/PRZSelect';
import PRZText from '../../../Common/UI/PRZText';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput';

// Common Components
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import SelectDepartment from '@Components/Common/SelectDepartment';
import SelectCustomer from '@Components/Common/SelectCustomer';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import SelectAppUser from '@Components/Common/SelectAppUser';
import RichTextEditor from '@Components/Common/RichTextEditor';
import BarcodeReader from '@Components/Common/BarcodeReader';
import SelectPriceList from '@Components/Common/SelectPriceList';
import SelectTaxType from '@Components/Admin/Common/SelectTaxType';
import SelectExtraCharge from '@Components/Admin/Common/SelectExtraCharge';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import CustomDocumentColumns from '@Components/Common/CustomDocumentColumns';
import TagSelector from '@Components/Common/Selector/TagSelector';
import TransporterForm from '@Components/Common/TransporterForm';
import FreightTaxInput from '@Components/Common/FreightTaxInput';
import FormLoadingSkull from '@Components/Common/formLoadingSkull';
import TenantSelector from '@Components/Common/Selector/TenantSelector';
import ChargesTaxInput from '../../../Common/ChargesTaxInput';
import CurrencyConversionV2 from '../../../Common/CurrencyConversionV2';
import RestrictedAccessMessage from '../../../Common/RestrictedAccess/RestrictedAccessMessage';
import ErrorHandle from '../../../Common/ErrorHandle';
import TnCSelector from '@Components/Admin/Common/TnCSelector';
import SelectPaymentRemark from '@Components/Common/SelectPaymentRemark';

// Actions
import CustomerActions from '@Actions/customerActions';
import OfferActions from '@Actions/offerActions';
import TenantActions from '@Actions/tenantActions';
import InvoiceActions from '@Actions/invoiceActions';
import PriceListActions from '@Actions/inventory/priceListActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import TagActions from '@Actions/tagActions';
import CustomStatusActions from '@Actions/configurations/customStatusActions';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import ProductActions from '@Actions/productActions';
import TallyIntegrationActions from '@Actions/integrations/tallyIntegrationActions';
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import WorkflowActions from '@Actions/workflowActions';
import SellerActions from '@Actions/sellerActions';
import AddressActions from '@Actions/addressActions';
import DocConfigActions from '@Actions/docConfigActions';
import MOActions from '@Actions/moActions';
import ExtraChargesActions from '@Actions/configurations/extraChargesAction';

// Assets
import { cdnUrl } from "@Utils/cdnHelper";

// Current File Components & Helpers
import InvoiceLines from './InvoiceLines';
import InvoiceErrorList from './InvoiceErrors';
import CustomerForm from '../../Customer/CustomerHome/CreateCustomer/CustomerForm';
import { getAvailableQty, getBatches, getDraftStatusType, getLineTotals, handleProductChangeHelper, getDataSource, INITIAL_STATE, createInvoiceHelper, addNewRowHelper, addNewChargeRowHelper, toggleBatchInnerHelper, toggleBatchHelper, handleDeleteHelper, handleProductChangeValueHelper, customInputChangeHelper, customLineInputChangeHelper, handleMultiProductChangeHelper } from './helpers';

// Styles
import './style.scss';
import '../../../../scss/form.scss';

const { Option } = Select;
const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

const InvoiceForm = (props) => {
  const {
    createInvoiceLoading, isPackingSlip, isPackingSlipFromSalesOrder, updateInvoiceLoading, match, getInvoiceByIdLoading, MONEY, selectedInvoice, CurrenciesResults, getCurrenciesLoading, PaddingTop, location, selectedOrderForInvoice, getProductByIdLoading, user, moRmLines, getCustomers, moInfo, tenantId, tallyConnections, getTallyConnectionsLoading, getApplyPriceLists, getDocConfigInvoiceLoading, orgStatusInvoice, getInvoiceById, getDocCFV2, getDocConfig, getTallyConnections, getOrgStatus, getCurrencies, getInvoiceByIdSuccess, cfV2DocInvoice, workOrder, getWorkOrderRm, getWorkOrderRmLoading, jwRmLines, priceMasking, rmTenantDepartmentId, isCarryForward, selectedDCForInvoice, getCharges,
  } = props;
  const [state, setState] = useState(INITIAL_STATE(user));
  const {
    data, formSubmitted, shippingAddress, selectedCustomer, chargeData, accountManager, selectedTags, billFrom, shipFrom,
    withApproval, paymentTerms, invoiceDate, showAddressDrawer, selectedAddressType, invoiceNumber, paymentRemark, initialInvNumber, docSeqId,
    billingAddress, termsAndConditions, isLineWiseDiscount, discountPercentage, discountType, taxTypeId, tncId,
    taxTypeName, gstNumber, cfInvoiceDoc, tenantDepartmentId, currentAction, selectedCurrentInvoice,
    showAddCustomer, showNewCustomerModal, salesAccount, freightTax, allStock, fileList, selectedFg,
    selectedPriceList, productSkuIds, pricesToApply, selectedCurrencyName, selectedCurrencyID,
    sendWhatsappNotification, charge1Name, charge1Value, dontDeductStock, visibleColumns, freightTaxId,
    freightSacCode, openFreightTax, createExpenseCheckbox, transporterId, vehicleNumber,
    transporterBillNumber, cfInvoiceLine, freightTaxInfo, taxTypeInfo, emptyQytForOnce, selectedTenant, currentSelectedTenantInfo,
    isUserReadyForCF, isUserReady, checkedRecipients, toRecipients, selectedWorkOrder, calculateRateFromBatches, createDCAutomatically, isAutomaticConversionRate, currencyConversionRate, updateDocumentReason, freightTaxData, narration, selectedTenantTallyIntegrationId,
  } = state;

  const updateState = (key, value) => {
    setState((prevState) => ({
      ...prevState,
      [key]: value,
    }));
  };

  useEffect(() => {
    let selectedTenantIdInv;
    if (selectedOrderForInvoice) {
      selectedTenantIdInv = selectedOrderForInvoice?.tenant_id;
    } else if (location?.state?.selectedTenantIdInv) {
      selectedTenantIdInv = location?.state?.selectedTenantIdInv;
    } else {
      selectedTenantIdInv = user?.tenant_info?.tenant_id;
    }
  }, [selectedInvoice, selectedOrderForInvoice, tenantId]);

  useEffect(() => {
    getInvoiceByIdSuccess(null);
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'INVOICE',
    };
    getDocCFV2(payload);
    getCurrencies();
    getTallyConnections(user?.tenant_info?.org_id);
    getOrgStatus({ orgId: user?.tenant_info?.org_id, entityName: 'INVOICE' }, () => { });
    sessionStorage.setItem('staticReservations', JSON.stringify({}));
    const { invoiceId } = match.params;
    const invIdClone = location?.state?.cloneInv;
    const selectedTenantIdInv = location?.state?.selectedTenantIdInv;
    const invIdCopy = invIdClone || invoiceId;
    if (invIdCopy) {
      getInvoiceById(user?.tenant_info?.org_id, (selectedTenantIdInv || tenantId || user?.tenant_info?.tenant_id), invIdCopy);
    }
    () => {
      updateState('isUserReadyForCF', false);
      updateState('data', []);
      updateState('discountType', 'Percent');
    };

    getCharges(user?.tenant_info?.org_id, 'SALES', (charges) => {
      const freight = charges?.find((i) => (i?.ledger_name === 'Freight Charge' && i?.is_system_charge));
      if (freight) {
        updateState('freightSacCode', freight?.charge_sac_code);
      }
    });
  }, []);

  useEffect(() => {

    if (selectedOrderForInvoice && cfV2DocInvoice && !state?.selectedOrderForInvoice) {
      const invoiceCreateFrom = selectedOrderForInvoice?.sales_order_lines?.length > 0 ? selectedOrderForInvoice?.sales_order_lines : selectedOrderForInvoice?.delivery_challan_lines;
      const oldCustomField = selectedOrderForInvoice?.custom_fields || [];
      const salesOrderLines = [];
      const invoiceFromDC = selectedOrderForInvoice?.delivery_challan_id;
      for (let i = 0; i < invoiceCreateFrom?.length; i++) {
        const orderLine = invoiceCreateFrom[i];
        const parentKey = uuidv4();
        const pendingQuantity = Math.max(new Decimal(orderLine?.quantity || 0).minus(orderLine?.invoice_gen_quantity || 0).toNumber(), 0);
        const batches = getBatches(Helpers.batchSelectionMethod(orderLine?.default_outwards_method || Helpers.getBatchMethod(orderLine), orderLine?.available_batches, pendingQuantity), Math.min(pendingQuantity + Number(orderLine?.free_quantity), Helpers.getValueTotalInObject(orderLine?.available_batches, 'available_qty')), orderLine?.uom_info?.[0], orderLine?.product_sku_info?.uom_info, null, parentKey, false, true);
        const orderLineQty = ['NON_STORABLE', 'SERVICE'].includes(orderLine?.product_sku_info?.product_type) ? orderLine?.quantity : Math.min(orderLine?.quantity, getAvailableQty(orderLine, batches, state, { ...props }, (newState) => setState((prevState) => ({ ...prevState, ...newState }))));

        const documentLineCustomFields = cfV2DocInvoice?.data?.document_line_custom_fields.filter((item) => item?.is_active);
        const availableBatches = orderLine?.available_batches?.filter((item) => !item?.is_rejected_batch) || [];
        const availableQtyTotal = Helpers.getValueTotalInObject(availableBatches, 'available_qty');
        const orderLineQuantity = Number(pendingQuantity);
        const oldLineCustomFields = orderLine?.custom_fields || [];
        const mergedArrayForLinesCfs = CustomFieldHelpers.mergeCustomFields(documentLineCustomFields, oldLineCustomFields);

        const discountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(orderLine?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(orderLine?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = orderLine?.is_discount_in_percent ? (orderLineQuantity * orderLine.unit_price) * (1 - discountValue / 100) : Math.max(orderLineQuantity * orderLine.unit_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(orderLineQuantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);

        let freeQty = 0;
        const isNonStorableOrService = ['NON_STORABLE', 'SERVICE'].includes(orderLine?.product_sku_info?.product_type);

        if (isNonStorableOrService) {
          freeQty = orderLine?.free_quantity || 0;
        } else {
          const availableQty = getAvailableQty(
            orderLine,
            batches,
            state,
            { ...props },
            (newState) =>
              setState((prevState) => ({
                ...prevState,
                ...newState,
              }))
          );

          const calculatedQty = Math.min(orderLine?.free_quantity ?? 0, availableQty - orderLineQty);

          freeQty = Math.max(calculatedQty, 0);
        }

        salesOrderLines.push({
          key: parentKey,
          asset1: orderLine?.product_sku_info?.assets?.[0]?.url,
          product_sku_name: orderLine?.product_sku_info?.product_sku_name,
          product_category_info: orderLine.product_sku_info?.product_category_info,
          showFreebie: Number(orderLine?.free_quantity) > 0,
          quantity: orderLineQuantity,
          pending_quantity_so: orderLineQuantity,
          so_quantity: orderLine?.quantity,
          invoice_gen_quantity: orderLine?.invoice_gen_quantity,
          so_free_quantity: orderLine?.free_quantity || 0,
          free_quantity: freeQty,
          unitPrice: orderLine?.unit_price || 0,
          hsn_code: orderLine?.hsn_code || orderLine?.product_sku_info?.hsn_code,
          uomId: Number(orderLine?.uom_id),
          uomGroup: Number(orderLine?.group_id),
          uom_info: orderLine?.uom_info?.[0],
          taxId: Number(orderLine?.tax_id),
          taxInfo: orderLine?.tax_group_info,
          child_taxes: Helpers.computeTaxation(taxableValue, orderLine?.tax_group_info, user?.tenant_info?.state, (selectedOrderForInvoice?.billing_address_info?.state || selectedOrderForInvoice?.sales_order_info?.billing_address_info?.state || selectedOrderForInvoice?.source_address?.state))?.tax_info?.child_taxes,
          discount: discountValue,
          lineDiscountType: orderLine?.is_discount_in_percent ? 'Percent' : 'Amount',
          unitDiscount: unitDiscountValue,
          showUnitDiscount: !orderLine?.is_discount_in_percent && unitDiscountValue > 0,
          order_line_id: orderLine?.order_line_id,
          delivery_challan_line_id: orderLine?.delivery_challan_line_id,
          tenant_product_id: orderLine?.tenant_product_id,
          product_batches: batches,
          expiry_days: orderLine?.product_sku_info?.expiry_days,
          product_sku_info: orderLine?.product_sku_info,
          remarks: orderLine?.remarks || '',
          remarkRequired: !!orderLine?.remarks?.replace(/<[^>]+>/g, ''),
          sales_account_name: user?.tenant_info?.tally_configuration?.sales_account_name,
          batchConsumptionMethod: orderLine?.default_outwards_method || Helpers.getBatchMethod(orderLine),
          lineCustomFields: mergedArrayForLinesCfs?.map((field) => ({
            ...field,
            fieldValue: field?.fieldName === 'Rate'
              ? orderLine?.unit_price
              : (field?.fieldName === 'Quantity'
                ? Math.min(orderLineQuantity, availableQtyTotal)
                : field?.fieldValue),
          })),
          bundle_products: orderLine?.bundle_products?.map((item) => {
            const lineKey = uuidv4();
            const bundleDiscountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(item?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(item?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
            const bundleTaxableValue = orderLine?.is_discount_in_percent ? (item?.quantity * item?.unit_price * Number(100 - bundleDiscountValue) / 100) : ((item?.quantity * item?.unit_price) - bundleDiscountValue);
            return {
              ...item,
              parentKey,
              key: lineKey,
              unitPrice: item?.unit_price || 0,
              uomId: Number(item?.uom_id),
              uomGroup: Number(item?.group_id),
              uom_info: item?.uom_info?.[0],
              taxId: Number(item?.tax_group_info?.tax_id),
              taxInfo: item?.tax_group_info,
              child_taxes: Helpers.computeTaxation(bundleTaxableValue, item?.tax_group_info, user?.tenant_info?.state, (selectedOrderForInvoice?.billing_address_info?.state || selectedOrderForInvoice?.sales_order_info?.billing_address_info?.state || selectedOrderForInvoice?.source_address?.state))?.tax_info?.child_taxes,
              product_sku_name: item?.product_sku_info?.product_sku_name,
              so_quantity: item?.quantity,
              expiry_days: item?.product_sku_info?.expiry_days,
              product_sku_info: item?.product_sku_info,
              batchConsumptionMethod: item?.default_outwards_method || Helpers.getBatchMethod(item),
              product_batches: getBatches(item?.available_batches, Math.min(new Decimal(item?.quantity || 0).minus(new Decimal(item?.invoice_gen_quantity || 0)), Helpers.getValueTotalInObject(item?.available_batches, 'available_qty')), item?.uom_info?.[0], item?.product_sku_info?.uom_info, null, lineKey, false, true),
            };
          }),
          manufacturingDateFormat: orderLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: orderLine?.product_sku_info?.expiry_date_format,
        });
      }

      const oldLineCustomField = selectedOrderForInvoice.sales_order_lines?.[0]?.custom_fields || [];
      const documentLineCustomFields = cfV2DocInvoice?.data?.document_line_custom_fields?.filter((item) => item?.is_active);

      // we are merging the old custom fields with the new custom fields keeping the value from the old custom fields
      const mergedArrayForLinesCfs = CustomFieldHelpers.mergeCustomFields(documentLineCustomFields, oldLineCustomField);
      const mergedArrayForDocCfs = CustomFieldHelpers.mergeCustomFields(cfV2DocInvoice?.data?.document_custom_fields, oldCustomField);
      const cfInvoiceLineTemp = mergedArrayForLinesCfs || [];
      const visibleColumnsTemp = CustomFieldHelpers.updateVisibleColumns(CustomFieldHelpers.postCfStructure(mergedArrayForLinesCfs), visibleColumns);

      // for updating pricesToApply state
      getApplyPriceLists(
        selectedOrderForInvoice?.tenant_id || user?.tenant_info?.tenant_id,
        selectedOrderForInvoice?.price_list_id || selectedOrderForInvoice?.sales_order_info?.price_list_id,
        selectedOrderForInvoice?.customer_id || selectedOrderForInvoice?.dc_customer_id || selectedOrderForInvoice?.adhoc_dc_customer_id,
        selectedOrderForInvoice?.shipping_address_info?.state || selectedOrderForInvoice?.sales_order_info?.shipping_address_info?.state || selectedOrderForInvoice?.destination_address?.state,
        invoiceCreateFrom?.map((line) => (line?.product_sku_info?.product_sku_id)) || [],
        (toApplyPriceList) => {
          updateState('pricesToApply', toApplyPriceList);
        },
      );
      setState((prevState) => ({
        ...prevState,
        billFrom: selectedOrderForInvoice?.tenant_billing_address_info,
        shipFrom: selectedOrderForInvoice?.delivery_challan_id ? selectedOrderForInvoice?.source_address : selectedOrderForInvoice?.tenant_shipping_address_info,
        selectedTenant: selectedOrderForInvoice?.tenant_id,
        selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedOrderForInvoice?.tenant_id)?.it_id,
        selectedCustomer: selectedOrderForInvoice?.customer_id || selectedOrderForInvoice?.dc_customer_id || selectedOrderForInvoice?.adhoc_dc_customer_id,
        accountManager: selectedOrderForInvoice?.account_manager_id || selectedOrderForInvoice?.sales_order_info?.account_manager_id,
        selectedPriceList: selectedOrderForInvoice?.price_list_id || selectedOrderForInvoice?.sales_order_info?.price_list_id,
        billingAddress: selectedOrderForInvoice?.billing_address_info || selectedOrderForInvoice?.sales_order_info?.billing_address_info || selectedOrderForInvoice?.adhoc_dc_customer_info?.billing_address_info,
        shippingAddress: selectedOrderForInvoice?.shipping_address_info || selectedOrderForInvoice?.sales_order_info?.shipping_address_info || selectedOrderForInvoice?.destination_address,
        data: salesOrderLines || [],
        selectedCustomerInfo: selectedOrderForInvoice?.adhoc_dc_customer_info || selectedOrderForInvoice?.customer_info || selectedOrderForInvoice?.sales_order_info?.customer_info,
        gstNumber: selectedOrderForInvoice?.customer_info?.gst_number || selectedOrderForInvoice?.sales_order_info?.customer_info?.gst_number || selectedOrderForInvoice?.gst_number || '',
        paymentTerms: Number(
          selectedOrderForInvoice?.sales_order_info?.customer_info?.default_payment_terms?.due_days ??
          selectedOrderForInvoice?.adhoc_dc_customer_info?.default_payment_terms?.due_days ??
          selectedOrderForInvoice?.payment_terms?.[0]?.due_days ??
          selectedOrderForInvoice?.customer_info?.default_payment_terms?.due_days ?? 0
        ),
        paymentRemark: selectedOrderForInvoice?.payment_terms?.[0]?.remark || '',
        toRecipients: selectedOrderForInvoice?.notification_recipients || [],
        checkedRecipients: selectedOrderForInvoice?.is_automatic_notification_enabled,
        fileList: selectedOrderForInvoice?.attachments || selectedOrderForInvoice?.sales_order_info?.attachments,
        selectedOrderForInvoice: selectedOrderForInvoice?.sales_order_info || selectedOrderForInvoice,
        tenantDepartmentId: selectedOrderForInvoice?.tenant_department_info?.tenant_department_id || selectedOrderForInvoice?.tenant_department_id || user?.tenant_info?.default_department_for_sales,
        discountPercentage: selectedOrderForInvoice?.is_discount_in_percent ? (selectedOrderForInvoice?.discount_percentage?.toFixed(DEFAULT_CUR_ROUND_OFF) || selectedOrderForInvoice?.sales_order_info?.discount_percentage?.toFixed(DEFAULT_CUR_ROUND_OFF)) : (selectedOrderForInvoice?.discount_amount?.toFixed(DEFAULT_CUR_ROUND_OFF) || selectedOrderForInvoice?.sales_order_info?.discount_amount?.toFixed(DEFAULT_CUR_ROUND_OFF)),
        isLineWiseDiscount: selectedOrderForInvoice?.is_line_wise_discount || selectedOrderForInvoice?.sales_order_info?.is_line_wise_discount,
        discountType: selectedOrderForInvoice?.is_discount_in_percent ? 'Percent' : 'Amount',
        chargeData: selectedOrderForInvoice?.other_charges?.map((item) => ({
          ...item,
          chargeKey: uuidv4(),
          chargesTaxId: item?.tax_info?.tax_id,
          chargesSacCode: item?.charge_sac_code,
          chargesTaxInfo: item?.tax_info,
          chargesTax: item?.tax_info?.tax_value,
          tallyLedgerName: item?.ledger_name || null,
          chargesTaxData: {
            ...item?.tax_info,
            child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, (selectedOrderForInvoice?.billing_address_info?.state || selectedOrderForInvoice?.sales_order_info?.billing_address_info?.state || selectedOrderForInvoice?.source_address?.state))?.tax_info?.child_taxes,
          },
        })) || [],
        selectedCurrencyID: selectedOrderForInvoice?.org_currency_info?.org_currency_id || selectedOrderForInvoice?.sales_order_info?.org_currency_info?.org_currency_id,
        selectedCurrencyName: selectedOrderForInvoice?.org_currency_info || selectedOrderForInvoice?.sales_order_info?.org_currency_info,
        sendWhatsappNotification: user?.tenant_info?.is_inv_automatic_notification_enabled,
        dontDeductStock: selectedOrderForInvoice?.bypass_inventory_change,
        charge1Name: selectedOrderForInvoice?.charge_1_name || 'Freight',
        charge1Value: selectedOrderForInvoice?.charge_1_value,
        createExpenseCheckbox: selectedOrderForInvoice?.transporter_id || selectedOrderForInvoice?.vehicle_number,
        transporterId: selectedOrderForInvoice?.transporter_id,
        vehicleNumber: selectedOrderForInvoice?.vehicle_number,
        transporterBillNumber: selectedOrderForInvoice?.transporter_bill_number,
        freightTaxId: selectedOrderForInvoice?.freight_tax_id || 'Not Applicable',
        freightTax: selectedOrderForInvoice?.freight_tax_info?.tax_value,
        freightTaxInfo: selectedOrderForInvoice?.freight_tax_info,
        freightTaxData: {
          ...selectedOrderForInvoice?.freight_tax_info,
          child_taxes: Helpers.computeTaxation(selectedOrderForInvoice?.charge_1_value, selectedOrderForInvoice?.freight_tax_info, user?.tenant_info?.state, (selectedOrderForInvoice?.billing_address_info?.state || selectedOrderForInvoice?.sales_order_info?.billing_address_info?.state || selectedOrderForInvoice?.source_address?.state))?.tax_info?.child_taxes
        },
        freightSacCode: selectedOrderForInvoice?.freight_sac_code,
        termsAndConditions: selectedOrderForInvoice?.terms_and_conditions || selectedOrderForInvoice?.sales_order_info?.terms_and_conditions,
        selectedTags: selectedOrderForInvoice?.tags,
        cfInvoiceDoc: mergedArrayForDocCfs,
        isAutomaticConversionRate: false,
        currencyConversionRate: selectedOrderForInvoice?.conversion_rate,
        cfInvoiceLine: cfInvoiceLineTemp,
        visibleColumns: visibleColumnsTemp,
      }));
    }
    if (selectedWorkOrder && selectedFg) {
      const payload = {
        moId: moInfo?.mo_id,
        fgTenantProductId: selectedFg,
      };
      getWorkOrderRm(payload, (_workOrderRm) => {
        sessionStorage.setItem('staticReservations', JSON.stringify({}));
        let lineData = [];
        if (_workOrderRm?.length) {
          lineData = _workOrderRm?.map((rmLine) => {
            const lineKey = uuidv4();
            return {
              key: lineKey,
              hsn_code: rmLine?.product_sku_info?.hsn_code,
              batchConsumptionMethod: rmLine?.default_outwards_method || Helpers.getBatchMethod(rmLine),
              product_batches: getBatches(rmLine?.product_batches, Math.abs(Number(rmLine?.remaining_quantity)), rmLine?.product_sku_info?.purchase_uom_info, rmLine?.product_sku_info?.uom_info, null, lineKey, false, true),
              child_taxes: Helpers.computeTaxation((Number(rmLine?.total_quantity_required) * rmLine?.tenant_product_info?.selling_price) * (rmLine?.line_discount_percentage ? Number(100 - rmLine?.line_discount_percentage) / 100 : 1), rmLine?.taxInfo, moInfo?.vendorAddress?.state, user?.tenant_info?.default_billing_address_info?.state)?.tax_info?.child_taxes,
              lineCustomFields: (!!cfV2DocInvoice?.data?.document_line_custom_fields?.length ? CustomFieldHelpers.mergeCustomFields(cfV2DocInvoice?.data?.document_line_custom_fields, rmLine?.product_sku_info?.system_custom_fields) : [])?.map((i) => ({
                ...i,
                fieldValue: i?.fieldName === 'Rate' ? rmLine?.tenant_product_info?.selling_price : (i?.fieldName === 'Quantity' ? Math.min(Number(rmLine?.remaining_quantity), Helpers.getValueTotalInObject(rmLine?.product_batches?.filter((item) => !item?.is_rejected_batch), 'available_qty')) : i?.fieldValue),
              })),
              product_sku_info: rmLine?.product_sku_info,
              tenant_product_info: rmLine?.tenant_product_info,
              product_sku_name: rmLine?.product_sku_info?.product_sku_name,
              remaining_quantity: Number(rmLine?.remaining_quantity),
              quantity: Math.min(rmLine?.remaining_quantity > 0 ? rmLine?.remaining_quantity.toFixed(rmLine?.uom_info?.precision) : 0, Helpers.getValueTotalInObject(rmLine?.product_batches?.filter((item) => !item?.is_rejected_batch), 'available_qty')),
              unitPrice: rmLine?.tenant_product_info?.selling_price,
              taxId: rmLine?.tenant_product_info?.tax_info?.tax_id,
              tax_id: rmLine?.tenant_product_info?.tax_info?.tax_id,
              taxInfo: rmLine?.tenant_product_info?.tax_info,
              discount: 0,
              fg_tenant_product_id: rmLine?.fg_tenant_product_id,
              tenant_product_id: rmLine?.tenant_product_info?.tenant_product_id,
              sku: rmLine?.product_sku_info?.product_sku_id,
              uomId: rmLine?.product_sku_info?.uom_info?.uom_id,
              uomInfo: rmLine?.product_sku_info?.uom_info,
              uom_info: rmLine?.product_sku_info?.uom_info,
              uom_list: rmLine?.product_sku_info?.uom_list,
              expiry_days: rmLine?.product_sku_info?.expiry_days,
              so_quantity: Number(Math.max(rmLine?.remaining_quantity, 0)),
              fg_wise_breakup: rmLine?.fg_wise_breakup?.map((item) => ({ ...item, key: uuidv4(), rmKey: lineKey, usedQty: item?.remaining_quantity, remaining_quantity: item?.remaining_quantity > 0 ? item?.remaining_quantity.toFixed(rmLine?.uom_info?.precision) : 0 })),
              fgWiseBreakup: rmLine?.fg_wise_breakup?.map((item) => ({ ...item, fg_tenant_product_id: item?.fg_tenant_product_id, remaining_quantity: item?.remaining_quantity > 0 ? item?.remaining_quantity.toFixed(rmLine?.uom_info?.precision) : 0, usedQty: item?.remaining_quantity })),
              product_category_info: rmLine?.product_category_info,
              manufacturingDateFormat: rmLine?.product_sku_info?.manufacturing_date_format,
              expiryDateFormat: rmLine?.product_sku_info?.expiryDateFormat,
              lineDiscountType: 'Percent',
            };
          });
        }
        setState((prevState) => ({
          ...prevState,
          data: lineData,
          tenantDepartmentId,
          ...moInfo,
          discountType: 'Percent',
        }));
      });
    } else if (selectedWorkOrder && !selectedFg) {
      notification.open({
        type: 'error',
        duration: '5',
        message: 'Please select atleast one finished goods',
        placement: 'top',
      });
    }
    if (jwRmLines && !!jwRmLines?.input_materials?.length && cfV2DocInvoice) {
      sessionStorage.setItem('staticReservations', JSON.stringify({}));
      let tenantDepartmentIdFromJwInfo = null;
      if (jwRmLines?.output_materials?.length > 0) {
        const MoLineIdForDep = jwRmLines.output_materials[0]?.mo_line_id_for_department ? jwRmLines?.rm_tenant_department_id : null;
        const MoFgIdForDep = jwRmLines.output_materials[0]?.mo_fg_id_for_department ? jwRmLines?.fg_tenant_department_id : null;
        tenantDepartmentIdFromJwInfo = MoLineIdForDep || MoFgIdForDep;
      } else if (jwRmLines?.processing_materials?.length > 0) {
        const MoLineIdForDep = jwRmLines.processing_materials[0]?.mo_line_id_for_department ? jwRmLines?.rm_tenant_department_id : null;
        const MoFgIdForDep = jwRmLines.processing_materials[0]?.mo_fg_id_for_department ? jwRmLines?.fg_tenant_department_id : null;
        tenantDepartmentIdFromJwInfo = MoLineIdForDep || MoFgIdForDep;
      }
      let lineData = jwRmLines?.input_materials?.map((jwRmLine) => {
        const lineKey = uuidv4();
        const lineQty = Math.min(jwRmLine?.required_input_qty > 0 ? jwRmLine?.required_input_qty.toFixed(jwRmLine?.uom_info?.precision) : 0, Helpers.getValueTotalInObject(jwRmLine?.product_batches?.filter((item) => !item?.is_rejected_batch), 'available_qty'));
        return {
          key: lineKey,
          ...jwRmLine,
          product_batches: getBatches(Helpers.batchSelectionMethod(jwRmLine.default_outwards_method || Helpers.getBatchMethod(jwRmLine), jwRmLine?.product_batches, Math.abs(Number(jwRmLine?.required_input_qty))), Math.abs(Number(jwRmLine?.required_input_qty)), jwRmLine?.uom_info, jwRmLine?.uom_info, null, lineKey, false, true),
          quantity: lineQty,
          remaining_quantity: lineQty,
          so_quantity: Number(Math.max(jwRmLine?.required_input_qty, 0)),
          lineCustomFields: (!!cfV2DocInvoice?.data?.document_line_custom_fields?.length ? CustomFieldHelpers.mergeCustomFields(cfV2DocInvoice?.data?.document_line_custom_fields, jwRmLine?.product_sku_info?.system_custom_fields) : [])?.map((i) => ({
            ...i,
            fieldValue: i?.fieldName === 'Rate' ? jwRmLine?.selling_price : (i?.fieldName === 'Quantity' ? lineQty : i?.fieldValue),
          })),
          child_taxes: Helpers.computeTaxation((Number(jwRmLine?.jwRmLine) * jwRmLine?.selling_price) * (jwRmLine?.line_discount_percentage ? Number(100 - jwRmLine?.line_discount_percentage) / 100 : 1), jwRmLine?.taxInfo, moInfo?.vendorAddress?.state, user?.tenant_info?.default_billing_address_info?.state)?.tax_info?.child_taxes,
          jw_wise_breakup: jwRmLine?.jw_wise_breakup?.map((item) => ({ ...item, key: uuidv4(), jwKey: lineKey, usedQty: item?.quantity, remaining_quantity: item?.quantity > 0 ? item?.quantity.toFixed(jwRmLine?.uom_info?.precision) : 0 })),
          jwWiseBreakup: jwRmLine?.jw_wise_breakup?.map((item) => ({ ...item, key: uuidv4(), jwKey: lineKey, usedQty: item?.quantity, remaining_quantity: item?.quantity > 0 ? item?.quantity.toFixed(jwRmLine?.uom_info?.precision) : 0 })),
          mo_line_id: jwRmLine?.mo_line_id,
          mo_fg_id: jwRmLine?.mo_fg_id,
          manufacturingDateFormat: jwRmLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: jwRmLine?.product_sku_info?.expiry_date_format,
          lineDiscountType: 'Percent',
        };
      });
      setState((prevState) => ({
        ...prevState,
        data: lineData,
        tenantDepartmentId: tenantDepartmentIdFromJwInfo,
        selectedTenant: jwRmLines?.tenant_id,
        sellerId: jwRmLines?.subcontractor_seller_id,
        discountType: 'Percent',
      }));
    }
  }, [selectedOrderForInvoice, moRmLines, moInfo, cfV2DocInvoice, selectedFg, jwRmLines]);

  useEffect(() => {
    const departmentLevelStock = user?.tenant_info?.inventory_config?.settings?.department_level_stock;

    // Setting default currency if not selected and no status on order or invoice
    if (CurrenciesResults && CurrenciesResults?.length > 0 && !selectedOrderForInvoice?.status && !selectedInvoice?.status) {
      const defaultCurrency = CurrenciesResults.find((currency) => currency.is_default === true);
      if (defaultCurrency && !state.selectedCurrencyID) {
        setState((prevState) => ({
          ...prevState,
          selectedCurrencyID: defaultCurrency.org_currency_id,
          selectedCurrencyName: defaultCurrency,
          isAutomaticConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate,
          currencyConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate ? defaultCurrency?.automatic_conversion_rate : defaultCurrency?.conversion_rate,
          isUserReady: false,
        }));
      }
    }

    // Custom Fields
    if (cfV2DocInvoice && !isUserReadyForCF && !location?.state?.cloneInv && !match.params.invoiceId && !selectedOrderForInvoice && !selectedInvoice) {
      const lineCFs = CustomFieldHelpers.getCfStructure(cfV2DocInvoice.data.document_line_custom_fields?.filter((item) => item?.is_active), false);
      setState((prevState) => ({
        ...prevState,
        cfInvoiceDoc: CustomFieldHelpers.getCfStructure(cfV2DocInvoice.data.document_custom_fields, true),
        cfInvoiceLine: lineCFs,
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(cfV2DocInvoice.data.document_line_custom_fields, prevState.visibleColumns),
        data: prevState.data.map((item) => ({ ...item, lineCustomFields: lineCFs })),
        isUserReadyForCF: true,
      }));
    }
    // Set tenant department ID
    if (!selectedOrderForInvoice && (!match.params.invoiceId && !location?.state?.cloneInv) && !tenantDepartmentId) {
      setState((prevState) => ({
        ...prevState,
        tenantDepartmentId: departmentLevelStock ? user?.tenant_info?.default_department_for_sales : user?.tenant_info?.default_store_id,
        selectedTenant: user?.tenant_info?.tenant_id,
        billFrom: user?.tenant_info?.default_billing_address_info,
        shipFrom: user?.tenant_info?.default_shipping_address_info,
        selectedTenantTallyIntegrationId: user?.tenant_info?.it_id,
      }));
    }
    // When selectedInvoice is available
    if (selectedInvoice?.invoice_id && (match.params.invoiceId || location?.state?.cloneInv) && !isUserReady && cfV2DocInvoice) {
      sessionStorage.setItem('staticReservations', JSON.stringify({}));
      const selectedCurrentInvoice = selectedInvoice;
      const oldCustomField = selectedCurrentInvoice.custom_fields || [];
      const oldLineCustomField = selectedCurrentInvoice.invoice_lines[0]?.invoice_line_custom_fields || [];
      const documentLineCustomFields = cfV2DocInvoice?.data?.document_line_custom_fields;
      const invoiceLines = selectedCurrentInvoice?.invoice_lines?.map((orderLine) => {
        const parentKey = uuidv4();
        const oldLineCustomField = orderLine?.custom_fields || [];
        const mergedArrayForLinesCfs = CustomFieldHelpers.mergeCustomFields(documentLineCustomFields, oldLineCustomField);
        const pendingQuantity = Math.max(new Decimal(orderLine?.so_quantity || 0).minus(orderLine?.invoice_gen_quantity || 0).toNumber(), 0);
        const discountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(orderLine?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(orderLine?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = orderLine?.is_discount_in_percent ? (orderLine.quantity * orderLine.unit_price) * (1 - discountValue / 100) : Math.max(orderLine.quantity * orderLine.unit_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(orderLine?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
        return {
          key: parentKey,
          mo_line_id: orderLine.mo_line_id,
          asset1: orderLine.product_sku_info?.assets?.[0]?.url || '',
          product_sku_name: orderLine.product_sku_info?.product_sku_name,
          product_category_info: orderLine.product_sku_info?.product_category_info,
          product_sku_id: orderLine.product_sku_info?.product_sku_id,
          hsn_code: orderLine.hsn_code,
          quantity: Number(orderLine.quantity),
          pending_quantity_so: pendingQuantity,
          unitPrice: Number(orderLine.unit_price) || 0,
          offerPrice: Number(orderLine.offer_price) || 0,
          uomId: Number(orderLine.uom_id),
          uomGroup: Number(orderLine.group_id),
          taxId: Number(orderLine.tax_id),
          taxInfo: orderLine.tax_group_info,
          child_taxes: Helpers.computeTaxation(taxableValue, orderLine?.tax_group_info, user?.tenant_info?.state, selectedCurrentInvoice.billing_address_info?.state)?.tax_info?.child_taxes,
          discount: discountValue,
          unitDiscount: unitDiscountValue,
          lineDiscountType: orderLine?.is_discount_in_percent ? 'Percent' : 'Amount',
          showUnitDiscount: !orderLine?.is_discount_in_percent && unitDiscountValue > 0,
          order_line_id: orderLine.order_line_id,
          delivery_challan_line_id: orderLine.delivery_challan_line_id,
          so_quantity: Number(orderLine.so_quantity || 1),
          so_free_quantity: orderLine.free_quantity || 0,
          free_quantity: orderLine.free_quantity || 0,
          showFreebie: Number(orderLine.free_quantity) > 0,
          tenant_product_id: orderLine.tenant_product_id,
          uom_info: orderLine.uom_info?.[0],
          uomInfo: orderLine.uom_info?.[0],
          uom_list: orderLine.product_sku_info?.uom_list,
          expiry_days: orderLine.product_sku_info?.expiry_days,
          batchConsumptionMethod: orderLine.default_outwards_method || Helpers.getBatchMethod(orderLine),
          product_batches: getBatches(
            Helpers.batchSelectionMethod(orderLine.default_outwards_method || Helpers.getBatchMethod(orderLine), orderLine.all_product_batches, (Number(orderLine.quantity) + Number(orderLine.free_quantity))),
            (Number(orderLine.quantity) + Number(orderLine.free_quantity)),
            orderLine.uom_info?.[0],
            orderLine.product_sku_info?.uom_info,
            null,
            parentKey,
            false,
            true,
          ),
          product_sku_info: orderLine.product_sku_info,
          remarks: orderLine.remarks,
          remarkRequired: !!orderLine?.remarks?.replace(/<[^>]+>/g, ''),
          sales_account_name: orderLine.tally_sales_account_name,
          lineCustomFields: mergedArrayForLinesCfs || [],
          bundle_products: orderLine.bundle_products?.map((item) => {
            const lineKey = uuidv4();
            return {
              ...item,
              parentKey,
              key: lineKey,
              unitPrice: Number(item.unit_price) || 0,
              uomId: Number(item.uom_id),
              uomGroup: Number(item.group_id),
              uom_info: item.uom_info?.[0],
              taxId: Number(item.tax_group_info?.tax_id),
              taxInfo: item.tax_group_info,
              child_taxes: Helpers.computeTaxation(
                (item.quantity * item.unit_price) * (orderLine.line_discount_percentage ? Number(100 - orderLine.line_discount_percentage) / 100 : 1),
                item.tax_group_info,
                user.tenant_info.state,
                selectedCurrentInvoice.billing_address_info.state,
              )?.tax_info?.child_taxes,
              product_sku_name: item.product_sku_info?.product_sku_name,
              so_quantity: Number(item.so_quantity || item.bundle_quantity),
              expiry_days: item.product_sku_info?.expiry_days,
              product_sku_info: item.product_sku_info,
              product_batches: getBatches(
                Helpers.batchSelectionMethod(item.default_outwards_method || Helpers.getBatchMethod(item), item.all_product_batches, (item.quantity + Number(item.free_quantity))),
                (item.quantity + Number(item.free_quantity)),
                item.uom_info?.[0],
                item.product_sku_info?.uom_info,
                null,
                lineKey,
                false,
                true
              ),
            };
          }),
          manufacturingDateFormat: orderLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: orderLine?.product_sku_info?.expiry_date_format,
        };
      });

      const mergedArrayForLinesCfs = CustomFieldHelpers.mergeCustomFields(cfV2DocInvoice?.data?.document_line_custom_fields, oldLineCustomField);
      const mergedArrayForDocCfs = CustomFieldHelpers.mergeCustomFields(cfV2DocInvoice?.data?.document_custom_fields, oldCustomField);

      // for updating pricesToApply state
      getApplyPriceLists(
        selectedCurrentInvoice?.tenant_id || user?.tenant_info?.tenant_id,
        selectedCurrentInvoice?.price_list_id,
        selectedCurrentInvoice.customer_id || selectedCurrentInvoice.dc_customer_id || selectedCurrentInvoice.adhoc_dc_customer_id,
        selectedCurrentInvoice.shipping_address_info?.state,
        selectedCurrentInvoice.invoice_lines.map((invoiceLine) => invoiceLine.product_sku_info?.product_sku_id) || [],
        (toApplyPriceList) => {
          updateState('pricesToApply', toApplyPriceList);
        },
      );
      setState((prevState) => ({
        ...prevState,
        isUserReady: true,
        billFrom: selectedCurrentInvoice.bill_from_address_info,
        shipFrom: selectedCurrentInvoice.ship_from_address_info,
        productSkuIds: selectedCurrentInvoice.invoice_lines.map((invoiceLine) => invoiceLine.product_sku_info?.product_sku_id) || [],
        selectedPriceList: selectedCurrentInvoice.price_list_id,
        selectedCustomer: selectedCurrentInvoice.customer_id || selectedCurrentInvoice.dc_customer_id || selectedCurrentInvoice.adhoc_dc_customer_id,
        selectedCustomerInfo: selectedCurrentInvoice.customer_info,
        accountManager: selectedCurrentInvoice.account_manager_id,
        gstNumber: selectedCurrentInvoice.customer_info?.gst_number,
        billingAddress: selectedCurrentInvoice.billing_address_info,
        shippingAddress: selectedCurrentInvoice.shipping_address_info,
        data: invoiceLines,
        tally_sales_account_name: selectedCurrentInvoice.tally_sales_account_name,
        paymentTerms: Number(selectedCurrentInvoice.payment_terms?.[0]?.due_days) ? Number(selectedCurrentInvoice.payment_terms[0].due_days) : 0,
        paymentRemark: selectedCurrentInvoice?.payment_terms?.[0]?.remark || '',
        toRecipients: selectedCurrentInvoice?.notification_recipients || [],
        checkedRecipients: selectedCurrentInvoice?.is_automatic_notification_enabled,
        termsAndConditions: selectedCurrentInvoice.terms_and_conditions,
        tncId: selectedCurrentInvoice?.tc_id || '',
        invoiceDate: location?.state?.cloneInv ? dayjs() : dayjs(selectedCurrentInvoice.invoice_date),
        invoiceNumber: selectedCurrentInvoice.invoice_number,
        fileList: selectedCurrentInvoice.attachments,
        selectedCurrentInvoice,
        salesAccount: selectedCurrentInvoice.tally_sales_account_name,
        charge1Name: selectedCurrentInvoice.charge_1_name || 'Freight',
        charge1Value: selectedCurrentInvoice.charge_1_value,
        createExpenseCheckbox: selectedCurrentInvoice.transporter_id || selectedCurrentInvoice.vehicle_number,
        transporterId: selectedCurrentInvoice.transporter_id,
        vehicleNumber: selectedCurrentInvoice.vehicle_number,
        transporterBillNumber: selectedCurrentInvoice.transporter_bill_number,
        freightTaxId: selectedCurrentInvoice.freight_tax_id || 'Not Applicable',
        freightTax: selectedCurrentInvoice.freight_tax_info?.tax_value,
        freightTaxInfo: selectedCurrentInvoice.freight_tax_info,
        freightTaxData: {
          ...selectedCurrentInvoice?.freight_tax_info,
          child_taxes: Helpers.computeTaxation(selectedCurrentInvoice?.charge_1_value, selectedCurrentInvoice?.freight_tax_info, user?.tenant_info?.state, selectedCurrentInvoice?.billing_address_info?.state)?.tax_info?.child_taxes,
        },
        freightSacCode: selectedCurrentInvoice.freight_sac_code,
        cfInvoiceDoc: mergedArrayForDocCfs || [],
        cfInvoiceLine: CustomFieldHelpers.getCfStructure(cfV2DocInvoice?.data?.document_line_custom_fields, false) || [],
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(CustomFieldHelpers.postCfStructure(mergedArrayForLinesCfs), prevState.visibleColumns),
        tenantDepartmentId: selectedCurrentInvoice.tenant_department_info?.tenant_department_id || selectedCurrentInvoice.tenant_department_id || user?.tenant_info?.default_department_for_sales,
        selectedTenant: selectedCurrentInvoice?.tenant_id,
        discountPercentage: selectedCurrentInvoice?.is_discount_in_percent ? selectedCurrentInvoice?.discount_percentage?.toFixed(DEFAULT_CUR_ROUND_OFF) : selectedCurrentInvoice?.discount_amount?.toFixed(DEFAULT_CUR_ROUND_OFF),
        isLineWiseDiscount: selectedCurrentInvoice.is_line_wise_discount,
        discountType: selectedCurrentInvoice?.is_discount_in_percent ? 'Percent' : 'Amount',
        chargeData: selectedCurrentInvoice.other_charges?.map((item) => ({
          ...item,
          chargeKey: uuidv4(),
          chargesTaxId: item?.tax_info?.tax_id,
          chargesSacCode: item?.charge_sac_code,
          chargesTaxInfo: item?.tax_info,
          chargesTax: item?.tax_info?.tax_value,
          tallyLedgerName: item?.ledger_name || null,
          chargesTaxData: {
            ...item?.tax_info,
            child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, selectedCurrentInvoice.billing_address_info.state)?.tax_info?.child_taxes,
          },
        })) || [],
        taxType: selectedCurrentInvoice.tcs_id || selectedCurrentInvoice.tds_id,
        taxTypeId: selectedCurrentInvoice.tcs_info ? selectedCurrentInvoice.tcs_info?.tax_id : selectedCurrentInvoice.tds_info?.tax_id,
        taxTypeInfo: selectedCurrentInvoice.tcs_info || selectedCurrentInvoice.tds_info,
        selectedCurrencyID: selectedCurrentInvoice.org_currency_info?.org_currency_id,
        selectedCurrencyName: selectedCurrentInvoice.org_currency_info,
        taxTypeName: selectedCurrentInvoice.tcs_info ? selectedCurrentInvoice.tcs_info?.tax_type_name : selectedCurrentInvoice.tds_info?.tax_type_name,
        sendWhatsappNotification: selectedCurrentInvoice.is_so_automatic_notification_enabled,
        dontDeductStock: selectedCurrentInvoice.bypass_inventory_change,
        selectedTags: selectedCurrentInvoice.tags,
        isAutomaticConversionRate: false,
        currencyConversionRate: selectedCurrentInvoice?.conversion_rate,
        narration: selectedCurrentInvoice?.narration || '',
        selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedCurrentInvoice?.tenant_id)?.it_id,
      }));
    }
  }, [CurrenciesResults, cfV2DocInvoice, selectedOrderForInvoice, selectedInvoice, user, match, location, selectedCurrencyID, isUserReadyForCF, isUserReady]);

  const handleChangeTextArea = (html) => {
    updateState('termsAndConditions', html);
  };

  const handleDelete = (key, record) => {
    handleDeleteHelper(state, props, key, (newState) => updateState('data', newState?.data), record);
  };

  const handleDeleteCharge = (chargeKey) => {
    const copyChargeData = chargeData.filter((item) => item.chargeKey !== chargeKey);
    updateState('chargeData', copyChargeData);
  };

  const handleProductChange = (tenantSku, key, productData) => {
    handleProductChangeHelper(state, props, tenantSku, key, productData, (newState) => {
      updateState('data', newState?.data);
      updateState('productSkuIds', newState?.productSkuIds);
      if (newState?.pricesToApply) {
        updateState('pricesToApply', newState?.pricesToApply);
      }
    });
  };

  const handleProductChangeValue = (value, key) => {
    handleProductChangeValueHelper(state, props, value, key, (newState) => updateState('data', newState?.data));
  };

  const handleMultiProductChange = (tenantSku, key, productData, callback) => {
    handleMultiProductChangeHelper(state, props, tenantSku, key, productData, (newState) => {
      if (callback) callback();

      setState((prevState) => ({
        ...prevState,
        data: [...prevState.data, ...newState.data],
        productSkuIds: [...prevState.productSkuIds, ...newState.productSkuIds],
        pricesToApply: newState?.pricesToApply ? [...prevState.pricesToApply, ...newState.pricesToApply] : [...prevState.pricesToApply],
      }));
    });
  };

  function addNewRow(callback) {
    addNewRowHelper(state, props, callback, (newState) => {
      updateState('data', newState?.data);
    });
  }

  function addNewChargesRow() {
    addNewChargeRowHelper(state, props, (newState) => updateState('chargeData', newState?.chargeData));
  }

  function createInvoice(withApproval) {
    createInvoiceHelper(state, props, withApproval, (newState) => {
      updateState('formSubmitted', newState?.formSubmitted);
    });
  }

  function customInputChange(fieldValue, cfId) {
    customInputChangeHelper(state, props, fieldValue, cfId, (newState) => updateState('cfInvoiceDoc', newState?.cfInvoiceDoc));
  }

  function customLineInputChange(fieldValue, key, fgId, lineProps, cBatchId, cBatchQty, rate) {
    customLineInputChangeHelper(state, props, fieldValue, key, lineProps, (newState) => updateState('data', newState?.data), cBatchId, cBatchQty, rate);
  }

  function toggleBatch(record, adjustmentRow) {
    toggleBatchHelper(state, props, record, adjustmentRow, (newState) => updateState('data', newState?.data));
  }

  function toggleBatchInner(record, adjustmentRow) {
    toggleBatchInnerHelper(state, props, record, adjustmentRow, (newState) => updateState('data', newState?.data));
  }

  const showNewCustomerInSelect = (value) => {
    setState((prevState) => ({
      ...prevState,
      selectedCustomer: value?.customer_id,
      billingAddress: value?.customer_info?.billing_address_details,
      shippingAddress: value?.customer_info?.shipping_address_details,
      gstNumber: value?.customer_info?.gst_number,
      selectedCustomerInfo: value?.customer_info,
    }));
  };

  const handleTaxTypeChange = (value) => {
    setState((prevState) => ({
      ...prevState,
      taxTypeId: value?.tax_id,
      taxTypeInfo: value,
      taxTypeName: value?.tax_type_name,
      taxType: value?.tax_type,
    }));
  };

  const handleCustomerChange = (value) => {
    const copyData = data?.map((record) => ({
      ...record,
      child_taxes: Helpers.computeTaxation((record?.quantity * record?.unitPrice), record.taxInfo, value?.customer_info?.billing_address_details?.state, user?.tenant_info?.state)?.tax_info?.child_taxes,
    }));

    const docCf = CustomFieldHelpers.postCfStructure(cfInvoiceDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
    const customerCf = value?.custom_field_values?.filter((item) => (item?.is_active)) || [];
    const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, customerCf) || [];

    setState((prevState) => ({
      ...prevState,
      selectedCustomer: value?.customer_id,
      billingAddress: value?.customer_info?.billing_address_details,
      shippingAddress: value?.customer_info?.shipping_address_details,
      gstNumber: value?.customer_info?.gst_number,
      toRecipients: value?.customer_info?.email_address ? [value?.customer_info?.email_address] : [],
      selectedCustomerInfo: value?.customer_info,
      accountManager: value?.customer_info?.account_manager_info?.account_manager_id,
      data: copyData,
      selectedCurrencyID: value?.org_currency_id,
      selectedCurrencyName: value?.currency_info,
      currencyConversionRate: isAutomaticConversionRate ? value?.currency_info?.automatic_conversion_rate : value?.currency_info?.conversion_rate,
      selectedPriceList: null,
      paymentTerms: value?.customer_info?.default_payment_terms?.due_days || 0,
      paymentRemark: value?.customer_info?.default_payment_terms?.remark || '',
      cfInvoiceDoc: mergedCf,
    }));
  };

  const handleFreightTaxData = (value) => {
    setState((prevState) => ({
      ...prevState,
      freightTaxId: !value ? 'Not Applicable' : value?.tax_id,
      freightTax: !value ? null : value?.tax_value,
      freightTaxInfo: value || null,
      freightTaxData: !value ? {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      } : {
        ...value,
        child_taxes: Helpers.computeTaxation(charge1Value, value, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes
      },
    }));
  };

  const splitChargesData = (charge) => {
    const chargeWithTaxName = charge?.filter((line) => line?.chargesTaxInfo?.tax_id) || [];
    const chargeWithoutTaxName = charge?.filter((line) => !line?.chargesTaxInfo?.tax_id) || [];
    return { chargeWithTaxName, chargeWithoutTaxName };
  };

  const renderCharges = (charge) => charge?.map((line) => (
    <div
      key={line?.chargeKey}
      className="form-calculator__field"
      style={{ marginBottom: '5px' }}
    >
      <div className="form-calculator__field-name">
        {!user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings?.use_custom_charges ? (
          <div className="select_extra_charge_wrapper">
            <SelectExtraCharge
              containerClassName="orgInputContainer"
              selectedChargeName={line.charge_name}
              disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || !invoice}
              onChange={(value) => {
                const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                copyChargeData.map((item) => {
                  if (item.chargeKey === line.chargeKey) {
                    // eslint-disable-next-line no-param-reassign
                    item.charge_name = value?.ledger_name;
                    item.chargesSacCode = (value?.charge_sac_code) || null;
                  }
                });
                updateState('chargeData', copyChargeData);
              }}
              customStyle={{
                width: '220px',
                backgroundColor: 'white',
              }}
              excludeCharges={chargeData?.map((item) => item?.charge_name)}
              entityName="SALES"
            />
            {line?.chargesTax && (
              <div style={{
                color: '#2d7df7',
                fontWeight: '400',
                fontSize: '12px',
              }}
              >
                {`tax@${line?.chargesTax}%`}
              </div>
            )}
          </div>
        ) : (
          <H3FormInput
            value={line?.charge_name}
            type="text"
            containerClassName={(formSubmitted && Number(line?.charge_name) <= 0) ? 'form-error__input' : ''}
            labelClassName="orgFormLabel"
            inputClassName="orgFormInput"
            onChange={(e) => {
              const copyChargeData = JSON.parse(JSON.stringify(chargeData));
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  item.charge_name = e.target.value;
                }
                return data;
              });
              updateState('chargeData', copyChargeData);
            }}
            placeholder="Charge name.."
          />
        )}

      </div>
      <div className="form-calculator__field-value flex-display" style={{ display: 'flex', gap: '0px' }}>
        <div style={{ width: '114px' }}>
          <H3FormInput
            value={line?.charge_amount}
            type="number"
            containerClassName={(formSubmitted && !line?.charge_amount) ? 'form-error__input' : ''}
            labelClassName="orgFormLabel"
            inputClassName="orgFormInput"
            onChange={(e) => {
              const copyChargeData = JSON.parse(
                JSON.stringify(chargeData),
              );
              copyChargeData.map((item) => {
                if (item.chargeKey === line.chargeKey) {
                  const updatedChargeAmount = Number.parseFloat(e.target.value) || 0;

                  // Update charge_amount
                  item.charge_amount = updatedChargeAmount;
                  // Compute chargesTaxData with updated values
                  const computedTaxData = Helpers.computeTaxation(
                    updatedChargeAmount,
                    line.chargesTaxInfo,
                    user?.tenant_info?.state,
                    billingAddress?.state
                  );
                  // Update chargesTaxData
                  item.chargesTaxData = {
                    ...line?.chargesTaxInfo,
                    child_taxes: computedTaxData?.tax_info?.child_taxes || [],
                  };
                }
                return data;
              });
              updateState('chargeData', copyChargeData);
            }}
            allowNegative
          />
        </div>
        <ChargesTaxInput
          selectedTenant={selectedTenant}
          selectedChargeName={line.charge_name}
          openChargesTax={line?.openChargesTax}
          setOpenChargesTax={(value) => {
            const copyChargeData = JSON.parse(
              JSON.stringify(chargeData),
            );
            copyChargeData.map((item) => {
              if (item.chargeKey === line.chargeKey) {
                // eslint-disable-next-line no-param-reassign
                item.openChargesTax = value;
              }
              return data;
            });
            updateState('chargeData', copyChargeData);
          }}
          sacCode={line?.chargesSacCode}
          setSacCode={(value) => {
            const copyChargeData = JSON.parse(
              JSON.stringify(chargeData),
            );
            copyChargeData.map((item) => {
              if (item.chargeKey === line.chargeKey) {
                // eslint-disable-next-line no-param-reassign
                item.chargesSacCode = value;
              }
              return data;
            });
            updateState('chargeData', copyChargeData);
          }}
          chargesTaxId={line?.chargesTaxId}
          setChargesTaxData={(value) => {
            const updatedChargeData = chargeData?.map((item) => {
              if (item.chargeKey === line.chargeKey) {
                return {
                  ...item,
                  chargesTaxId: !value ? 'Not Applicable' : value?.tax_id,
                  chargesTax: !value ? null : value?.tax_value,
                  chargesTaxInfo: value || null,
                  chargesTaxData: !value ? {
                    child_taxes: [
                      {
                        tax_amount: 0,
                        tax_type_name: '',
                      },
                    ],
                  } : {
                    ...value,
                    child_taxes: Helpers.computeTaxation(line?.charge_amount, value, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes,
                  },
                };
              }
              return item;
            });
            updateState('chargeData', updatedChargeData);
          }}
          tallyLedgerName={line?.tallyLedgerName}
          setTallyLedgerName={(value) => {
            const updatedChargeData = chargeData?.map((item) => {
              if (item.chargeKey === line.chargeKey) {
                item.tallyLedgerName = value;
              }
              return data;
            });
            updateState((prevState) => ({
              ...prevState,
              chargeData: updatedChargeData,
            }));
          }}
          showTallyLedgerSales
        />
        <div
          className="form-calculator__delete-line-button"
          onClick={() => handleDeleteCharge(line?.chargeKey)}
        >
          <FontAwesomeIcon
            icon={faCircleXmark}
            size="sm"
            style={{ color: '#6f7276' }}
          />
        </div>
      </div>
    </div>
  ));

  const getTotals = () => getLineTotals(data, chargeData, taxTypeInfo, taxTypeName, charge1Value, freightTax, freightTaxData);

  const CloneInv = !!location?.state?.cloneInv;
  const selectedTenantIdInv = location?.state?.selectedTenantIdInv;

  const barCoding = user?.tenant_info?.global_config?.sub_modules?.barcoding?.is_active;
  const invoice = user?.tenant_info?.sales_config?.sub_modules?.invoice?.is_active;

  const disablePriceChange = user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.disable_price_change_in_invoice_from_so;
  const departmentLevelStock = user?.tenant_info?.inventory_config?.settings?.department_level_stock;
  const accountingGSTTransactionAsPerMaster = user?.tenant_info?.sales_config?.sub_modules?.customer?.settings?.gst_details_in_transaction === 'AS_PER_MASTER';
  const isPriceListMandatory = user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.is_price_list_mandatory;

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;
  const { docLevelError, lineLevelError } = InvoiceErrorList({ cfInvoiceDoc, invoiceNumber, invoiceDate, selectedOrderForInvoice, selectedCustomer, billingAddress, shippingAddress, shipFrom, billFrom, checkedRecipients, toRecipients, data, cfInvoiceLine, chargeData, moInfo, selectedWorkOrder, selectedPriceList, isPriceListMandatory, jwRmLines, user, selectedInvoice, gstNumber, });
  const salesAccountList = user?.user_tenants?.find((item) => item?.tenant_id === selectedTenant)?.tally_configuration?.sales_account_list;
  const salesAccountOptions = salesAccountList?.map((item) => ({
    label: item.sales_account_name,
    value: item.sales_account_name,
  }));
  const amountTypeOptions = [
    {
      label: `${selectedCurrencyName?.currency_symbol} `,
      value: 'Amount',
    },
    {
      label: '%',
      value: 'Percent',
    },
  ];
  const userMenuConfig = JSON.parse(localStorage.getItem('user_menu_config'));
  return (
    <Fragment>
      <Drawer
        open={showNewCustomerModal}
        onClose={() => updateState('showNewCustomerModal', false)}
        width="720px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '680px' }}>
            <H3Text
              text="Add New Customer"
              className="custom-drawer__title"
            />
            <H3Image
              src={cdnUrl("icon-close-blue.png", "icons")}
              className="custom-drawer__close-icon"
              onClick={() => updateState('showNewCustomerModal', false)}
            />
          </div>
        </div>
        <CustomerForm
          callback={(createdCustomer) => {
            getCustomers(
              '',
              user?.tenant_info?.tenant_id,
              1,
              1000,
              '',
              (newCustomers) => {
                showNewCustomerInSelect(
                  newCustomers?.customers.filter(
                    (customer) => customer?.customer_id === createdCustomer.customer_id,
                  )[0],
                );
                updateState('showNewCustomerModal', false);
              },
            );
          }}
        />
      </Drawer>
      <Drawer
        open={showAddressDrawer}
        onClose={() => {
          updateState('showAddressDrawer', false);
          updateState('selectedAddressType', '');
        }}
        width="360"
        destroyOnClose
      >
        <AddressSelector
          title={
            selectedAddressType === 'TENANT_SHIP_FROM'
              ? 'Ship From'
              : (selectedAddressType === 'TENANT_SHIPPING'
                ? 'Shipping Address'
                : 'Billing Address')
          }
          addressType={selectedAddressType?.split('_')[0]}
          selectedAddressId={
            selectedAddressType === 'TENANT_SHIP_FROM'
              ? shipFrom?.address_id
              : (selectedAddressType === 'TENANT_SHIPPING'
                ? shippingAddress?.address_id
                : billingAddress?.address_id)
          }
          onAddressChange={(address) => {
            if (selectedAddressType === 'TENANT_SHIP_FROM') {
              updateState('shipFrom', address);
              updateState('showAddressDrawer', false);
            }
            if (selectedAddressType === 'TENANT_SHIPPING') {
              updateState('shippingAddress', address);
              updateState('showAddressDrawer', false);
            }
            if (selectedAddressType === 'TENANT_BILLING') {
              const copyData = data?.map((record) => ({
                ...record,
                child_taxes: Helpers.computeTaxation((record?.quantity * record?.unitPrice), record.taxInfo, address?.state, user?.tenant_info?.state)?.tax_info?.child_taxes,
              }));
              updateState('billingAddress', address);
              updateState('showAddressDrawer', false);
              updateState('data', copyData);
            }
          }}
          entityId={selectedAddressType === 'TENANT_SHIP_FROM' ? selectedTenant : selectedCustomer}
          entityType={selectedAddressType === 'TENANT_SHIP_FROM' ? 'TENANT' : 'CUSTOMER'}
          tenantId={selectedTenant || selectedTenantIdInv || selectedInvoice?.tenant_id || selectedOrderForInvoice?.tenant_id || user?.tenant_info?.tenant_id}
        />
      </Drawer>
      {getInvoiceByIdLoading ? (
        <FormLoadingSkull />
      )
        : (
          <div
            className={match.url.includes('/sales-order') ? 'form__wrapper form-component' : 'form__wrapper form-page'}
            style={{ paddingTop: (moRmLines || selectedOrderForInvoice || PaddingTop) ? '0px' : '90px' }}
          >
            {(formSubmitted && (!!docLevelError?.length || !!lineLevelError?.length)) && (
              <ErrorHandle message='Mandatory fields required' docLevelErrors={docLevelError} lineLevelErrors={lineLevelError} />
            )}
            {!jwRmLines && !moRmLines && (
              <div className="ant-row">
                <div className="ant-col-md-24">
                  <div className="form__section">
                    <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                      <H3Text text="PART A" className="form__section-title" />
                      <div className="form__section-line" />
                      <div style={{ display: 'flex', justifyContent: 'end', alignItems: 'center' }}>
                        <CurrencyConversionV2
                          selectedCurrencyName={selectedCurrencyName}
                          selectedCurrencyID={selectedCurrencyID}
                          isAutomaticConversionRate={isAutomaticConversionRate}
                          currencyConversionRate={currencyConversionRate}
                          setCurrencyConversionRate={(val) => updateState('currencyConversionRate', val)}
                          setIsAutomaticConversionRate={(val) => updateState('isAutomaticConversionRate', val)}
                          setSelectedCurrencyName={(val) => updateState('selectedCurrencyName', val)}
                          setSelectedCurrencyID={(val) => updateState('selectedCurrencyID', val)}
                        />
                        <CustomDocumentInputs
                          customFields={cfInvoiceDoc}
                          updateCustomFields={(cf) => updateState('cfInvoiceDoc', cf)}
                        />
                      </div>
                    </div>
                    <div className="form__section-inputs mg-bottom-20">
                      <div className="ant-row">
                        <DocumentNumberSeqInput
                          valueFromProps={invoiceNumber}
                          updateCase={match.params?.invoiceId}
                          setInitialDocSeqNumber={(value) => updateState('initialInvNumber', value)}
                          entityName="INVOICE"
                          docSeqId={docSeqId}
                          tenantId={selectedTenant || selectedTenantIdInv}
                          onChangeFromProps={(event, newValue, seqId) => {
                            updateState('invoiceNumber', newValue ? (newValue || '') : (event?.target?.value || ''));
                            updateState('docSeqId', seqId);
                          }}
                          docTitle="Invoice #"
                          formSubmitted={formSubmitted}
                        />
                        <div className="ant-col-md-6">
                          <div className="form__input-row" style={{ marginBottom: '10px' }}>
                            <H3Text
                              required
                              text="Select Location"
                              className="form__input-row__label"
                            />

                            <div className="form__input-row__input">
                              <TenantSelector
                                hideTitle
                                selectedTenant={selectedTenant}
                                showSearch={false}
                                onChange={(value) => {
                                  const defaultDepForSales = user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_department_for_sales;
                                  const defaultDepId = user?.user_tenants?.find((item) => item?.tenant_id === value)?.tenant_department_info?.tenant_department_id;
                                  const currentTenantInfo = user?.user_tenants?.find((item) => item?.tenant_id === value);
                                  updateState('selectedTenant', value);
                                  updateState('selectedTenantTallyIntegrationId', user?.user_tenants?.find((item) => item?.tenant_id === value)?.it_id);
                                  updateState('tenantDepartmentId', departmentLevelStock ? defaultDepForSales : defaultDepId);
                                  updateState('currentSelectedTenantInfo', currentTenantInfo);
                                  updateState('selectedCustomer', null);
                                  updateState('selectedCustomerInfo', null);
                                  updateState('selectedPriceList', null);
                                  updateState('gstNumber', null);
                                  updateState('fileList', []);
                                  updateState('chargeData', []);
                                  updateState('salesAccount', null);
                                  updateState('billFrom', currentTenantInfo?.default_billing_address_info);
                                  updateState('shipFrom', currentTenantInfo?.default_shipping_address_info);
                                  updateState('tncId', null);
                                  updateState('data', [
                                    {
                                      key: uuidv4(),
                                      asset1: '',
                                      product_sku_name: '',
                                      quantity: '',
                                      unitPrice: '',
                                      lot: 0,
                                      taxId: '',
                                      discount: 0,
                                      child_taxes: [{
                                        tax_amount: 0,
                                        tax_type_name: '',
                                      }],
                                      lineCustomFields: [],
                                    },
                                  ]);
                                }}
                                placeholder="Select Business Unit"
                                customStyle={{
                                  border: '1px solid rgba(68, 130, 218, 0.25)',
                                  borderRadius: '4px',
                                  height: '28px',
                                  padding: '0px',
                                }}
                                labelClassName="form__input-row__label"
                                inputClassName="orgFormInput input"
                                includedTenants={Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.INVOICE, Helpers.permissionTypes.CREATE)}
                                loading={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                                disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || selectedOrderForInvoice || (selectedInvoice && !CloneInv)}
                              />
                            </div>
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row" style={{ marginBottom: '10px' }}>
                            <H3Text
                              required
                              text="Department"
                              className="form__input-row__label"
                            />
                            {!selectedOrderForInvoice?.sales_order_lines ? (
                              <div className="form__input-row__input">
                                <SelectDepartment
                                  hideTitle
                                  selectedDepartment={tenantDepartmentId}
                                  onChange={(value) => {
                                    updateState('tenantDepartmentId', value?.tenant_department_id);
                                    updateState('data', [
                                      {
                                        key: uuidv4(),
                                        asset1: '',
                                        product_sku_name: '',
                                        quantity: '',
                                        unitPrice: '',
                                        lot: 0,
                                        taxId: '',
                                        discount: 0,
                                        child_taxes: [{
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        }],
                                        lineCustomFields: [],
                                      },
                                    ]);
                                  }}
                                  emptyNotAllowed
                                  customStyle={{
                                    border: '1px solid rgba(68, 130, 218, 0.25)',
                                    borderRadius: '4px',
                                    height: '28px',
                                    padding: '0px 3px',
                                  }}
                                  loading={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                                  disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || (selectedInvoice && !CloneInv)}
                                  labelClassName="form__input-row__label"
                                  inputClassName="orgFormInput input"
                                  tenentLevelDepartment
                                  tenantId={selectedTenant || selectedTenantIdInv}
                                />
                              </div>
                            ) : (
                              <H3Text
                                text={`\u00A0\u00A0${selectedOrderForInvoice?.tenant_department_info?.alias_name}`}
                                className="form__input-row__text"
                              />
                            )}
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              required
                              text="Invoice Date"
                              className="form__input-row__label"
                            />
                            <div className={`orgInputContainer form__input-row__input ${(formSubmitted && !invoiceDate) ? 'form__input-row__input-error' : ''}`}>
                              <DatePicker
                                value={invoiceDate}
                                onChange={(value) => {
                                  updateState('invoiceDate', value);
                                }}
                                style={{
                                  border: '1px solid rgba(68, 130, 218, 0.2)',
                                  borderRadius: '2px',
                                  height: '28px',
                                  padding: '1px 3px',
                                  width: '100%',
                                  background: 'white',
                                  marginBottom: formSubmitted && !invoiceDate ? '0px' : '10px',
                                }}
                                disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                                format="DD/MM/YYYY"
                              />
                            </div>
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row " style={{ marginBottom: '10px' }}>
                            <div className="form__input-row__label">
                              Labels
                            </div>
                            <TagSelector
                              hideTitle
                              entityType="INVOICE"
                              selectedTags={selectedTags}
                              isMultiple
                              showSearch
                              onChange={(value) => {
                                updateState('selectedTags', value);
                              }}
                              maxTagCount="responsive"
                              placeholder="Select Tags"
                              isForm
                              containerWrapper="form__input-row__input"
                              disabled={getTallyConnectionsLoading || createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                            />
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          {!selectedOrderForInvoice ? (
                            <Fragment>
                              <SelectCustomer
                                showAddCustomer={showAddCustomer}
                                selectedCustomer={selectedCustomer}
                                addCustomer={() => updateState('showNewCustomerModal', true)}
                                onChange={handleCustomerChange}
                                containerClass="form__input-row"
                                inputClassName={`form-seller__selector form__input-row__input ${(formSubmitted && !selectedCustomer) ? 'form__input-row__select-error' : ''}`}
                                labelClassName="form__input-row__label"
                                tenantId={selectedOrderForInvoice?.tenant_id || selectedCurrentInvoice?.tenant_id || tenantId || user?.tenant_info?.tenant_id}
                                disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || selectedOrderForInvoice || selectedInvoice?.order_id}
                              />
                            </Fragment>
                          )
                            : (
                              <div
                                className="form__input-row"
                                style={{ marginBottom: '10px' }}
                              >
                                <H3Text
                                  text="Customer"
                                  className="form__input-row__label"
                                />
                                <H3Text
                                  text={selectedOrderForInvoice?.customer_name || selectedOrderForInvoice?.customer_info?.customer_name || selectedOrderForInvoice?.adhoc_dc_customer_info?.customer_name}
                                  className="form__input-row__text"
                                />
                              </div>
                            )}
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="GST Number" className="form__input-row__label" />
                            <H3FormInput
                              name="a valid GST Number"
                              type="text"
                              containerClassName={`orgInputContainer form__input-row__input ${(formSubmitted && gstNumber ? String(gstNumber)?.length !== 15 : false) ? 'form__input-row__input-error' : ''}`}
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput input"
                              placeholder=""
                              onChange={(e) => updateState('gstNumber', e.target.value?.trim())}
                              value={gstNumber}
                              disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || accountingGSTTransactionAsPerMaster}
                            />
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          <SelectPaymentTerm
                            selectedPaymentTerm={paymentTerms}
                            onChange={(value) => {
                              updateState('paymentTerms', value?.due_days);
                            }}
                            callback={(value) => {
                              updateState('paymentTerms', value?.due_days);
                            }}
                            containerClassName="form__input-row"
                            inputClassName="orgInputContainer form__input-row__input"
                            labelClassName="form__input-row__label"
                            showError={formSubmitted && !paymentTerms}
                            disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                            showAddPaymentTerm
                            placeholder="Select Payment Term"
                          />
                        </div>
                        <div className="ant-col-md-6">
                          <SelectPaymentRemark
                            selectedPaymentRemark={paymentRemark}
                            onChange={(value) => {
                              updateState('paymentRemark', value?.message);
                            }}
                            callback={(value) => {
                              updateState('paymentRemark', value);
                            }}
                            containerClassName="form__input-row"
                            inputClassName="orgInputContainer form__input-row__input"
                            labelClassName="form__input-row__label"
                            showError={formSubmitted && !paymentRemark}
                            disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                            showAddPaymentRemark
                            placeholder="Select Payment Remark"
                          />
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Sales Manager" className="form__input-row__label" />
                            <div className="form__input-row__input">
                              <SelectAppUser
                                hideTitle
                                containerClassName="orgInputContainer"
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                selectedUser={accountManager}
                                onChange={(value) => {
                                  updateState('accountManager', value);

                                }}
                                disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || selectedOrderForInvoice}
                              />
                            </div>
                          </div>
                        </div>
                        {user?.tenant_info?.sales_config?.addons?.price_list?.is_active && (
                          <div className="ant-col-md-6">
                            <div
                              className="form__input-row"
                              style={{ marginBottom: '10px' }}
                            >
                              <H3Text
                                required={isPriceListMandatory}
                                text="Select Price List"
                                className="form__input-row__label"
                              />
                              <div className="form__input-row__input">
                                <SelectPriceList
                                  selectedPriceList={selectedPriceList}
                                  hideTitle
                                  allowClear
                                  onChange={(value) => {
                                    updateState('selectedPriceList', value?.price_list_id);
                                    if (productSkuIds?.length > 0) {
                                      getApplyPriceLists(selectedOrderForInvoice?.tenant_id || user?.tenant_info?.tenant_id, value?.price_list_id, selectedCustomer, shippingAddress?.state, productSkuIds, (toApplyPriceList) => {
                                        updateState('pricesToApply', toApplyPriceList);
                                        const copyData = JSON.parse(JSON.stringify(data));
                                        for (let i = 0; i <= toApplyPriceList?.length; i++) {
                                          for (let j = 0; j <= copyData?.length; j++) {
                                            const copyDataItem = copyData[j];
                                            if (toApplyPriceList[i]?.product_sku_id === copyDataItem?.product_sku_id && (toApplyPriceList[i]?.product_sku_id !== undefined || copyDataItem?.product_sku_id !== undefined)) {
                                              copyDataItem.unitPrice = toApplyPriceList[i]?.is_inclusive_of_tax ? toApplyPriceList[i]?.price_list_amount / ((100 + copyDataItem?.taxInfo?.tax_value) / 100) : toApplyPriceList[i]?.price_list_amount;
                                              copyDataItem.discount = toApplyPriceList[i]?.discount_percentage;
                                              copyData[j] = copyDataItem;
                                              break;
                                            }
                                          }
                                        }
                                        updateState('data', copyData);
                                      });
                                    }
                                  }}
                                  customerId={selectedCustomer}
                                  tenantId={selectedOrderForInvoice?.tenant_id || selectedInvoice?.tenant_id}
                                  state={shippingAddress?.state}
                                  disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || !selectedCustomer || !billingAddress || !shippingAddress || selectedOrderForInvoice}
                                  containerClass={(formSubmitted && !selectedPriceList && isPriceListMandatory) ? 'form__input-row__input-error' : 'form__input-row'}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                        {user?.tenant_info?.tally_configuration?.sales_account_selection === 'DOC_LEVEL' && (
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text
                                required
                                text="Tally Sales Account"
                                className="form__input-row__label"
                              />
                              <div
                                className="form__input-row__input"
                              >
                                <div className={`form__input-row sales_account__selector__wrapper ${formSubmitted &&
                                  !salesAccount &&
                                  user?.tenant_info?.tally_configuration?.sales_account_selection === 'DOC_LEVEL'
                                  ? 'sales_account__selector__wrapper_with_error'
                                  : ''
                                  }`}
                                >
                                  <PRZSelect
                                    value={salesAccount}
                                    onChange={(value) => {
                                      updateState('salesAccount', value);
                                    }}
                                    disabled={
                                      getTallyConnectionsLoading ||
                                      createInvoiceLoading ||
                                      updateInvoiceLoading ||
                                      getDocConfigInvoiceLoading
                                    }
                                    options={salesAccountOptions}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        {!moRmLines && (
                          <CustomFieldV3
                            customFields={cfInvoiceDoc}
                            formSubmitted={formSubmitted}
                            customInputChange={(value, cfId) => customInputChange(value, cfId)}
                            wrapperClassName="ant-col-md-6"
                            containerClassName="form__input-row"
                            labelClassName="form__input-row__label"
                            inputClassName="form__input-row__input"
                            errorClassName="form__input-row__input-error"
                            disableCase={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                            hideTitle
                            isCarryForward={true}
                          />
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="form__section">
                    <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                      <H3Text text="PART B" className="form__section-title" />
                      <div className="form__section-line" />
                    </div>
                    <div className="ant-row">
                      <div className="ant-col-md-8">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text="Bill From"
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__address__wrapper ${(formSubmitted && !billFrom) ? 'form__input-row__address-error' : ''} ${(!billFrom) ? 'form__input-row__address-disabled' : ''}`}>
                            {' '}
                            <div className="form__input-row__address">
                              {billFrom && (
                                <div className="form__input-row__address-info">
                                  <div
                                    className="form__input-row__address-l1"
                                  >
                                    {billFrom?.address1}
                                  </div>
                                  <div
                                    className="form__input-row__address-l2"
                                  >
                                    {`${billFrom?.city}, ${billFrom?.state}, ${billFrom?.postal_code}, ${billFrom?.country}`}
                                  </div>
                                </div>
                              )}
                              {!billFrom && (
                                <H3Text
                                  text="Select address.."
                                  className="form__input-row__address-placeholder"
                                />
                              )}
                            </div>
                            {(formSubmitted && !billFrom) && (
                              <div className="input-error">
                                *Please select shipping address
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-8">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text="Ship From"
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__address__wrapper ${(formSubmitted && !shipFrom) ? 'form__input-row__address-error' : ''} ${(!shipFrom) ? 'form__input-row__address-disabled' : ''}`}>
                            {' '}
                            <div className="form__input-row__address">
                              {shipFrom && (
                                <div className="form__input-row__address-info">
                                  <div
                                    className="form__input-row__address-l1"
                                  >
                                    {shipFrom?.address1}
                                  </div>
                                  <div
                                    className="form__input-row__address-l2"
                                  >
                                    {`${shipFrom?.city}, ${shipFrom?.state}, ${shipFrom?.postal_code}, ${shipFrom?.country}`}
                                  </div>
                                </div>
                              )}
                              {!shipFrom && (
                                <H3Text
                                  text="Select address.."
                                  className="form__input-row__address-placeholder"
                                />
                              )}
                              <div
                                className="form__input-row__address-icon"
                                onClick={() => {
                                  if (selectedTenant) {
                                    updateState('selectedAddressType', 'TENANT_SHIP_FROM');
                                    updateState('showAddressDrawer', true);
                                  }
                                }}
                              >
                                <EditFilled />
                                {' '}
                                Update
                              </div>
                            </div>
                            {(formSubmitted && !shipFrom) && (
                              <div className="input-error">
                                *Please select shipping address
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-8" />
                      <div className="ant-col-md-8">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text="Bill To"
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__address__wrapper ${(formSubmitted && !billingAddress) ? 'form__input-row__address-error' : ''} ${(!selectedCustomer) ? 'form__input-row__address-disabled' : ''}`}>
                            <div className="form__input-row__address">
                              {billingAddress && (
                                <div className="form__input-row__address-info">
                                  <div
                                    className="form__input-row__address-l1"
                                  >
                                    {billingAddress?.address1}
                                  </div>
                                  <div
                                    className="form__input-row__address-l2"
                                  >
                                    {`${billingAddress?.city}, ${billingAddress?.state}, ${billingAddress?.postal_code}, ${billingAddress?.country}`}
                                  </div>
                                </div>
                              )}
                              {!billingAddress && (
                                <H3Text
                                  text="Select address.."
                                  className="form__input-row__address-placeholder"
                                />
                              )}
                              <div
                                className="form__input-row__address-icon"
                                onClick={() => {
                                  if (selectedCustomer) {
                                    updateState('selectedAddressType', 'TENANT_BILLING');
                                    updateState('showAddressDrawer', true);
                                  }
                                }}
                              >
                                <EditFilled />
                                {' '}
                                Update
                              </div>
                            </div>
                            {(formSubmitted && !billingAddress) && (
                              <div className="input-error">
                                *Please select billing address
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-8">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text="Ship To"
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__address__wrapper ${(formSubmitted && !shippingAddress) ? 'form__input-row__address-error' : ''} ${(!selectedCustomer) ? 'form__input-row__address-disabled' : ''}`}>
                            {' '}
                            <div className="form__input-row__address">
                              {shippingAddress && (
                                <div className="form__input-row__address-info">
                                  <div
                                    className="form__input-row__address-l1"
                                  >
                                    {shippingAddress?.address1}
                                  </div>
                                  <div
                                    className="form__input-row__address-l2"
                                  >
                                    {`${shippingAddress?.city}, ${shippingAddress?.state}, ${shippingAddress?.postal_code}, ${shippingAddress?.country}`}
                                  </div>
                                </div>
                              )}
                              {!shippingAddress && (
                                <H3Text
                                  text="Select address.."
                                  className="form__input-row__address-placeholder"
                                />
                              )}
                              <div
                                className="form__input-row__address-icon"
                                onClick={() => {
                                  if (selectedCustomer) {
                                    updateState('selectedAddressType', 'TENANT_SHIPPING');
                                    updateState('showAddressDrawer', true);
                                  }
                                }}
                              >
                                <EditFilled />
                                {' '}
                                Update
                              </div>
                            </div>
                            {(formSubmitted && !shippingAddress) && (
                              <div className="input-error">
                                *Please select shipping address
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-8" />
                      <div className="ant-col-md-8 form__checkboxes">
                        {user?.tenant_info?.integration_config?.sub_modules?.whatsapp?.is_active && (
                          <div className="form__input-row">
                            <Checkbox
                              disabled={getTallyConnectionsLoading || createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                              checked={sendWhatsappNotification}
                              onChange={(e) => {
                                updateState('sendWhatsappNotification', e.target.checked);
                              }}
                            />
                            <span
                              style={{
                                fontWeight: '500',
                                fontSize: '12px',
                                marginLeft: '5px',
                              }}
                            >
                              Send automatic whatsapp notification
                            </span>
                          </div>
                        )}
                        <div className="form__input-row">
                          <Checkbox
                            disabled={getTallyConnectionsLoading || createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || (selectedInvoice && !location?.state?.cloneInv)}
                            checked={dontDeductStock}
                            onChange={(e) => {
                              updateState('dontDeductStock', e.target.checked);
                              updateState('emptyQytForOnce', true);
                              if (data && CloneInv && !emptyQytForOnce && dontDeductStock) {
                                updateState(data, data?.map((item) => ({
                                  ...item,
                                  quantity: 0,
                                })));
                              }
                            }}
                          />
                          <span
                            style={{
                              fontWeight: '500',
                              fontSize: '12px',
                              marginLeft: '5px',
                            }}
                          >
                            Create invoice without deducting stock
                            <Tooltip
                              title="This option prevents the system from deducting stock quantity for this item when creating an invoice."
                            >
                              <InfoCircleOutlined
                                style={{ marginLeft: '5px', color: 'rgb(45, 124, 247)', cursor: 'pointer' }}
                              />
                            </Tooltip>
                          </span>
                        </div>
                        <div className="form__input-row">
                          <Checkbox
                            disabled={true}
                            checked={calculateRateFromBatches}
                            onChange={(e) => {
                              updateState('calculateRateFromBatches', e.target.checked);
                            }}
                          />
                          <span
                            style={{
                              fontWeight: '500',
                              fontSize: '12px',
                              marginLeft: '5px',
                            }}
                          >
                            Auto calculate selling price from selected batches
                            <Tooltip
                              title="Enabling this option will calculate selling price of the items as the weighted average of the selling price of selected batches. This configuration will override selling price calculation via formulas or price lists."
                            >
                              <InfoCircleOutlined
                                style={{ marginLeft: '5px', color: 'rgb(45, 124, 247)', cursor: 'pointer' }}
                              />
                            </Tooltip>
                          </span>
                        </div>
                        <div className="form__input-row">
                          <Checkbox
                            disabled={getTallyConnectionsLoading || createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                            checked={checkedRecipients}
                            onChange={() => {
                              updateState('checkedRecipients', !checkedRecipients);
                            }}
                          />
                          <span
                            style={{
                              fontWeight: '500',
                              fontSize: '12px',
                              marginLeft: '5px',
                            }}
                          >
                            Send automatic email when order is issued
                          </span>
                        </div>
                        {checkedRecipients && (
                          <div className="form__input-row">
                            <PRZSelect
                              className={`${(formSubmitted && checkedRecipients && !toRecipients?.length) ? 'form__recipients__input-error' : ''}`}
                              mode="tags"
                              value={toRecipients}
                              filterOption={false}
                              maxTagCount="responsive"
                              onChange={(value) => {
                                const recipients = [];
                                for (let i = 0; i < value?.length; i++) {
                                  if (Helpers.validateEmail(value[i])) {
                                    recipients.push(value[i]);
                                  }
                                }
                                updateState('toRecipients', recipients);
                              }}
                              disabled={getTallyConnectionsLoading || createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {moRmLines && (
              <div className="ant-row">
                <div className="ant-col-md-12">
                  <div
                    className="form__input-row__label"
                    style={{
                      fontSize: '12px', fontWeight: 500, marginBottom: '5px',
                    }}
                  >
                    Please select a work order&nbsp;
                    <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                  </div>
                  <div className="form__input-row">
                    {
                      moInfo?.fgLines && (
                        <PRZSelect
                          className="form__input-row__input"
                          value={selectedWorkOrder}
                          onChange={(value) => {
                            const fgTpIds = workOrder?.find((item) => item?.po_id === value)?.po_lines?.map((item) => item?.tenant_product_id);
                            const sellerId = workOrder?.find((item) => item?.po_id === value)?.tenant_seller_info?.seller_id;
                            updateState('selectedWorkOrder', value);
                            updateState('selectedFg', fgTpIds);
                            updateState('sellerId', sellerId);
                          }}
                        >
                          {workOrder?.filter((item) => ['SENT_FOR_APPROVAL', 'ISSUED'].includes(item?.status))?.map((item) =>
                            <Option key={item?.po_id} value={item?.po_id}>
                              {`${item?.po_number} ${item?.tenant_seller_info?.seller_name}${item?.seller_address ? ` (${item?.seller_address?.city})` : ''}`}
                            </Option>)}
                        </PRZSelect>
                      )
                    }
                  </div>
                </div>
              </div>
            )}
            {/* FG Selector */}
            {moRmLines && (
              <div className="ant-row">
                <div className="ant-col-md-12">
                  <div
                    className="form__input-row__label"
                    style={{
                      fontSize: '12px', fontWeight: 500, marginBottom: '5px',
                    }}
                  >
                    Please select finished goods&nbsp;
                    <span style={{ color: 'red', marginLeft: '5px' }}>*</span>
                  </div>
                  <div className="form__input-row">
                    {
                      moInfo?.fgLines && (
                        <PRZSelect
                          className="form__input-row__input"
                          value={selectedFg}
                          onChange={(fg) => {
                            updateState('selectedFg', fg);
                          }}
                          mode='multiple'
                          maxTagCount="responsive"
                          removeIcon={selectedFg?.length === 1 ? null : <CloseOutlined />}
                        >
                          {workOrder?.find((item) => item?.po_id === selectedWorkOrder)?.po_lines?.map((item) => (
                            <Option
                              key={item?.tenant_product_id}
                              value={item?.tenant_product_id}
                            >
                              {`${item?.ref_product_code ? `${item?.ref_product_code} -` : ''} ${item?.product_sku_name}`?.trim()}
                            </Option>
                          ))}
                        </PRZSelect>
                      )
                    }
                  </div>
                </div>

                <div className="ant-col-md-12" />

                <div className="ant-col-md-12">
                  <div className="form__input-row">
                    <Checkbox
                      disabled={getTallyConnectionsLoading || createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                      checked={createDCAutomatically}
                      onChange={(e) => {
                        updateState('createDCAutomatically', e.target.checked);
                      }}
                    />
                    <span
                      style={{
                        fontWeight: '500',
                        fontSize: '12px',
                        marginLeft: '5px',
                      }}
                    >
                      Create Delivery Challan Automatically
                    </span>
                  </div>
                </div>
              </div>
            )}
            {(jwRmLines || (moRmLines && selectedFg?.length) || (!moRmLines && selectedFg?.length === 0)) && (
              <div className="form__lines-wrapper">
                {tenantDepartmentId && (
                  <InvoiceLines
                    title={() => (
                      <div className="form-title__wrapper">
                        <H3Text
                          text={`Invoice Products (${data.filter((item) => item.product_sku_name?.length > 0)?.length})`}
                          className="form__input-row__label"
                        />
                        <div className="form-title-left">
                          <Checkbox
                            checked={!isLineWiseDiscount}
                            onChange={() => {
                              const copyData = JSON.parse(JSON.stringify(data));
                              copyData.map((i) => {

                                i.discount = 0;
                                i.unitDiscount = 0;
                                i.showUnitDiscount = false;
                                i.lineDiscountType = 'Percent';

                                i.child_taxes = Helpers.computeTaxation(i?.quantity * i?.unitPrice, i?.taxInfo, user?.tenant_info?.state, shippingAddress?.state)?.tax_info?.child_taxes;
                              });
                              updateState('isLineWiseDiscount', !isLineWiseDiscount);
                              updateState('discountPercentage', 0);
                              updateState('discountType', 'Percent');
                              updateState('data', copyData);
                            }}
                            disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                          />
                          <div style={{ marginLeft: '5px' }}>Invoice Level Discount</div>
                          <div style={{ marginLeft: '5px' }}>
                            <CustomDocumentColumns
                              visibleColumns={visibleColumns}
                              setVisibleColumns={(val) => updateState('visibleColumns', val)}
                              customColumns={cfInvoiceLine}
                              data={data}
                              updateData={(updatedData) => updateState('data', updatedData)}
                            />
                          </div>
                        </div>
                      </div>
                    )}
                    handleDelete={handleDelete}
                    handleProductChange={handleProductChange}
                    handleProductChangeValue={handleProductChangeValue}
                    data={getDataSource(data)}
                    addNewRow={() => addNewRow()}
                    updateData={(updatedData) => updateState('data', updatedData)}
                    formSubmitted={formSubmitted}
                    loading={getInvoiceByIdLoading || createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || getProductByIdLoading || getWorkOrderRmLoading}
                    tenantDepartmentId={tenantDepartmentId}
                    toggleBatch={(batch, adjustmentRow) => toggleBatch(batch, adjustmentRow)}
                    toggleBatchInner={(batch, adjustmentRow) => toggleBatchInner(batch, adjustmentRow)}
                    selectedOrderForInvoice={selectedOrderForInvoice}
                    allStock={allStock}
                    toggleAllStock={() => updateState('allStock', !allStock)}
                    isLineWiseDiscount={isLineWiseDiscount}
                    moRmLines={moRmLines}
                    disabled={!selectedFg}
                    tallyConnections={tallyConnections}
                    pricesToApply={pricesToApply}
                    selectedCurrencyName={selectedCurrencyName}
                    tenantId={selectedTenant || selectedOrderForInvoice?.tenant_id || user?.tenant_info?.tenant_id}
                    selectedCustomer={selectedCustomer}
                    billFromState={user?.tenant_info?.state}
                    billToState={billingAddress?.state}
                    dontDeductStock={dontDeductStock}
                    visibleColumns={visibleColumns}
                    cfInvoiceLine={cfInvoiceLine}
                    customLineInputChange={(value, key, fgId, lineProps, cBatchId, cBatchQty, rate) => customLineInputChange(value, key, fgId, lineProps, cBatchId, cBatchQty, rate)}
                    parentState={state}
                    parentProps={props}
                    setParentState={(newState) => setState({ ...newState })}
                    calculateRateFromBatches={calculateRateFromBatches}
                    jwRmLines={jwRmLines}
                    disablePriceChange={(selectedOrderForInvoice && !isPackingSlip && !selectedDCForInvoice) ? disablePriceChange : false}
                    isCarryForward={isCarryForward}
                    selectedInvoice={selectedInvoice}
                    handleMultiProductChange={handleMultiProductChange}
                    isInvoiceFromSo={selectedInvoice?.order_id}
                  />
                )}
              </div>
            )}

            {((moRmLines && selectedFg?.length) || (!moRmLines && selectedFg?.length === 0)) && (
              <div className="form__data-wrapper">
                <div className="ant-row">
                  {!moRmLines && !selectedOrderForInvoice && (<div className="ant-col-md-24">
                    <div className={`new-row-button ${!!data?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id)) ? 'new-row-button__disabled' : ''}`} onClick={() => {
                      if (!data?.find((item) => !(item?.product_sku_id || item?.product_sku_info?.product_sku_id))) addNewRow();
                    }}>
                      <span className="new-row-button__icon"><PlusCircleFilled /></span>
                      <div>New Item</div>
                    </div>
                  </div>
                  )}

                  <div className="ant-col-md-12">
                    <div className="form__data-tc">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <label className="orgFormLabel" style={{ marginRight: 8 }}>
                          Terms and Conditions
                        </label>
                        <TnCSelector
                          entityName="INVOICE"
                          selectedTnC={termsAndConditions}
                          updateTnC={(value) => handleChangeTextArea(value)}
                          disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                          tenantId={selectedTenant || selectedTenantIdInv}
                          updateTncId={(value) => updateState('tncId', value)}
                          tncId={tncId}
                          isCarryForwardTnC={!!selectedOrderForInvoice || !!selectedOrderForInvoice || !!selectedInvoice?.order_id}
                        />
                      </div>
                      <RichTextEditor
                        onChange={(value) => handleChangeTextArea(value)}
                        value={termsAndConditions}
                        disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                      />
                    </div>
                    {!jwRmLines && !moRmLines && !!selectedTenantTallyIntegrationId && <div className="form__data-tc">
                      <label className="orgFormLabel">
                        Narration
                      </label>
                      <textarea
                        className="orgFormInput"
                        rows="4"
                        onChange={(event) => {
                          updateState('narration', event.target.value);
                        }}
                        disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                        value={narration}
                        style={{
                          minHeight: '40px',
                          height: '60px',
                        }}
                      />
                    </div>}
                    <div className="form__data-attachment" style={{ marginTop: '10px' }}>
                      <label className="orgFormLabel">
                        Attachment(s)
                      </label>
                      <Upload
                        action={Constants.UPLOAD_FILE}
                        listType="picture-card"
                        fileList={fileList}
                        disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                        onChange={(fileListData) => {
                          const copyFileList = fileListData?.fileList?.map((item) => ({
                            ...item,
                            url: item?.response?.response?.location || item?.url,
                          }));
                          updateState('fileList', copyFileList);
                        }}
                        multiple
                      >
                        {fileList?.length >= 20 ? null : uploadButton}
                      </Upload>
                    </div>
                  </div>

                  <div className="ant-col-md-12">
                    <div className="form-calculator__wrapper">
                      <div className="form-calculator">
                        <div className="form-calculator__field">
                          <H3Text text="Sub Total" className="form-calculator__field-name" />
                          <H3Text
                            text={MONEY((getTotals().totalAmount), selectedCurrencyName?.currency_code)}
                            className="form-calculator__field-value"
                            hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                            popOverMessage={'You don\'t have access to view sub total'}
                          />
                        </div>
                        {!isLineWiseDiscount ? (
                          <div className="form-calculator__field">
                            <H3Text text="Discount" className="form-calculator__field-name" />
                            <div className="form-calculator__field-value" style={{ display: 'flex', }}>
                              <div style={{ width: '112px' }}>
                                <H3FormInput
                                  value={discountPercentage}
                                  type="number"
                                  containerClassName={(formSubmitted && Number(discountPercentage) <= 0) ? 'form-error__input' : ''}
                                  labelClassName="orgFormLabel"
                                  inputClassName="orgFormInput"
                                  onChange={(e) => {

                                    const copyData = JSON.parse(JSON.stringify(data));
                                    const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.unitPrice), 0);

                                    copyData.map((item) => {

                                      const discountValue = discountType === 'Percent' ? Number.parseFloat(e.target.value) : ((item.quantity * item.unitPrice) / Number.parseFloat(totalValue)) * Number.parseFloat(e.target.value);

                                      const taxableValue = discountType === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item.quantity * item?.unitPrice - discountValue, 0);

                                      item.discount = discountValue;
                                      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billingAddress?.state, shippingAddress?.state)?.tax_info?.child_taxes;
                                    });
                                    updateState('data', copyData);
                                    updateState('discountPercentage', Number.parseFloat(e.target.value));
                                  }}
                                />
                              </div>
                              <div className="form-calculator__discount-type">
                                <PRZSelect
                                  value={discountType}
                                  onChange={(value) => {
                                    const copyData = JSON.parse(JSON.stringify(data));
                                    const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.unitPrice), 0);
                                    copyData.map((item) => {

                                      const discountValue = value === 'Percent' ? Number(discountPercentage) : ((item.quantity * item.unitPrice) / Number.parseFloat(totalValue)) * Number(discountPercentage);

                                      const taxableValue = value === 'Percent'
                                        ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100)
                                        : Math.max(item.quantity * item?.unitPrice - discountValue, 0);

                                      item.discount = discountValue;
                                      item.lineDiscountType = value;
                                      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billingAddress?.state, shippingAddress?.state)?.tax_info?.child_taxes;
                                    });
                                    updateState('data', copyData);
                                    updateState('discountType', value);
                                  }}
                                  options={amountTypeOptions}
                                />
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="form-calculator__field">
                            <H3Text text="Discount" className="form-calculator__field-name" />
                            <H3Text
                              text={MONEY((getTotals().totalDiscount), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                            />
                          </div>
                        )}

                        <div className="form-calculator__field">
                          <div
                            className="form-calculator__field-name"
                            style={{ display: 'flex', alignItems: 'center' }}
                          >
                            <H3Text text={charge1Name} style={{ marginRight: '10px' }} />
                            {freightTax && (
                              <div style={{
                                color: '#2d7df7',
                                fontWeight: '400',
                                fontSize: '12px',
                              }}
                              >
                                {`tax@${freightTax}%`}
                              </div>
                            )}
                            {/* <Checkbox disabled={!(charge1Value > 0)} onChange={(e) => { setState({ createExpenseCheckbox: e.target.checked }); }} checked={createExpenseCheckbox}><span style={{ color: '#2d7df7', fontWeight: '400', fontSize: '12px' }}>+ Add To Expense</span></Checkbox> */}
                          </div>

                          <div className="form-calculator__field-value" style={{ display: 'flex' }}>
                            <div style={{ width: '112px' }}>
                              <H3FormInput
                                value={charge1Value}
                                type="number"
                                containerClassName={(formSubmitted && Number(charge1Value) < 0) ? 'form-error__input' : ''}
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                onChange={(e) => {
                                  const newFreightTaxData = {
                                    ...freightTaxData,
                                    child_taxes: Helpers.computeTaxation(e.target.value, freightTaxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes
                                  };
                                  updateState('charge1Value', e.target.value);
                                  updateState('freightTaxData', newFreightTaxData);
                                }}
                              />
                            </div>
                            <FreightTaxInput
                              freightTaxId={freightTaxId}
                              openFreightTax={openFreightTax}
                              sacCode={freightSacCode}
                              setOpenFreightTax={(value) => updateState('openFreightTax', value)}
                              setFreightTaxData={handleFreightTaxData}
                              setSacCode={(value) => updateState('freightSacCode', value)}
                            />
                          </div>
                        </div>

                        {renderCharges(splitChargesData(chargeData)?.chargeWithTaxName)}

                        <div className="form-calculator__field">
                          <H3Text text="Taxable Amount" className="form-calculator__field-name" />
                          <H3Text
                            text={MONEY((getTotals().totalBase), selectedCurrencyName?.currency_code)}
                            className="form-calculator__field-value"
                            hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                            popOverMessage={'You don\'t have access to view taxable amount'}
                          />
                        </div>
                        {data?.[0]?.child_taxes?.[0]?.tax_type_name && Helpers.groupAndSumByTaxName(FormHelpers.childTaxesData([...data, freightTaxData, ...chargeData?.flatMap((charge) => charge?.chargesTaxData)]))?.map((tax, i) => (
                          <Fragment key={i}>
                            <div className="form-calculator__field">
                              <H3Text
                                text={tax?.tax_type_name}
                                className="form-calculator__field-name"
                              />
                              <H3Text
                                text={MONEY((tax?.tax_amount || '0'), selectedCurrencyName?.currency_code)}
                                className="form-calculator__field-value"
                                hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                                popOverMessage={`You don't have access to view ${tax?.tax_type_name?.toLowerCase()}`}
                              />
                            </div>

                          </Fragment>
                        ))}

                        {createExpenseCheckbox && (
                          <TransporterForm
                            transporterId={transporterId}
                            vehicleNumber={vehicleNumber}
                            transporterBillNumber={transporterBillNumber}
                            setTransporterId={(val) => updateState('transporterId', val)}
                            setVehicleNumber={(val) => updateState('vehicleNumber', val)}
                            setTransporterBillNumber={(val) => updateState('transporterBillNumber', val)}
                          />
                        )}
                        <div />

                        {user?.tenant_info?.global_config?.settings?.enable_tds_tcs && user?.tenant_info?.country_code === 'IN' && (
                          <div className="form-calculator__field">
                            <div
                              className="form-calculator__field-name"
                              style={{ marginBottom: '12px' }}
                            >
                              <div>
                                <Radio.Group
                                  disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                                  onChange={(event) => {
                                    updateState('taxTypeName', event.target.value);
                                    updateState('taxTypeId', '');
                                    updateState('taxTypeInfo', null);
                                  }}
                                  value={taxTypeName}
                                >
                                  <Radio value="TDS">TDS</Radio>
                                  <Radio value="TCS">TCS</Radio>
                                </Radio.Group>
                              </div>
                              <SelectTaxType
                                selectedTaxType={taxTypeId}
                                disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading}
                                onChange={handleTaxTypeChange}
                                taxTypeName={taxTypeName}
                                customStyle={{
                                  width: '220px',
                                  backgroundColor: 'white',
                                }}
                              />
                            </div>
                            {taxTypeName === 'TCS' ? (
                              <H3Text
                                text={MONEY((getTotals().totalTcs || '0'), selectedCurrencyName?.currency_code)}
                                className="form-calculator__field-value"
                                hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                                popOverMessage={`You don't have access to view ${taxTypeName?.toLowerCase()}`}
                              />
                            ) : (
                              <H3Text
                                text={MONEY((getTotals().totalTds || '0'), selectedCurrencyName?.currency_code)}
                                className="form-calculator__field-value"
                                hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                                popOverMessage={`You don't have access to view ${taxTypeName?.toLowerCase()}`}
                              />
                            )}
                          </div>
                        )}
                        {renderCharges(splitChargesData(chargeData)?.chargeWithoutTaxName)}

                        <div>
                          <div
                            className="new-charge-row-button"
                            onClick={() => addNewChargesRow()}
                            style={{ marginTop: '15px' }}
                          >
                            <span
                              className="new-charge-row-button__icon"
                            >
                              <PlusCircleFilled />
                            </span>
                            <div>Add Charges</div>
                          </div>
                        </div>
                        {user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.round_off_method !== 'NO_ROUND_OFF' && (
                          <div className="form-calculator__field">
                            <H3Text
                              text="Round Off"
                              className="form-calculator__field-name"
                            />
                            <Tooltip
                              title={`Round Off method for Invoice is set to ${user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.round_off_method?.replace(/_/g, ' ')?.toProperCase()}`}
                            >
                              <div style={{ cursor: 'pointer', }}>
                                <FontAwesomeIcon icon={faCircleInfo} size='lg' style={{ color: '#2D7DF7', }} />
                              </div>
                            </Tooltip>
                            <H3Text
                              text={`${Helpers.configuredRoundOff(getTotals()?.invoiceTotal, user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.round_off_method)?.roundOff < 0 ? '(-) ' : ''}${MONEY(
                                Math.abs(Helpers.configuredRoundOff(getTotals()?.invoiceTotal, user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.round_off_method)?.roundOff),
                                selectedCurrencyName?.currency_code
                              )}`}
                              className="form-calculator__field-value"
                              hideText={
                                isDataMaskingPolicyEnable && isHideSellingPrice
                              }
                              popOverMessage={
                                'You don\'t have access to view sub total'
                              }
                            />
                          </div>
                        )}
                        <div className="form-calculator__field form-calculator__field-total">
                          <H3Text text="Grand Total" className="form-calculator__field-name" />
                          <H3Text
                            text={MONEY(Helpers.configuredRoundOff(getTotals()?.invoiceTotal, user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.round_off_method)?.value, selectedCurrencyName?.currency_code)}
                            className="form-calculator__field-value"
                            hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                            popOverMessage="You don't have access to view grand total"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )
      }
      {
        <div
          className={`form__footer ${userMenuConfig === 'FIXED' ? 'form__footer-fixed' : ''}`}
          style={{ width: selectedOrderForInvoice ? '1225px' : 'width: calc(100% - 235px)' }}
        >
          {(!!selectedInvoice && !location?.state?.cloneInv) ? (
            <Fragment>
              <PRZConfirmationPopover
                title="Are you sure you want to update?"
                content={
                  <Fragment>
                    <PRZText text="Reason" required />
                    <PRZInput
                      placeholder="Enter update reason"
                      value={updateDocumentReason}
                      onChange={(e) => setState((prevState) => ({
                        ...prevState,
                        updateDocumentReason: e.target.value,
                      }))}
                    />
                  </Fragment>
                }
                onConfirm={() => {
                  updateState('currentAction', 'SAVE_AS_DRAFT');
                  if (!createInvoiceLoading) {
                    createInvoice(false);
                  }
                }}
                confirmButtonText="Confirm"
                cancelButtonText="Back"
                confirmDisabled={!updateDocumentReason}
              >
                <PRZButton
                  id="save-as-draft"
                  type="default"
                  wrapperStyle={{ marginRight: '10px' }}
                  buttonStyle={{
                    width: '130px',
                    height: '40px',
                    border: '1px solid #2d7df7',
                    color: '#2d7df7',
                  }}
                  isLoading={((createInvoiceLoading) || updateInvoiceLoading || getDocConfigInvoiceLoading) && currentAction === 'SAVE_AS_DRAFT'}
                  disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || !invoice}
                >
                  Save as Draft
                </PRZButton>
              </PRZConfirmationPopover>
              {(selectedInvoice ? selectedInvoice?.status !== 'VOID' : true) && getDraftStatusType(orgStatusInvoice)?.length === 1 && (
                <PRZConfirmationPopover
                  title="Are you sure you want to update?"
                  content={
                    <Fragment>
                      <PRZText text="Reason" required />
                      <PRZInput
                        placeholder="Enter update reason"
                        value={updateDocumentReason}
                        onChange={(e) => setState((prevState) => ({
                          ...prevState,
                          updateDocumentReason: e.target.value,
                        }))}
                      />
                    </Fragment>
                  }
                  onConfirm={() => {
                    updateState('currentAction', 'SAVE_AND_ISSUE');
                    if (!createInvoiceLoading) {
                      createInvoice(true);
                    }
                  }}
                  confirmButtonText="Confirm"
                  cancelButtonText="Back"
                  confirmDisabled={!updateDocumentReason}
                >
                  <PRZButton
                    id="save-and-issue"
                    isLoading={((createInvoiceLoading) || updateInvoiceLoading || getDocConfigInvoiceLoading) && currentAction === 'SAVE_AND_ISSUE'}
                    disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || !invoice}
                    buttonStyle={{ width: '130px', height: '40px' }}
                  >
                    Save and Issue
                  </PRZButton>
                </PRZConfirmationPopover>
              )}
            </Fragment>
          ) : (
            <Fragment>
              <PRZButton
                type="default"
                onClick={() => {
                  updateState('currentAction', 'SAVE_AS_DRAFT');
                  if (!createInvoiceLoading) {
                    createInvoice(false);
                  }
                }}
                isLoading={((createInvoiceLoading) || updateInvoiceLoading || getDocConfigInvoiceLoading) && currentAction === 'SAVE_AS_DRAFT'}
                disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || !invoice}
                wrapperStyle={{ marginRight: '10px' }}
                buttonStyle={{
                  width: '130px',
                  height: '40px',
                  border: '1px solid #2d7df7',
                  color: '#2d7df7',
                }}
              >
                Save as Draft
              </PRZButton>
              {(selectedInvoice ? selectedInvoice?.status !== 'VOID' : true) && getDraftStatusType(orgStatusInvoice)?.length === 1 && (
                <PRZButton
                  id="save-and-issue"
                  onClick={() => {
                    updateState('currentAction', 'SAVE_AND_ISSUE');
                    if (!createInvoiceLoading) {
                      createInvoice(true);
                    }
                  }}
                  isLoading={((createInvoiceLoading) || updateInvoiceLoading || getDocConfigInvoiceLoading) && currentAction === 'SAVE_AND_ISSUE'}
                  disabled={createInvoiceLoading || updateInvoiceLoading || getDocConfigInvoiceLoading || !invoice}
                  buttonStyle={{ width: '130px', height: '40px' }}
                >
                  Save and Issue
                </PRZButton>
              )}
            </Fragment>
          )}

          {(isDataMaskingPolicyEnable && isHideSellingPrice) && <RestrictedAccessMessage message={'You don\'t have access to view or edit rate'} />}

          {!invoice && (
            <Popconfirm
              placement="topRight"
              title="This feature is not accessible within your current plan to use this feature contact us."
              onConfirm={() => window.Intercom('showNewMessage')}
              okText="Contact Us"
              cancelText="Cancel"
            >
              <img
                className="barcode-restrict"
                src={cdnUrl("crown2.png", "images")}
                alt="premium"
                style={{
                  marginLeft: '8px',
                  marginTop: '5px',
                }}
              />
            </Popconfirm>
          )}

          <div className="form-barcode__wrapper" style={{ display: 'flex' }}>
            {!barCoding && (
              <Popconfirm
                placement="topRight"
                title="This feature is not accessible within your current plan to use this feature contact us."
                onConfirm={() => window.Intercom('showNewMessage')}
                okText="Contact Us"
                cancelText="Cancel"
              >
                <img className="barcode-restrict" src={cdnUrl("crown2.png", "images")} alt="premium" />
              </Popconfirm>
            )}
            <BarcodeReader
              productTypes={['STORABLE', 'SERVICE', 'BUNDLE', 'NON_STORABLE']}
              isSalesProduct
              excludedProducts={data.map((item) => JSON.stringify(item?.product_sku_id)) || []}
              tenantDepartmentId={tenantDepartmentId}
              excludeOutOfStock
              filterReservedQuantity
              onSearch={(tenantSku) => {
                if (data[data?.length - 1]?.product_sku_id) {
                  addNewRow((key) => handleProductChange(tenantSku, key));
                } else {
                  handleProductChange(tenantSku, data[data?.length - 1]?.key);
                }
              }}
              disabled={!barCoding}
            />
          </div>

        </div>
      }
    </Fragment >
  );
};

const mapStateToProps = ({
  SellerReducers, UserReducers, ProductReducers, PurchaseOrderReducers, AddressReducer, WorkflowsReducers, OfferReducers, TenantReducers, InvoiceReducers, CFV2Reducers, TallyIntegrationReducers, CustomStatusReducers, DocConfigReducers, CurrenciesReducers, TagReducers, MOReducers,
}) => ({
  sellers: SellerReducers.sellers,
  products: ProductReducers.products,
  sellerAddresses: AddressReducer.sellerAddresses,
  tenantAddresses: AddressReducer.tenantAddresses,
  purchaseWorkflows: WorkflowsReducers.purchaseWorkflows,
  tenantSkuOffer: OfferReducers.tenantSkuOffer,
  tenantsConfiguration: TenantReducers.tenantsConfiguration,
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  selectedInvoice: InvoiceReducers.selectedInvoice,
  createInvoiceLoading: InvoiceReducers.createInvoiceLoading,
  updateInvoiceLoading: InvoiceReducers.updateInvoiceLoading,
  getInvoiceByIdLoading: InvoiceReducers.getInvoiceByIdLoading,
  getDocCFV2Loading: CFV2Reducers.getDocCFV2Loading,
  cfV2DocInvoice: CFV2Reducers.cfV2DocInvoice,
  getProductByIdLoading: ProductReducers.getProductByIdLoading,
  getTallyConnectionsLoading: TallyIntegrationReducers.getTallyConnectionsLoading,
  tallyConnections: TallyIntegrationReducers.tallyConnections,
  orgStatusInvoice: CustomStatusReducers.orgStatusInvoice,
  docConfigInvoice: DocConfigReducers.docConfigInvoice,
  getDocConfigInvoiceLoading: DocConfigReducers.getDocConfigInvoiceLoading,
  CurrenciesResults: CurrenciesReducers.CurrenciesResults,
  getCurrenciesLoading: CurrenciesReducers.getCurrenciesLoading,
  createTagLoading: TagReducers.createTagLoading,
  getWorkOrderRmLoading: MOReducers.getWorkOrderRmLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
  getCustomers: (keyword, tenantId, page, limit, customerId, callback) => dispatch(CustomerActions.getCustomers(keyword, tenantId, page, limit, customerId, callback)),
  getSellers: (keyword, tenantId, page, limit) => dispatch(SellerActions.getSellers(keyword, tenantId, page, limit)),
  getAddresses: (page, entityId, entityType, addressId) => dispatch(AddressActions.getAddresses(page, entityId, entityType, addressId)),
  getPurchaseWorkflows: () => dispatch(WorkflowActions.getPurchaseWorkflows()),
  updatePoStatus: (payload, callback) => dispatch(PurchaseOrderActions.updatePoStatus(payload, callback)),
  getTenantsConfiguration: (tenantId, callback) => dispatch(TenantActions.getTenantsConfiguration(tenantId, callback)),
  getTenantSkuOffer: (tenantProductId, tenantSellerId, callback) => dispatch(OfferActions.getOfferByTenantSku(tenantProductId, tenantSellerId, callback)),
  getInvoiceById: (orgId, tenantId, invoiceId) => dispatch(InvoiceActions.getInvoiceById(orgId, tenantId, invoiceId)),
  updateInvoice: (payload, callback) => dispatch(InvoiceActions.updateInvoice(payload, callback)),
  createInvoice: (payload, callback) => dispatch(InvoiceActions.createInvoice(payload, callback)),
  getInvoiceByIdSuccess: (selectedInvoice) => dispatch(InvoiceActions.getInvoiceByIdSuccess(selectedInvoice)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getProductById: (tenantId, productSkuId, tenantDepartmentId, callback, filterReservedBatches, productSkuIds) => dispatch(ProductActions.getProductById(tenantId, productSkuId, tenantDepartmentId, callback, filterReservedBatches, productSkuIds)),
  getTallyConnections: (orgId, callback) => dispatch(TallyIntegrationActions.getTallyConnections(orgId, callback)),
  getApplyPriceLists: (tenantId, priceListId, customerId, states, skuId, callback) => dispatch(PriceListActions.getApplyPriceLists(tenantId, priceListId, customerId, states, skuId, callback)),
  getOrgStatus: (payload, callback) => dispatch(CustomStatusActions.getOrgStatus(payload, callback)),
  getDocConfig: (tenantId, entityName, callback) => dispatch(DocConfigActions.getDocConfig(tenantId, entityName, callback)),
  getCurrencies: (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId)),
  createTag: (payload, callback) => dispatch(TagActions.createTag(payload, callback)),
  getWorkOrderRm: (payload, callback) => dispatch(MOActions.getWorkOrderRm(payload, callback)),
  getCharges: (orgId, entityName, callback) => dispatch(ExtraChargesActions.getCharges(orgId, entityName, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(InvoiceForm));
