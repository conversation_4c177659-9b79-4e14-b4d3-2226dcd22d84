import React, { memo } from 'react';

// Third-party libraries
import { Alert, Button, Checkbox, DatePicker, Radio, Select, Upload } from 'antd';
import { EditFilled, UploadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';

// Constants & Helpers
import constants from '@Apis/constants';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';

// UI Components
import PRZInput from '@Components/Common/UI/PRZInput';
import PRZSelect from '@Components/Common/UI/PRZSelect';
import PRZText from '@Components/Common/UI/PRZText';

// Common Components
import CurrencyConversionV2 from '@Components/Common/CurrencyConversionV2';
import CustomDocumentInputs from '@Components/Common/CustomDocumentInputs';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import DocumentNumberSeqInput from '@Components/Admin/Common/DocumentNumberSeqInput';
import SelectDepartment from '@Components/Common/SelectDepartment';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import SelectPaymentRemark from '@Components/Common/SelectPaymentRemark';
import SelectSellerV2 from '@Components/Common/SelectSellerV2';
import TagSelector from '@Components/Common/Selector/TagSelector';
import TenantSelector from '@Components/Common/Selector/TenantSelector';

// Local Imports
import SelectOrdersForGRN, { getLineTotals } from '../helpers';
import { ACTIONS } from '../reducer';

const { Option } = Select;

const uploadButtonFormLevel = (
  <Button icon={<UploadOutlined />} />
);

function GRNFormDocFields({
  selectedPoForGrn,
  selectedGRN,
  formState,
  localDispatch,
  grnId,
  selectedPo,
  createGRNLoading,
  updateGRNLoading,
  getDocCFV2GoodReceivingNotesLoading,
  cfV2DocGoodReceivingNotes,
  grnTypeValue,
  user,
  getTenantsConfiguration,
  getInventoryLocations,
  getPurchaseOrdersV2,
  selectedPurchaseOrder,
  handleMultiPoChange,
  handlePoChange,
  purchaseOrdersV2,
  getPurchaseOrdersV2Loading,
  isDataMaskingPolicyEnable,
  isHideCostPrice,
  data,
  isApInvoiceEnabled,
  purchaseAccountList,
  MONEY,
  customInputChange,
  workOrder,
}) {

  const {
    cfGoodReceivingNotesDoc, cfGoodReceivingNotesLine, visibleLineCfs, selectedTenant, selectedSeller, invoiceNumber, grnDate, selectedPoValue, shippingAddress, formSubmitted, grnNumber, docSeqId, isAutomaticConversionRate, currencyConversionRate, selectedCurrencyInfo, selectedCurrencyID, paymentTerms, paymentRemarks, selectedTenantSeller, tenantDepartmentId, gstNumber, invoiceDate, dueDate, purchaseAccount, ewayBillNumber, ewayBillList, selectedTags, checkedRecipients, toRecipients, vendorAddress, chargeData, charge1Value, taxTypeInfo, taxTypeName, freightTax, freightTaxData,
  } = formState;

  return (
    <div className="ant-row">
      <div className="ant-col-md-24">
        <div className="form__section">
          <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
            <PRZText text="PART A" className="form__section-title" />
            <div className="form__section-line" />
            <div style={{ display: 'flex', justifyContent: 'end', alignItems: 'center', gap: '2px' }}>
              <CurrencyConversionV2
                selectedCurrencyName={selectedCurrencyInfo}
                selectedCurrencyID={selectedCurrencyID}
                isAutomaticConversionRate={isAutomaticConversionRate}
                currencyConversionRate={currencyConversionRate}
                setCurrencyConversionRate={(val) =>
                  localDispatch({
                    type: ACTIONS.UPDATE_FIELD,
                    field: 'currencyConversionRate',
                    value: val,
                  })
                }
                setIsAutomaticConversionRate={(val) =>
                  localDispatch({
                    type: ACTIONS.UPDATE_FIELD,
                    field: 'isAutomaticConversionRate',
                    value: val,
                  })
                }
                setSelectedCurrencyName={(val) =>
                  localDispatch({
                    type: ACTIONS.UPDATE_FIELD,
                    field: 'selectedCurrencyInfo',
                    value: val,
                  })
                }
                setSelectedCurrencyID={(val) =>
                  localDispatch({
                    type: ACTIONS.UPDATE_FIELD,
                    field: 'selectedCurrencyID',
                    value: val,
                  })
                }
              />
              <CustomDocumentInputs
                customFields={cfGoodReceivingNotesDoc}
                updateCustomFields={(cf) =>
                  localDispatch({
                    type: ACTIONS.UPDATE_FIELD,
                    field: 'cfGoodReceivingNotesDoc',
                    value: cf,
                  })
                }
              />
            </div>
          </div>
          <div className="form__section-inputs mg-bottom-20">
            <div className="ant-row">
              <div className="ant-col-md-24">
                {!selectedPoForGrn && !selectedGRN && (
                  <div className="form__input-row" style={{ alignItems: 'center', marginBottom: '15px' }}>
                    <Radio.Group
                      disabled={createGRNLoading || updateGRNLoading || getDocCFV2GoodReceivingNotesLoading || grnId || selectedPoForGrn?.po_id}
                      onChange={(event) => {
                        const isAdhoc = event.target.value === 'ADHOC';
                        const isMultiPo = event.target.value === 'MULTIPO';

                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: {
                            grnTypeValue: event.target.value,
                            selectedSeller: null,
                            selectedPoValue: [],
                            vendorAddress: null,
                            selectedPo: null,
                            data: isAdhoc
                              ? [
                                {
                                  key: uuidv4(),
                                  child_taxes: [
                                    {
                                      tax_amount: 0,
                                      tax_type_name: '',
                                    },
                                  ],
                                  lineCustomFields: cfGoodReceivingNotesLine,
                                  quantity: 0,
                                  discount: 0,
                                },
                              ]
                              : [],
                            shippingAddress: null,
                            selectedTenantSeller: null,
                            formSubmitted: null,
                            invoiceNumber: '',
                            selectedCurrencyID: null,
                            selectedCurrencyInfo: null,
                            isAutomaticConversionRate: null,
                            currencyConversionRate: null,
                            tenantDepartmentId:
                              isAdhoc || isMultiPo ? user?.tenant_info?.default_store_id : null,
                            visibleLineCfs: cfGoodReceivingNotesLine,
                            selectedTenant: user?.tenant_info?.tenant_id,
                            selectedTenantTallyIntegrationId: user?.tenant_info?.it_id,
                            narration: '',
                            discountPercentage: null,
                            cfGoodReceivingNotesDoc:
                              CustomFieldHelpers.getCfStructure(
                                cfV2DocGoodReceivingNotes?.data?.document_custom_fields,
                                true
                              ) || [],
                            paymentTerms: 0,
                            paymentRemarks: '',
                          },
                        });
                      }}

                      value={grnTypeValue}
                      className="mg-top-5"
                    >
                      <Radio value="Purchase Order">Purchase Order</Radio>
                      <Radio value="ADHOC" disabled={!user?.tenant_info?.config__grn__allow_adhoc}>ADHOC</Radio>
                      <Radio value="MULTIPO">MULTI PO</Radio>
                    </Radio.Group>
                  </div>
                )}
              </div>
              <DocumentNumberSeqInput
                valueFromProps={grnNumber}
                updateCase={grnId}
                setInitialDocSeqNumber={(value) =>
                  localDispatch({
                    type: ACTIONS.UPDATE_FIELD,
                    field: 'initialGrnNumber',
                    value,
                  })
                }
                entityName="GOOD_RECEIVING_NOTE"
                docSeqId={docSeqId}
                tenantId={selectedTenant}
                onChangeFromProps={(event, newValue, seqId) => {
                  localDispatch({
                    type: ACTIONS.UPDATE_FIELDS,
                    payload: {
                      grnNumber: newValue ?? (event?.target?.value || ''),
                      docSeqId: seqId,
                    },
                  });
                }}
                docTitle="GRN#"
                formSubmitted={formSubmitted}
              />
              {grnTypeValue === 'ADHOC' &&
                <div className="ant-col-md-6">
                  <TenantSelector
                    selectedTenant={selectedTenant}
                    showSearch
                    onChange={(value) => {
                      getTenantsConfiguration(value, (tenantData) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELD,
                          field: 'isAdhocGrnAllowed',
                          value: !tenantData?.config__grn__allow_adhoc,
                        });
                      });

                      const copychargeData = [
                        {
                          chargeKey: uuidv4(),
                          charge_name: '',
                          charge_amount: 0,
                          chargesTaxData: {
                            child_taxes: [
                              {
                                tax_amount: 0,
                                tax_type_name: '',
                              },
                            ],
                          },
                        },
                      ];

                      localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: {
                          selectedTenant: value,
                          selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.it_id,
                          tenantDepartmentId: user?.user_tenants?.find((item) => item?.tenant_id === value)?.default_store_id,
                          sellerName: '',
                          sellerGst: '',
                          tenantSellerId: '',
                          vendorAddress: '',
                          finishedGoods: [{
                            key: uuidv4(), asset1: '', product_name: '', quantity: '', unitPrice: '', lot: 0, taxId: '', discount: 0,
                          }],
                          bomByProducts: [],
                          extraCharges: [],
                          bomLines: [],
                          productionRoutes: [],
                          currentTab: '/finished-goods',
                          selectedTenantSeller: '',
                          data: [{
                            key: uuidv4(),
                            asset1: '',
                            product_sku_name: '',
                            quantity: '',
                            selling_price: '',
                            taxId: '',
                            lot: '',
                            product_sku_id: null,
                            product_sku_info: null,
                            child_taxes: [{
                              tax_amount: 0,
                              tax_type_name: '',
                            }],
                            lineCustomFields: visibleLineCfs,
                          }],
                          paymentTerms: 0,
                          paymentRemarks: '',
                          gstNumber: '',
                          chargeData: copychargeData,
                          purchaseAccount: null,
                        },
                      });
                    }}
                    showAll={false}
                    title="Location"
                    customStyle={{ height: '28px', border: 'none' }}
                    labelClassName="form__input-row__label"
                    inputClassName="form__input-row__input"
                    containerClassName="form__input-row"
                    noDropdownAlign
                    placeholder="Select Business Unit"
                    includedTenants={Helpers.getTenantEntityPermission(
                      user?.user_tenants,
                      Helpers.permissionEntities.GOOD_RECEIVING,
                      Helpers.permissionTypes.CREATE
                    )}
                    disabled={grnId || selectedPoForGrn || selectedPo}
                  />
                </div>}
              {/* vendor selector */}
              {(grnId || selectedPoForGrn?.po_id) ? (
                <div className="ant-col-md-6">
                  <div className="form__input-row">
                    <PRZText
                      text="Vendor"
                      className="form__input-row__label"
                    />
                    <div className="form__input-row__input">
                      <PRZText
                        text={selectedPoForGrn ? selectedPoForGrn?.seller_info?.seller_name : selectedGRN?.tenant_seller_info?.seller_name || selectedPoForGrn?.seller_info?.seller_name}
                        style={{ padding: '7px' }}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="ant-col-md-6">
                  <SelectSellerV2
                    selectedSeller={selectedTenantSeller}
                    onChange={(value) => {
                      if (grnTypeValue === 'ADHOC') {
                        const docCf =
                          CustomFieldHelpers.postCfStructure(
                            cfGoodReceivingNotesDoc?.filter(
                              (item) => item?.isActive && item?.visible
                            )
                          ) || [];
                        const vendorCf =
                          value?.custom_field_values?.filter((item) => item?.is_active) || [];
                        const mergedCf =
                          CustomFieldHelpers.mergeCustomFields(docCf, vendorCf) || [];

                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: {
                            cfGoodReceivingNotesDoc: mergedCf,
                            paymentRemarks:
                              value?.seller_info?.default_payment_terms?.remark || '',
                            paymentTerms:
                              value?.seller_info?.default_payment_terms?.due_days || 0,
                          },
                        });
                      }

                      localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: {
                          selectedSeller: value,
                          selectedTenantSeller: value?.tenant_seller_id,
                          gstNumber: value?.seller_info?.gst_number,
                          selectedPoValue: [],
                          vendorAddress:
                            grnTypeValue === 'ADHOC' || grnTypeValue === 'MULTIPO'
                              ? value?.seller_info?.office_address_details
                              : null,
                          selectedPo: null,
                          shippingAddress:
                            grnTypeValue === 'MULTIPO'
                              ? user?.tenant_info?.default_shipping_address_info
                              : null,
                          tenantDepartmentId:
                            grnTypeValue !== 'ADHOC'
                              ? user?.tenant_info?.default_store_id
                              : tenantDepartmentId,
                          data:
                            grnTypeValue === 'ADHOC'
                              ? [
                                {
                                  key: uuidv4(),
                                  child_taxes: [
                                    {
                                      tax_amount: 0,
                                      tax_type_name: '',
                                    },
                                  ],
                                  lineCustomFields: cfGoodReceivingNotesLine,
                                },
                              ]
                              : [],
                          isAutomaticConversionRate:
                            user?.tenant_info?.global_config?.settings
                              ?.automatic_conversion_rate,
                          selectedCurrencyID: value?.org_currency_id,
                          selectedCurrencyInfo: value?.currency_info,
                          currencyConversionRate: user?.tenant_info?.global_config?.settings
                            ?.automatic_conversion_rate
                            ? value?.currency_info?.automatic_conversion_rate
                            : value?.currency_info?.conversion_rate,
                          paymentRemarks:
                            value?.seller_info?.default_payment_terms?.remark || '',
                          paymentTerms:
                            value?.seller_info?.default_payment_terms?.due_days || 0,
                        },
                      });

                      const excludePo = grnTypeValue === 'MULTIPO';
                      getInventoryLocations(
                        grnTypeValue !== 'ADHOC'
                          ? user?.tenant_info?.default_store_id
                          : tenantDepartmentId,
                        '',
                        null,
                        true,
                        null,
                        null,
                        () => {}
                      );

                      if (selectedTenant) {
                        getPurchaseOrdersV2({
                          query: {
                            org_id: user?.tenant_info?.org_id,
                            tenant_id: selectedTenant,
                            tenant_seller_id: value?.tenant_seller_id,
                            status: 'ISSUED',
                            page: 1,
                            limit: 30,
                            exclude_job_works_po: excludePo,
                            exclude_subcontractor_po: excludePo,
                          },
                        });
                      }
                    }}
                    containerClass="orgInputContainer form__input-row"
                    inputClassName={`form__input-row__input ${(formSubmitted && !selectedSeller) ? 'form__input-row__input-error' : ''}`}
                    labelClassName="form__input-row__label"
                    disabled={createGRNLoading || updateGRNLoading || grnId || selectedPoForGrn?.po_id}
                    showAddVendor
                    tenantId={selectedTenant}
                  />
                </div>
              )}
              {/* purchase order selector */}
              {((grnId || selectedPoForGrn?.po_id) && (selectedPurchaseOrder?.po_number || selectedPoForGrn?.po_number)) ? (
                <div className="ant-col-md-6">
                  <div className="form__input-row">
                    <PRZText
                      text="Purchase Order"
                      className="form__input-row__label"
                    />
                    <div className="form__input-row__input">
                      <PRZText
                        text={`${selectedPurchaseOrder?.po_number || selectedPoForGrn?.po_number} `}
                        style={{ padding: '7px' }}
                      />
                    </div>
                  </div>
                </div>
              ) : ((grnTypeValue === 'Purchase Order' || grnTypeValue === 'MULTIPO') ?
                (
                  <div className="ant-col-md-6">
                    <div className="form__input-row">
                      <div className="form__input-row__label">
                        Select Order
                      </div>
                      <div>
                        <SelectOrdersForGRN
                          disabled={!selectedTenantSeller || createGRNLoading || updateGRNLoading}
                          selectedPoValue={selectedPoValue}
                          mode={grnTypeValue === 'MULTIPO' ? 'multiple' : 'single'}
                          onChange={grnTypeValue === 'MULTIPO' ? handleMultiPoChange : handlePoChange}
                          purchaseOrders={purchaseOrdersV2}
                          isDataMaskingPolicyEnable={isDataMaskingPolicyEnable}
                          isHideCostPrice={isHideCostPrice}
                          MONEY={MONEY}
                          grnTypeValue={grnTypeValue}
                          selectedTenantSeller={selectedTenantSeller}
                          selectedTenant={selectedTenant}
                          loading={getPurchaseOrdersV2Loading || createGRNLoading || updateGRNLoading}
                          getPurchaseOrdersV2={getPurchaseOrdersV2}
                          inputClassName={`orders-selector ${formSubmitted && !selectedPoValue ? 'form__input-row__input-error' : ''}`}
                        />
                        {grnTypeValue === 'Purchase Order' ? (
                          selectedPo && (
                            <PRZText
                              text="View Purchase Order"
                              className="create-grn__view-po-button_new"
                              onClick={() => {
                                const win = window.open(`/approval?type=po&id=${selectedPo?.po_id}`, '_blank');
                                win.focus();
                              }}
                            />
                          )
                        ) : (
                          selectedPoValue?.length > 0 && (
                            <PRZText
                              text="View Purchase Order"
                              className="create-grn__view-po-button_new"
                              onClick={() => {
                                localDispatch({
                                  type: ACTIONS.UPDATE_FIELDS,
                                  payload: {
                                    openDrawerOfMultipleSelectedPos: true,
                                  },
                                });
                              }}
                            />
                          ))}
                      </div>
                    </div>
                  </div>
                )
                : '')}
              {/* department selector */}
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText text="Department" className="form__input-row__label" />
                  <div className="form__input-row__input">
                    <SelectDepartment
                      hideTitle
                      tenantId={selectedTenant || selectedPoForGrn?.tenant_id || selectedPo?.tenant_id || selectedGRN?.tenant_id}
                      selectedDepartment={tenantDepartmentId}
                      noDropdownAlign
                      onChange={(value) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { tenantDepartmentId: value?.tenant_department_id },
                        });

                        getInventoryLocations(value?.tenant_department_id, '', null, true, null, null, (locationData) => {
                          const newLocationData = locationData?.inventory_location?.[0];
                          if (newLocationData) {
                            const updatedGrnTableData = data?.map((item) => {
                              // Update the selectedBatch details
                              const updatedSelectedBatch = {
                                ...item.selectedBatch,
                                inventory_location_id: newLocationData.inventory_location_id,
                                inventory_location_path: newLocationData.inventory_location_path,
                              };
                              // Update the first available batch details
                              const updatedAvailableBatches = item?.available_batches?.map((batch, index) => {
                                if (index === 0) {
                                  return {
                                    ...batch,
                                    inventory_location_id: newLocationData.inventory_location_id,
                                    inventory_location_path: newLocationData.inventory_location_path,
                                  };
                                }
                                return batch;
                              });
                              // Update the first multiple batch info
                              const updatedMultipleBatchInfo = item?.multiple_batch_info?.map((batch, index) => {
                                if (index === 0) {
                                  return {
                                    ...batch,
                                    inventory_location_id: newLocationData.inventory_location_id,
                                    inventory_location_path: newLocationData.inventory_location_path,
                                  };
                                }
                                return batch;
                              });
                              return {
                                ...item,
                                selectedBatch: updatedSelectedBatch,
                                available_batches: updatedAvailableBatches,
                                multiple_batch_info: updatedMultipleBatchInfo,
                              };
                            });
                            localDispatch({
                              type: ACTIONS.UPDATE_FIELDS,
                              payload: {
                                data: updatedGrnTableData,
                                inventoryLocationId: newLocationData.inventory_location_id,
                                inventoryLocationPath: newLocationData.inventory_location_path,
                              },
                            });
                          }
                        });
                      }}
                      tenentLevelDepartment
                      emptyNotAllowed
                      customStyle={{
                        border: '1px solid rgba(68, 130, 218, 0.25)',
                        borderRadius: '4px',
                        height: '28px',
                        padding: '0px',
                        marginBottom: '10px',
                      }}
                      loading={createGRNLoading || updateGRNLoading}
                      disabled={createGRNLoading || updateGRNLoading || selectedPoForGrn || selectedGRN || selectedPo}
                      labelClassName="mo-form__input-row__label"
                      inputClassName="orgFormInput input"
                    />
                  </div>
                </div>
              </div>
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText
                    text="GST Number"
                    className="form__input-row__label"
                  />
                  <PRZInput
                    type="text"
                    disabled={
                      createGRNLoading
                      || updateGRNLoading
                      || user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.gst_details_in_transaction === 'AS_PER_MASTER'
                    }
                    wrapperClassName="orgInputContainer form__input-row__input"
                    className="orgFormInput input"
                    placeholder=""
                    onChange={(event) => {
                      localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: { gstNumber: event.target.value },
                      });
                    }}
                    value={gstNumber}
                  />
                </div>
              </div>
              {/* vendor invoice number  */}
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText
                    text="Vendor Invoice#"
                    className="form__input-row__label"
                    required={user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory}
                  />
                  {/* <SettingsInfo
                      settingName="Keep invoice number of the vendor mandatory"
                      path="/admin/configuration?tab=%2Fadmin-config%2Fgoods-receiving&subTab=%2Fadmin-config%2Fgoods-receiving%2Fgeneral"
                    /> */}
                  <PRZInput
                    name="invoice number"
                    type="text"
                    wrapperClassName={'orgInputContainer form__input-row__input'}
                    className={`orgFormInput input ${(formSubmitted && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.grn_invoice_number_mandatory && !invoiceNumber) ? 'form__input-row__input-error' : ''}`}
                    placeholder=""
                    onChange={(event) => {
                      localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: { invoiceNumber: event.target.value },
                      });
                    }}
                    value={invoiceNumber}
                  />
                </div>
              </div>
              {/* grn date */}
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <div className="form__input-row__label">
                    GRN Date
                    <span style={{ color: 'red' }}>{'  *'}</span>
                  </div>
                  <div className={`form__input-row__input ${(formSubmitted && !grnDate) ? 'form__input-row__input-error' : ''}`}>
                    <DatePicker
                      value={dayjs(grnDate, 'DD-MM-YYYY')}
                      onChange={(value) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { grnDate: value },
                        });
                      }}
                      disabledDate={
                        (d) => !d
                          || (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.allow_future_date_grn ? null : d.isAfter(dayjs().add(1, 'days').format('YYYY-MM-DD')))
                          || (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.allow_past_date_grn ? null : d.isSameOrBefore(dayjs().format('YYYY-MM-DD')))
                      }
                      disabled={createGRNLoading || updateGRNLoading}
                      format="DD-MM-YYYY"
                      allowClear={false}
                      style={{
                        border: '1px solid rgba(68, 130, 218, 0.2)',
                        borderRadius: '2px',
                        height: '28px',
                        padding: '1px 3px',
                        width: '100%',
                        background: 'white',
                        marginBottom:
                          formSubmitted && !grnDate ? '0px' : '10px',
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText
                    text="Invoice Date"
                    className="form__input-row__label"
                  />
                  <div className={`form__input-row__input ${(formSubmitted && !invoiceDate) ? 'form__input-row__input-error' : ''}`}>
                    <DatePicker
                      value={invoiceDate}
                      onChange={(value) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { invoiceDate: value },
                        });
                      }}
                      disabled={createGRNLoading || updateGRNLoading}
                      format="DD-MM-YYYY"
                      style={{
                        // border: (formSubmitted && !deliveryDate && deliveryDateMandatory) ? '1px solid red' : '1px solid rgba(68, 130, 218, 0.2)',
                        borderRadius: '2px',
                        height: '28px',
                        padding: '1px 3px',
                        width: '100%',
                        background: 'white',
                        // marginBottom: formSubmitted && !deliveryDate ? '0px' : '10px',
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className="ant-col-md-6">
                <div className="form__input-row">
                  <PRZText
                    text="Due Date"
                    className="form__input-row__label"
                  />
                  <div className={`form__input-row__input ${(formSubmitted && !dueDate) ? 'form__input-row__input-error' : ''}`}>
                    <DatePicker
                      value={dueDate}
                      onChange={(value) => {
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { dueDate: value },
                        });
                      }}
                      disabled={createGRNLoading || updateGRNLoading}
                      format="DD-MM-YYYY"
                      style={{
                        border: (formSubmitted && !dueDate) ? '1px solid red' : '1px solid rgba(68, 130, 218, 0.2)',
                        borderRadius: '2px',
                        height: '28px',
                        padding: '1px 3px',
                        width: '100%',
                        background: 'white',
                        marginBottom: formSubmitted && !dueDate ? '0px' : '10px',
                      }}
                    />
                  </div>
                </div>
              </div>
              {user?.tenant_info?.purchase_account_selection === 'FROM_GRN' && !isApInvoiceEnabled && (
                <div className="ant-col-md-6">
                  <div className="form__input-row">
                    <PRZText text="Purchase Account" className="form__input-row__label" required />
                    <div className={`form__input-row__input ${formSubmitted && !purchaseAccount ? 'form__input-row__input-error' : ''}`}>
                      <PRZSelect
                        value={purchaseAccount}
                        onChange={(value) => {
                          localDispatch({
                            type: ACTIONS.UPDATE_FIELDS,
                            payload: { purchaseAccount: value },
                          });
                        }}
                        loading={createGRNLoading || updateGRNLoading}
                        disabled={createGRNLoading}
                      >
                        {purchaseAccountList?.map((item) => (
                          <Option key={item.purchase_account_name} value={item.purchase_account_name}>
                            {' '}
                            {item.purchase_account_name}
                          </Option>
                        ))}
                      </PRZSelect>

                    </div>
                  </div>
                </div>
              )}
              {getLineTotals({
                data,
                chargeData,
                charge1Value,
                grnTypeValue,
                taxTypeInfo,
                taxTypeName,
                freightTax,
                freightTaxData,
              }).totalAmount >= 50_000 && user?.tenant_info?.country_code === 'IN'
                ? (
                  <React.Fragment>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <PRZText
                          text="e-Way Bill number"
                          className="form__input-row__label"
                          required={user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory}
                        />
                        <PRZInput
                          name="Ewaybill number"
                          type="text"
                          wrapperClassName={`orgInputContainer form__input-row__input ${(formSubmitted && user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory && !ewayBillNumber) ? 'form__input-row__input-error' : ''}`}
                          className="orgFormInput input"
                          onChange={(event) => {
                            localDispatch({
                              type: ACTIONS.UPDATE_FIELDS,
                              payload: { ewayBillNumber: event.target.value },
                            });
                          }}
                          maxlength="100"
                          value={ewayBillNumber}
                          disabled={createGRNLoading}
                        />
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <PRZText
                          text="e-Way Bill Attachment"
                          className="form__input-row__label"
                          required={user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.is_e_way_bill_mandatory}
                        />
                        <Upload
                          action={constants.UPLOAD_FILE}
                          // listType="picture-card"
                          fileList={ewayBillList}
                          disabled={createGRNLoading}
                          multiple
                          onChange={(ewayBillListData) => {
                            localDispatch({
                              type: ACTIONS.UPDATE_FIELDS,
                              payload: { ewayBillList: ewayBillListData?.fileList?.map((item) => ({
                                ...item,
                                url: item?.response?.response?.location || item?.url,
                              })) },
                            });
                          }}
                        >
                          {ewayBillList?.length >= 1 ? null : uploadButtonFormLevel}
                        </Upload>
                      </div>
                    </div>
                  </React.Fragment>
                ) : <React.Fragment />}
              <div className="ant-col-md-6">
                <div className="form__input-row ">
                  <div className="form__input-row__label">
                    Labels
                  </div>
                  <TagSelector
                    hideTitle
                    entityType="GRN"
                    selectedTags={selectedTags}
                    isMultiple
                    showSearch
                    onChange={(value) => {
                      localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: { selectedTags: value },
                      });
                    }}
                    placeholder="Select Tags"
                    isForm
                    containerWrapper="form__input-row__input"
                    disabled={createGRNLoading || updateGRNLoading}
                    maxTagCount="responsive"
                  />
                </div>
              </div>
              {!isApInvoiceEnabled && (<div className="ant-col-md-6">
                <SelectPaymentTerm
                  selectedPaymentTerm={paymentTerms}
                  onChange={(value) => {
                    localDispatch({
                      type: ACTIONS.UPDATE_FIELDS,
                      payload: { paymentTerms: value?.due_days },
                    });
                  }}
                  callback={(value) => {
                    localDispatch({
                      type: ACTIONS.UPDATE_FIELDS,
                      payload: { paymentTerms: Number(value) },
                    });
                  }}
                  containerClassName="orgInputContainer form__input-row"
                  inputClassName=" form-seller__selector form__input-row__input"
                  labelClassName="form__input-row__label"
                  showError={formSubmitted && !paymentTerms}
                  disabled={
                    createGRNLoading || updateGRNLoading
                  }
                  showAddPaymentTerm
                  placeholder="Select Payment Term"
                />
              </div>)}
              {!isApInvoiceEnabled && (<div className="ant-col-md-6">
                <SelectPaymentRemark
                  selectedPaymentRemark={paymentRemarks}
                  onChange={(value) => {
                    localDispatch({
                      type: ACTIONS.UPDATE_FIELDS,
                      payload: { paymentRemarks: value?.message },
                    });
                  }}
                  callback={(value) => {
                    localDispatch({
                      type: ACTIONS.UPDATE_FIELDS,
                      payload: { paymentRemarks: value },
                    });
                  }}
                  containerClassName="orgInputContainer form__input-row"
                  inputClassName=" form-seller__selector form__input-row__input"
                  labelClassName="form__input-row__label"
                  showError={formSubmitted && !paymentRemarks}
                  disabled={
                    createGRNLoading || updateGRNLoading
                  }
                  showAddPaymentRemark
                  placeholder="Select Payment Term"
                />
              </div>)}
              <CustomFieldV3
                customFields={cfGoodReceivingNotesDoc}
                formSubmitted={formSubmitted}
                customInputChange={(value, cfId) => customInputChange(value, cfId)}
                wrapperClassName="ant-col-md-6"
                containerClassName="form__input-row"
                labelClassName="form__input-row__label"
                inputClassName="form__input-row__input"
                errorClassName="form__input-row__input-error"
                disableCase={createGRNLoading || updateGRNLoading
                  || (grnTypeValue === 'Purchase Order' && !selectedPoValue && !selectedPoForGrn?.po_id)
                }
                hideTitle
                isCarryForward={true}
              />
            </div>
          </div>
        </div>
        {workOrder && !selectedPoForGrn?.subcontractor_bears_rm_cost && !selectedPoForGrn?.is_job_works_po && (
          <div style={{ marginBottom: '10px' }}>
            {!selectedPoForGrn?.is_invoice_issued ? (
              <Alert
                description="You have not issued the raw material for this work order. This may cause mismatch in the costing of goods receipt."
                type="warning"
                showIcon
              />
            ) : (
              <Alert
                description="You have already issued raw materials. The cost of raw materials issued for this work order will automatically be added in the landed cost of this received goods."
                type="success"
                showIcon
              />
            )}
          </div>
        )}
        <div className="form__section">
          <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
            <PRZText text="PART B" className="form__section-title" />
            <div className="form__section-line" />
          </div>
          <div className="form__section-inputs">
            <div className="ant-row">
              {(grnTypeValue !== 'ADHOC') && (!grnId && !selectedPoForGrn?.po_id) && (
                <div className="ant-col-md-8">
                  <div className="form__input-row">
                    <PRZText
                      text="Delivery Address"
                      className="form__input-row__label"
                      required
                    />
                    <div className={`form__input-row__address__wrapper ${(formSubmitted && !shippingAddress) ? 'form__input-row__address-error' : ''} `}>
                      <div className="form__input-row__address">
                        {shippingAddress && (
                          <div className="form__input-row__address-info">
                            <div className="form__input-row__address-l1">
                              {shippingAddress?.address1}
                            </div>
                            <div className="form__input-row__address-l2">
                              {`${shippingAddress?.city}, ${shippingAddress?.state}, ${shippingAddress?.postal_code}`}
                            </div>
                          </div>
                        )}
                        {!shippingAddress && (
                          <PRZText
                            text="Select address.."
                            className="form__input-row__address-placeholder"
                          />
                        )}
                      </div>
                      {formSubmitted && !shippingAddress && (
                        <div className="input-error">
                          *Please select delivery address
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              {(!grnId) && (
                <div className="ant-col-md-8">
                  <div className="form__input-row">
                    <PRZText
                      text="Vendor's Address"
                      className="form__input-row__label"
                      required={grnTypeValue === 'ADHOC'}
                    />
                    <div className={`form__input-row__address__wrapper ${(formSubmitted && !vendorAddress && grnTypeValue === 'ADHOC') ? 'form__input-row__address-error' : ''} `}>
                      <div className="form__input-row__address">
                        {vendorAddress && (
                          <div className="form__input-row__address-info">
                            <div className="form__input-row__address-l1">
                              {vendorAddress?.address1}
                            </div>
                            <div className="form__input-row__address-l2">
                              {`${vendorAddress?.city}, ${vendorAddress?.state}, ${vendorAddress?.postal_code}, ${vendorAddress?.country}`}
                            </div>
                          </div>
                        )}
                        {!vendorAddress && (
                          <PRZText
                            text="Select address.."
                            className="form__input-row__address-placeholder"
                          />
                        )}
                        {(
                          <div
                            className="form__input-row__address-icon"
                            onClick={() => {
                              localDispatch({
                                type: ACTIONS.UPDATE_FIELDS,
                                payload: { showAddressDrawer: true, selectedAddressType: 'SELLER' },
                              });
                            }}
                          >
                            <EditFilled />
                            {' '}
                            Update
                          </div>
                        )}
                      </div>
                      {formSubmitted && !vendorAddress && (
                        <div className="input-error">
                          *Please select vendor address
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              <div className="ant-col-md-24">
                <div className="form__input-row" style={{ marginTop: '10px' }}>
                  <Checkbox
                    disabled={createGRNLoading || updateGRNLoading}
                    checked={checkedRecipients}
                    onChange={() => {
                      localDispatch({
                        type: ACTIONS.UPDATE_FIELDS,
                        payload: { checkedRecipients: !checkedRecipients },
                      });
                    }}
                  />
                  <span
                    style={{
                      fontWeight: '500',
                      fontSize: '12px',
                      marginLeft: '5px',
                    }}
                  >
                    Send automatic email when order is issued
                  </span>
                </div>
              </div>
              {checkedRecipients && (
                <div className="ant-col-md-8">
                  <div className="form__input-row__input">
                    <PRZSelect
                      className={(formSubmitted && checkedRecipients && !toRecipients?.length) ? 'form__recipients__input-error' : ''}
                      mode="tags"
                      value={toRecipients}
                      filterOption={false}
                      maxTagCount="responsive"
                      onChange={(value) => {
                        const recipients = [];
                        for (let i = 0; i < value?.length; i++) {
                          if (Helpers.validateEmail(value[i])) {
                            recipients.push(value[i]);
                          }
                        }
                        localDispatch({
                          type: ACTIONS.UPDATE_FIELDS,
                          payload: { toRecipients: recipients },
                        });
                      }}
                      disabled={createGRNLoading || updateGRNLoading}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default memo(GRNFormDocFields);