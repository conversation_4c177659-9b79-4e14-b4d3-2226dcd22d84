import { message } from 'antd';
import FormHelpers from '../../../../helpers/FormHelpers';
import { getDataSource } from './helpers';

const ERROR_MESSAGES = {
  requiredField: (fieldName) => `Error in ${fieldName} field. Select/Enter a valid value then proceed.`,
};

const validateDocLevelError = ({
  cfInvoiceDoc, invoiceDate, selectedOrderForInvoice, selectedCustomer, billingAddress, shippingAddress, shipFrom, billFrom, checkedRecipients, toRecipients, chargeData, moInfo, selectedWorkOrder, selectedPriceList, isPriceListMandatory, jwRmLines, user, gstNumber,
}) => {
  const errors = [];

  if (!invoiceDate) {
    errors.push({
      name: "Invoice Date",
      value: "",
      message: ERROR_MESSAGES.requiredField("Invoice date"),
    });
  }

  if (!selectedOrderForInvoice && !selectedCustomer && (!moInfo && !jwRmLines?.jw_number)) {
    errors.push({
      name: "Customer",
      value: "",
      message: ERROR_MESSAGES.requiredField("Customer"),
    });
  }

  if (gstNumber && String(gstNumber)?.length !== 15) {
    errors.push({
      name: "GST Number",
      value: 0,
      message: "Error in GST Number. Enter Valid GST Number.",
    });
  }

  if (!billingAddress && (!moInfo && !jwRmLines?.jw_number)) {
    errors.push({
      name: "Bill To",
      value: "",
      message: ERROR_MESSAGES.requiredField("Bill To"),
    })
  }

  if (!shippingAddress && (!moInfo && !jwRmLines?.jw_number)) {
    errors.push({
      name: "Ship To",
      value: "",
      message: ERROR_MESSAGES.requiredField("Ship To"),
    })
  }
  if (!billFrom && (!moInfo && !jwRmLines?.jw_number)) {
    errors.push({
      name: "Bill From",
      value: "",
      message: ERROR_MESSAGES.requiredField("Bill From"),
    })
  }
  if (!shipFrom && (!moInfo && !jwRmLines?.jw_number)) {
    errors.push({
      name: "Ship From",
      value: "",
      message: ERROR_MESSAGES.requiredField("Ship From"),
    })
  }

  if (moInfo && !selectedWorkOrder) {
    errors.push({
      name: "Work Order",
      value: "",
      message: ERROR_MESSAGES.requiredField("Work Order"),
    })
  }

  if (checkedRecipients) {
    if (toRecipients?.length === 0) {
      errors.push({
        name: "Email",
        value: "",
        message: ERROR_MESSAGES.requiredField("Recipient Email"),
      })
    }
  }

  if (cfInvoiceDoc && (!moInfo && !jwRmLines?.jw_number)) {
    cfInvoiceDoc.forEach((field) => {
      if (field.isRequired && field?.isActive) {
        errors.push({
          name: field?.fieldName,
          value: field?.fieldValue,
          message: ERROR_MESSAGES.requiredField(field?.fieldName),
        });
      }
    });
  }

  if (chargeData) {
    chargeData.map((charge) => {
      if (Number(charge?.charge_amount) === 0 || !charge?.charge_name) {
        errors.push({
          name: charge?.charge_name,
          value: charge?.value,
          message: ERROR_MESSAGES.requiredField(charge?.charge_name || 'Charges'),
        });
      }
    });
  }

  if (isPriceListMandatory && !selectedPriceList) {
    errors.push({
      name: "Price List",
      value: "",
      message: `Error in Price List. Select a Price List then proceed.`,
    });
  }

  return errors;
};

const validateLineLevelError = ({
  data, cfInvoiceLine, user, selectedOrderForInvoice, selectedInvoice
}) => {
  const errors = [];
  const allowExcessQty = user?.tenant_info?.sales_config?.sub_modules?.invoice?.settings?.flexible_qty_from_so;
  if (getDataSource(data)) {
    getDataSource(data).forEach((item) => {
      if (!item?.product_sku_name) {
        errors.push({
          name: 'Products',
          value: 0,
          message: 'Error in Product. Select a product to continue.',
        });
      } else {
        if (
          !item?.quantity
          || ((selectedOrderForInvoice || selectedInvoice?.order_id) && item?.quantity > item?.pending_quantity_so && !allowExcessQty)
        ) {
          let errorMessage = '';
          if (!item?.quantity) {
            errorMessage = ERROR_MESSAGES.requiredField(`Quantity for Product '${item?.product_sku_info?.internal_sku_code || ''}'`);
          } else if ((selectedOrderForInvoice || selectedInvoice?.order_id) && item?.quantity > item?.pending_quantity_so && !allowExcessQty) {
            errorMessage = `Quantity for Product '${item?.product_sku_info?.internal_sku_code || ''}' cannot exceed Pending Quantity (${item?.pending_quantity_so}).`;
          }

          errors.push({
            name: `Quantity in Product ${item?.product_sku_info?.internal_sku_code || ''}`,
            value: 0,
            message: errorMessage,
          });
        }

        if (!item?.taxInfo) {
          errors.push({
            name: `Tax in Product ${item?.product_sku_info?.internal_sku_code || ''}`,
            value: 0,
            message: ERROR_MESSAGES.requiredField(`Tax for Product '${item?.product_sku_info?.internal_sku_code || ''}'`),
          });
        }
        if (cfInvoiceLine) {
          item?.lineCustomFields.filter((field) => field?.fieldName !== 'Quantity' && field?.fieldName !== 'Rate')?.forEach((field) => {
            if (field.isRequired && field?.isActive) {
              errors.push({
                name: field?.fieldName,
                value: field?.fieldValue,
                message: ERROR_MESSAGES.requiredField(`${field?.fieldName} for Product '${item?.product_sku_info?.internal_sku_code || ''}'`),
              });
            }
          });
        }
      }
    });
  }

  return errors;
};

const InvoiceErrorList = (params) => {
  const docLevelErrors = validateDocLevelError(params);
  const lineLevelErrors = validateLineLevelError(params);

  return FormHelpers.errorChecker({ docLevelErrors, lineLevelErrors });
};

export default InvoiceErrorList;
