import React, { Component, Fragment } from 'react';
import CustomFieldLine from '@Components/Common/CustomFieldLine';
import ViewCustomFieldLine from '@Components/Common/CustomField/ViewCustomFieldLine';
import { DEFAULT_CUR_ROUND_OFF } from "@Apis/constants";

class CustomFieldHelpers {
  getDocumentCf = (documentCustomFields, existingCustomFields) => {
    let existingCustomFieldsCopy = JSON.parse(JSON.stringify(existingCustomFields));
    // New Custom  Fields
    const newCustomField = documentCustomFields?.filter((customField) => existingCustomFieldsCopy?.findIndex((x) => x?.cf_id === customField?.cf_id) === -1);
    // Old Updated Fields
    // const oldUpdatedCustomField = documentCustomFields?.filter((customField) => existingCustomFieldsCopy?.findIndex((x) => x?.cf_id === customField?.cf_id) !== -1);
    const oldUpdatedCustomField = documentCustomFields
      ?.filter((customField) => existingCustomFieldsCopy?.findIndex((x) => x?.cf_id === customField?.cf_id) !== -1)
      ?.map((customField) => {
        const matchingField = existingCustomFieldsCopy?.find((x) => x?.cf_id === customField?.cf_id);
        return {
          ...customField,
          is_required: matchingField?.is_required, // Override with the value from existingCustomFieldsCopy
        };
      });
    // updating old field
    existingCustomFieldsCopy = existingCustomFieldsCopy?.map((customField) => {
      const index = oldUpdatedCustomField?.findIndex((x) => x?.cf_id === customField?.cf_id);
      if (index !== -1) {
        return {
          ...customField,
          fieldType: oldUpdatedCustomField[index]?.field_type,
          is_required: oldUpdatedCustomField[index]?.is_required,
          visible: oldUpdatedCustomField[index]?.hasOwnProperty('visible') ? oldUpdatedCustomField[index]?.visible : existingCustomFieldsCopy[index]?.hasOwnProperty('visible') ? existingCustomFieldsCopy[index]?.visible : oldUpdatedCustomField[index]?.is_active,
          field_name: oldUpdatedCustomField[index]?.field_name,
          default_data: oldUpdatedCustomField[index]?.default_data,
          placeholder: oldUpdatedCustomField[index]?.placeholder,
          isPrintable: oldUpdatedCustomField[index]?.is_printable,
          defaultExpression: oldUpdatedCustomField[index]?.default_expression,
          dependentFields: oldUpdatedCustomField[index]?.dependent_fields,
          isSystemField: oldUpdatedCustomField[index]?.is_system_field,
          cfEntityId: oldUpdatedCustomField[index]?.cf_entity_id,
        };
      }
      return {
        ...customField,
      };
    });

    // Merged Both Custom  Fields
    let newData;
    if (existingCustomFieldsCopy?.length && !newCustomField?.length) {
      newData = [...existingCustomFieldsCopy];
    }
    if (!existingCustomFieldsCopy?.length && newCustomField?.length) {
      newData = [...existingCustomFieldsCopy, ...newCustomField];
    }
    if (existingCustomFieldsCopy?.length && newCustomField?.length) {
      newData = [...existingCustomFieldsCopy, ...newCustomField];
    }
    // Formatted data
    return newData?.map((customField) => ({
      cfId: customField?.cf_id,
      isActive: customField?.is_active || customField?.visible,
      fieldType: customField?.field_type,
      isRequired: customField?.is_required,
      fieldName: customField?.field_name,
      defaultData: customField?.default_data,
      placeholder: customField?.placeholder,
      fieldValue: customField?.field_value,
      visible: customField?.hasOwnProperty('visible') ? customField?.visible : customField?.is_active,
      isPrintable: customField?.is_printable,
      defaultExpression: customField?.default_expression,
      dependentFields: customField?.dependent_fields,
      isSystemField: customField?.is_system_field,
      cfEntityId: customField?.cf_entity_id,
      isEditable: documentCustomFields?.find((x) => x?.cf_id === customField?.cf_id)?.is_editable_on_update,
      isCarryForward: documentCustomFields?.find((x) => x?.cf_id === customField?.cf_id)?.is_editable_while_carrying_forward,
    }));
  }

  getCfStructure = (customFieldsData, doc) => {
    const customFields = customFieldsData?.map((customField) => ({
      cfId: customField?.cf_id,
      fieldType: customField?.field_type,
      isRequired: customField?.is_required,
      isActive: customField?.is_active,
      fieldName: customField?.field_name,
      defaultData: customField?.default_data,
      placeholder: customField?.placeholder,
      fieldValue: customField?.field_type?.toUpperCase() !== 'ATTACHMENT' ? customField?.field_value : customField?.field_value?.map((field) => {
        return {
          ...field,
          url: field?.url,
          name: field?.name,
          type: field?.type,
          uid: field?.uid
        }
      }),
      visible: customField?.hasOwnProperty('visible') ? customField?.visible : customField?.is_active,
      isPrintable: customField?.is_printable,
      dependentFields: customField?.dependent_fields,
      defaultExpression: customField?.default_expression,
      isSystemField: customField?.is_system_field,
      cfEntityId: customField?.cf_entity_id,
      isEditable: customField?.is_editable_on_update,
      isCarryForward: customField?.is_editable_while_carrying_forward,
    }));
    return customFields;
  }

  postCfStructure = (customFieldsData) => {
    if (!Array.isArray(customFieldsData) || customFieldsData.length === 0) {
      return [];
    }

    return customFieldsData?.map((customField) => ({
      cf_id: customField?.cfId,
      field_name: customField?.fieldName,
      field_value: customField?.fieldValue,
      field_type: customField?.fieldType,
      is_required: customField?.isRequired,
      is_active: customField?.isActive,
      default_data: customField?.defaultData,
      placeholder: customField?.placeholder,
      is_printable: customField?.isPrintable,
      visible: customField?.visible,
      default_expression: customField?.defaultExpression,
      dependent_fields: customField?.dependentFields,
      is_system_field: customField?.isSystemField,
      cf_entity_id: customField?.cfEntityId,
      isEditable: customField?.isEditable,
      isCarryForward: customField?.isCarryForward,
    }));
  };

  isCfValid = (customFields) => !customFields?.filter((customField) => customField.visible && customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length

  renderCustomLineColumns = (isChild, lineCustomFields, visibleColumns, customLineInputChange, formSubmitted, quickUpdateApi, entityLineName, isEditable, activityLogUpdater, entityId, lineProps, isUpdate, isCarryForward, cfV2Doc) => {
    if (customLineInputChange) {
      const copyData = lineCustomFields
        ?.filter((item) => !['Rate', 'Quantity', 'Invoice Quantity'].includes(item?.fieldName))
        ?.map((item) => ({
          title: item?.fieldName,
          visible: visibleColumns[item?.fieldName?.toUpperCase()]?.visible && !isChild,
          render: (record) => (
            <div style={{ width: '120px' }}>
              <CustomFieldLine
                customFields={record?.lineCustomFields || []}
                cfId={item?.cfId}
                labelClassName="orgFormLabel"
                inputClassName="orgFormInput"
                customLineInputChange={(value) => {
                  customLineInputChange(value, record?.key, record?.fg_tenant_product_id, lineProps)
                }}
                formSubmitted={formSubmitted}
                isUpdate={isUpdate}
                isCarryForward={isCarryForward}
              />
            </div>
          ),
        }));
      return copyData || [];
    }
    const copyData = lineCustomFields
      ?.filter((item) => !['Rate', 'Quantity', 'Invoice Quantity'].includes(item?.field_name))
      ?.map((item) => ({
        title: item?.field_name,
        visible: visibleColumns[item?.field_name?.toUpperCase()]?.visible && !isChild,
        render: (record) => (
          <div style={{ maxWidth: '120px' }}>
            <ViewCustomFieldLine
              cfId={item?.cf_id}
              customFields={record?.lineCustomFields}
              quickUpdateApi={quickUpdateApi}
              entityLineName={entityLineName}
              lineId={record?.[entityLineName]}
              isEditable={isEditable}
              activityLogUpdater={activityLogUpdater}
              entityId={entityId}
              isUpdate={isUpdate}
              cfV2Doc={cfV2Doc}
            />
          </div>
        ),
        onCell: (item) => {
          if (item?.fg_product) {
            return { colSpan: 0 };
          }
          return {};
        },
      }));
    return copyData || [];
  }

  // Function to transform cf data and update visibleColumns
  updateVisibleColumns = (cfData, visibleColumns) => {
    const updatedVisibleColumns = { ...visibleColumns };
    const activeCfData = cfData?.filter((item) => item?.is_active && !['Rate', 'Quantity', 'Invoice Quantity'].includes(item?.field_name));
    for (let i = 0; i < activeCfData?.length; i++) {
      const fieldName = activeCfData[i]?.field_name?.toUpperCase();
      const isVisible = (() => {
        if (activeCfData[i]?.hasOwnProperty('visible')) {
          return activeCfData[i]?.is_required || activeCfData[i]?.visible;
        }
        return activeCfData[i]?.is_active;
      })();
      updatedVisibleColumns[fieldName] = {
        label: activeCfData[i]?.field_name,
        visible: isVisible,
        disabled: false,
        required: activeCfData[i]?.is_required,
      };
    }
    return updatedVisibleColumns;
  }

  updateCustomColumnValue = (updatedValue, currentCf, customFields, customLineInputChange) => {
    const cfMap = {};
    const recordCf = JSON.parse(JSON.stringify(customFields));
    for (let i = 0; i < recordCf?.length; i++) {
      const cf = recordCf[i];
      cfMap[cf?.cfId] = cf;
    }

    const evaluateExpression = (expression) => expression.replace(/{{cf_(\d+)}}/g, (match, p1) => {
      const field = cfMap[p1];
      return field ? (field?.fieldType === "NUMBERS" ? (Number(field.fieldValue) || 0) : field.fieldValue) : 0;
    });
    const updateCfValue = (value, cf) => {
      try {
        // Update the value in the cfMap
        cfMap[cf.cfId].fieldValue = cfMap[cf.cfId].fieldType === 'NUMBERS' ? Number(value) : value;
        // Process each dependent field
        cf?.dependentFields?.forEach((dependentFieldId) => {
          const dependentField = cfMap[dependentFieldId];
          if (dependentField?.defaultExpression) {
            const expression = dependentField.defaultExpression;
            const evaluatedValue = eval(evaluateExpression(expression, cfMap));
            updateCfValue(evaluatedValue, dependentField);
          }
        });
        if (!cf?.dependentFields?.length) {
          customLineInputChange(Object?.values(cfMap));
        }
      } catch (e) {
        console.log('automatic calculation failure', e);
      }
    };
    updateCfValue(updatedValue, currentCf);
  };

  calculateValuesForDependsFields(fields) {
    // Helper function to evaluate mathematical expressions safely
    const evaluateExpression = (expression, fieldMap) => {
      // Replace the cf references (e.g., cf_4096) with their respective field values
      const sanitizedExpression = expression?.replace(/\{\{cf_(\d+)\}\}/g, (_, cfId) => {
        const id = parseInt(cfId);
        const fieldValue = fieldMap[id] ? parseFloat(fieldMap[id].fieldValue) : 0; // Fallback to 0
        return isNaN(fieldValue) ? 0 : fieldValue; // Ensure the fieldValue is a valid number
      });

      try {
        // Safely evaluate the expression using the Function constructor
        const result = new Function(`return (${sanitizedExpression});`)();
        return isNaN(result) ? 0 : result; // Return 0 if the result is not a number
      } catch (error) {
        console.error(`Error evaluating expression: ${sanitizedExpression}`, error);
        return 0; // If evaluation fails, return 0 as fallback
      }
    };

    // Create a map to easily access fields by cfId
    const fieldMap = fields?.reduce((map, field) => {
      map[field.cfId] = field;
      return map;
    }, {});

    // Loop over each field
    fields?.forEach(field => {
      // If defaultExpression is present
      if (field.defaultExpression) {
        // Evaluate the expression
        try {
          // Replace placeholders in the expression and evaluate the result
          const expression = evaluateExpression(field.defaultExpression, fieldMap);
          field.fieldValue = expression; // Assign the evaluated result to fieldValue
        } catch (error) {
          console.error(`Error setting fieldValue for field ${field.cfId}:`, error);
          field.fieldValue = undefined; // If there's an error, set fieldValue to undefined
        }
      }
    });

    return fields;
  }

  mergeCustomFields = (currentCustomFields, oldCustomFields, isUpdateCase) => {
    const currentCustomFieldsCopy = JSON.parse(JSON.stringify(currentCustomFields));
    const oldCustomFieldsCopy = JSON.parse(JSON.stringify(oldCustomFields));
    const mergedCustomFields = currentCustomFieldsCopy?.map((newField) => {
      const oldField = oldCustomFieldsCopy?.find((old) => old.cf_id === newField.cf_id);
      return isUpdateCase ? { ...newField, field_value: oldField?.field_value, visible: oldField?.hasOwnProperty('visible') ? oldField?.visible : newField?.is_active } : { ...newField, field_value: oldField?.field_value };
    });
    const caseConvertedCustomFields = mergedCustomFields?.map((customField) => ({
      cfId: customField?.cf_id,
      isActive: customField?.is_active || customField?.visible,
      fieldType: customField?.field_type,
      isRequired: customField?.is_required,
      fieldName: customField?.field_name,
      defaultData: customField?.default_data,
      placeholder: customField?.placeholder,
      fieldValue: customField?.field_value,
      visible: customField?.hasOwnProperty('visible') ? customField?.visible : customField?.is_active,
      isPrintable: customField?.is_printable,
      defaultExpression: customField?.default_expression,
      dependentFields: customField?.dependent_fields,
      isSystemField: customField?.is_system_field,
      cfEntityId: customField?.cf_entity_id,
      isEditable: customField?.hasOwnProperty('isEditable') ? customField?.isEditable : customField?.is_editable_on_update,
      isCarryForward: customField?.hasOwnProperty('isCarryForward') ? customField?.isCarryForward : customField?.is_editable_while_carrying_forward,
    }));
    return caseConvertedCustomFields;
  }

}

export default new CustomFieldHelpers();
