// 📦 React & Hooks
import React, { Fragment, useEffect, useState } from 'react';

// 🚦 Routing
import { <PERSON>, withRouter } from 'react-router-dom';

// 🧩 Redux
import { connect } from 'react-redux';

// 📅 Utilities
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';

// 🧠 Helpers & Constants
import Helpers from '@Apis/helpers';
import Constants, { SERVICE_ENDPOINT } from '@Apis/constants';

// 🧾 Redux Actions
import AttachmentActions from '@Actions/attachmentActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import { UpdateAPInvoiceStatus, APInvoiceApprovalWorkflow } from '../../../../modules/purchase/accountPayableInvoice';
import TallyIntegrationActions from '@Actions/integrations/tallyIntegrationActions';

// 🔧 API Hooks
import { useApiQuery } from '../../../../apiV2/hooks/useApiQuery';
import { useApiMutation } from '../../../../apiV2/hooks/useApiMutation';

// 🎨 UI Components - H3 UI Kit
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';

// 🎛️ Common Components
import Attachment from '@Components/Common/Attachment';
import ViewCustomFields from '@Components/Common/CustomField/ViewCustomFields';
import ActivityLog from '@Components/Common/ActivityLog';
import WorkFlowTimeLine from '@Components/Common/WorkFlowTimeLine';
import TagSelector from '@Components/Common/Selector/TagSelector';
import ViewPayment from '@Components/Common/ViewPayment';
import ViewLoadingSkull from '../../../Common/ViewLoadingSkull';
import DebitNoteForm from '@Components/Purchase/DebitNotes/DebitNoteForm';
import AllocateLandingPrice from './AllocateLandingPrice';
import HideComponent from '@Components/Common/RestrictedAccess/HideComponent';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import ReverseAllocateLandingPrice from './ReverseAllocateLandingPrice';

// 🧾 Payment Modules
import LinkOutgoingPayment from '../../../Payments/LinkPayment/LinkOutgoingPayment';
import RecordOutgoingPayment from '../../../Payments/RecordPayment/RecordOutgoingPayment';

// 📄 Module-Specific Components
import ViewAccountPayableInvoiceLines from './ViewAccountPayableInvoiceLines';

// 🧰 Ant Design Components
import { Popconfirm, notification, Dropdown, Menu, Drawer, Tooltip } from 'antd';

// 🎨 Icons
import { LoadingOutlined, EditOutlined, CaretRightOutlined, CaretDownOutlined, UserOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPrint } from '@fortawesome/free-solid-svg-icons';

// 🖼️ Static Assets
import { cdnUrl } from "@Utils/cdnHelper";

// 🖌️ Styles
import './style.scss';

function AccountPayableInvoiceView({
  user, isQuickView, match, getAttachmentByIdLoading, updateAttachmentLoading, updateAttachment, getAttachmentById, history, downloadDocument, MONEY, priceMasking, selectedAPInvoiceId, selectedTenantId, updateAPInvoiceStatus, apInvoiceApprovalWorkflow, apInvoiceApprovalWorkflowLoading, updateAPInvoiceStatusLoading, syncTallyGRN, pullTallyVendorPayment, syncTallyGRNLoading, pullTallyVendorPaymentLoading,
}) {
  const fixedMenuBar = user?.side_menu_bar_type === 'FIXED';
  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;
  const apiId = match?.params?.apiId || selectedAPInvoiceId;

  // using to get AP invoice BYId
  const { data: getSelectedAPI, isLoading, manualRefetch } = useApiQuery({
    key: ['selectedAPInvoice', apiId],
    url: `/account-payable-invoice/${apiId}`,
    params: {
      tenant_id: Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.READ).join(','),
      map_ap_invoice_payments: true,
    },
    enabled: !!apiId,
    staleTime: 0,
    select: (data) => data?.data?.[0],
    onManualRefetchSuccess: (refetch) => { },
  });

  const [state, setState] = useState({
    visibleColumns: {
      PRODUCT: {
        label: 'Product',
        visible: true,
        disabled: true,
      },
      QUANTITY: {
        label: 'Received',
        visible: true,
        disabled: true,
      },
      UNIT_PRICE: {
        label: 'Unit Price',
        visible: true,
        disabled: true,
      },
      LANDED_COST: {
        label: 'Landed Cost',
        visible: true,
        disabled: true,
      },
      DISCOUNT: {
        label: 'Discount',
        visible: true,
        disabled: true,
      },
      TOTAL: {
        label: 'Total',
        visible: true,
        disabled: true,
      },
      ASSESSABLE_VALUE: {
        label: 'Assessable Value',
        visible: true,
        disabled: true,
      },
      TAXABLE_TYPE: {
        label: 'Taxable Type',
        visible: true,
        disabled: true,
      },
      NATURE_OF_TRANSACTION: {
        label: 'Nature of Transaction',
        visible: true,
        disabled: true,
      },
    },
    currentUpdate: '',
    sendForApprovalConfirmation: false,
    selectedTags: [],
    fileList: [],
    getterExecuted: false,
    createPODrawer: false,
    showAllocateLandingPriceDrawer: false,
    showAPInvoiceItemSelectorDrawer: false,
    showAllocatedLandingPriceDrawer: false,
    showReverseAllocateLandingPriceDrawer: false,
    selectedAPInvoiceLine: null,
  });
  const [selectedAPI, setSelectedAPInvoice] = useState('');
  const [selectedApiLoading, setSelectedApiLoading] = useState(false);
  const [isVendorOverseas, setIsVendorOverseas] = useState(false);
  const {
    visibleColumns,
    currentUpdate,
    selectedTags,
    fileList,
    showLinkPaymentDrawer,
    showRecordPaymentDrawer,
    showDnFormDrawer,
    showViewPayout,
    selectedPayment,
    showAllocateLandingPriceDrawer,
    showReverseAllocateLandingPriceDrawer,
  } = state;

  // use to update all the states in view
  const updateState = (newStates) => {
    setState((oldStates) => ({
      ...oldStates,
      ...newStates,
    }));
  };

  // updating states for Ap invoice ById case
  useEffect(() => {
    if (getSelectedAPI) {
      setSelectedAPInvoice(getSelectedAPI);
      setSelectedApiLoading(false);
      setIsVendorOverseas((getSelectedAPI?.tenant_seller_info?.seller_type === 'OVERSEAS' || getSelectedAPI?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas);
      if (getSelectedAPI?.integration_tally_config?.purchase_voucher_integration && getSelectedAPI?.ap_invoice_lines?.some((line) => line?.tally_purchase_account) && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) {
        updateState({
          visibleColumns: {
            ...visibleColumns,
            PURCHASE_ACCOUNT: {
              label: 'Purchase Account',
              visible: true,
              disabled: true,
            },
          },
        });
      } else {
        const lineCf = getSelectedAPI?.ap_invoice_lines?.[0]?.ap_invoice_line_custom_fields || [];
        updateState({
          visibleColumns: {
            ...CustomFieldHelpers.updateVisibleColumns(lineCf, visibleColumns),
          },
        });
      }
    } else {
      setSelectedApiLoading(true);
    }
  }, [getSelectedAPI, user]);

  // add and remove attachment function
  const handleFileChange = (fileListData) => {

    const fileList = fileListData?.fileList?.map((item) => ({
      ...item,
      url: item?.response?.response?.location || item?.url,
    }));
    const attachments = fileList?.map((attachment) => ({
      url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
    }));

    if (fileListData?.file.status === 'done') {
      const payload = {
        entity_id: apiId,
        entity_name: 'account_payable_invoice',
        attachments,
      };
      updateAttachment(payload, () => {
        getAttachmentById(apiId, 'account_payable_invoice', () => {
          updateState({
            isGetAttachment: true,
          });
        });
      });
    }
    if (fileListData?.file.status === 'removed') {
      const payload = {
        entity_id: apiId,
        entity_name: 'account_payable_invoice',
        attachments,
      };
      updateAttachment(payload, () => {
        getAttachmentById(apiId, 'account_payable_invoice', () => {
          updateState({
            isGetAttachment: true,
          });
        });
      });
    }

    updateState({
      fileList: attachments.length > 0 ? attachments : [],
    });
  };

  //  using to check adhoc approvals are allowed or not to add
  const HideAdhocApprovals = () => {
    if (selectedAPI?.workflow_steps) {
      const steps = Object.values(selectedAPI?.workflow_steps)?.filter((step) => step !== null);
      const pendingSteps = steps?.filter((step) => step?.status === 'PENDING');
      return steps.length < 10 && pendingSteps.length === steps.length;
    }
    return false;
  };

  // prefilling all the states required in view case
  useEffect(() => {
    if (selectedAPI) {
      const tags = selectedAPI?.tags?.length > 0 ? selectedAPI?.tags : [];
      updateState({ selectedTags: tags });
      updateState({ fileList: selectedAPI?.attachments });
    }

  }, [selectedAPI]);

  function renderFreight(position) {
    if ((selectedAPI?.charge_1_value > 0)) {
      if (position === 'top' && selectedAPI?.freight_tax_id) {
        return (
          <div className="view-document__totals-field">
            <H3Text
              text={(
                <Fragment>
                  {selectedAPI?.charge_1_name}
                  &nbsp;
                  {!isVendorOverseas && (<span className="table-subscript">{`tax@${selectedAPI?.freight_tax_info?.tax_value}%`}</span>)}
                </Fragment>
              )}
              className="view-document__totals-field-name"
            />
            <H3Text text={MONEY((selectedAPI?.charge_1_value), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
          </div>
        );
      }
      if (position === 'bottom' && !selectedAPI?.freight_tax_id) {
        return (
          <div className="view-document__totals-field">
            <H3Text text={selectedAPI?.charge_1_name} className="view-document__totals-field-name" />
            <H3Text text={MONEY((selectedAPI?.charge_1_value), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
          </div>
        );
      }
    }
    return '';
  }
  const splitChargesData = (charge) => {
    const chargeWithTaxName = charge?.filter((line) => line?.tax_info?.tax_id) || [];
    const chargeWithoutTaxName = charge?.filter((line) => !line?.tax_info?.tax_id) || [];
    return { chargeWithTaxName, chargeWithoutTaxName };
  };

  const renderCharges = (charge) => !!charge?.length && charge?.map((otherCharge, i) => (
    <div key={i} className="view-document__totals-field">
      <H3Text
        text={(
          <Fragment>
            {otherCharge?.charge_name?.toProperCase()}&nbsp;
            {otherCharge?.tax_info?.tax_value && !isVendorOverseas ? (
              <span className="table-subscript">{`tax@${otherCharge.tax_info.tax_value}%`}</span>
            ) : null}
          </Fragment>
        )}
        className="view-document__totals-field-name" />
      <H3Text text={MONEY((otherCharge?.charge_amount), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
    </div>
  ));

  const isTallyConnected = (selectedAPI?.integration_tally_config?.purchase_voucher_integration) && (['ISSUED'].includes(selectedAPI?.status));

  return (
    <div className={`view-document__wrapper ${!isQuickView ? 'view-document__wrapper-page' : ''}`}>
      {selectedApiLoading ? (
        <ViewLoadingSkull isQuickView={isQuickView} fixedMenuBar={fixedMenuBar} />
      ) : (
        <div className='ant-row'>
          <div className={`${fixedMenuBar ? 'ant-col-md-16' : 'ant-col-md-17'} ant-col-xs-24`}>
            <div className={`view-left__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
              <div className="view-document__title">
                <div className="view-document__title-number-wrapper">
                  <H3Text text={selectedAPI?.ap_invoice_number || 'N/A'} className="view-document__title-number" />
                  <div className="view-document__title-status" style={{ backgroundColor: Helpers.getStatusColor(selectedAPI?.status)?.color }}>
                    {selectedAPI?.status?.replaceAll('_', ' ')}
                  </div>
                </div>
                {!isQuickView && (
                  <div className="view-document__title-actions">
                    <div className="action-buttons">
                      {Helpers.getPermission(Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE, Helpers.permissionTypes.UPDATE, user) && selectedAPI?.status === 'DRAFT' && (
                        <H3Text
                          text={(
                            <div onClick={() => {
                              history.push(`/purchase/account-payable-invoice/update/${apiId}`);
                            }}
                            >
                              <EditOutlined />
                            </div>
                          )}
                          className="action-button hide__in-mobile"
                        />
                      )}
                      <H3Text
                        text={
                          <FontAwesomeIcon icon={faPrint} style={{ fontSize: '16px', width: '35px', textAlign: 'center' }} />
                        }
                        onClick={() => downloadDocument({
                          url: `${Constants.ACCOUNT_PAYABLE_INVOICE}/download/${apiId}?tenant_id=${selectedAPI?.tenant_id}`,
                          document_type: 'ACCOUNT_PAYABLE_INVOICE',
                          document_number: selectedAPI?.ap_invoice_number,
                          key: uuidv4(),
                        })}
                        className="action-button"
                      />
                    </div>
                    <div className="action-buttons">
                      {/* {Helpers.getPermission(Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE, Helpers.permissionTypes.CREATE, user) && (
                        <Tooltip
                          placement="topLeft"
                          title="Make a Copy"
                        >
                          <CopyOutlined
                            className="action-button"
                            style={{
                              justifyContent: 'center',
                              fontSize: '16px',
                            }}
                            onClick={() => {
                              history.push(`/purchase/account-payable-invoice/create?cloneApiId=${apiId}`);
                            }}
                          />
                        </Tooltip>
                      )} */}
                      {['DRAFT'].includes(selectedAPI?.status) && Helpers.getPermission(Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE, Helpers.permissionTypes.CREATE, user) && (
                        <Popconfirm
                          placement="topRight"
                          title="Are you sure you want to send this Ap Invoice for approval?"
                          onConfirm={() => {
                            updateState({ currentUpdate: 'SENT_FOR_APPROVAL' });
                            updateAPInvoiceStatus({
                              url: SERVICE_ENDPOINT.UPDATE_ACCOUNT_PAYABLE_INVOICE_STATUS({ api_id: apiId }).url,
                              body: {
                                status: 'SENT_FOR_APPROVAL',
                              }
                            }, () => {
                              manualRefetch();
                              updateState({ currentUpdate: '' });
                            });
                          }}
                          okText="Yes"
                          cancelText="No"
                        >
                          <H3Text
                            text={(
                              <div style={{ width: '55px', textAlign: 'center' }}>
                                {(updateAPInvoiceStatusLoading && currentUpdate === 'SENT_FOR_APPROVAL')
                                  ? <LoadingOutlined /> : (
                                    <span>Confirm</span>
                                  )}
                              </div>
                            )}
                            onClick={() => updateState({ sendForApprovalConfirmation: true })}
                            className="action-button action-button-big"
                          />
                        </Popconfirm>
                      )}
                      {Helpers.getPermission(Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE,
                        Helpers.permissionTypes.DELETE, user) && !selectedAPI?.payments?.length
                        && selectedAPI?.status?.toUpperCase() !== 'VOID'
                        && (
                          <Popconfirm
                            placement="topRight"
                            title="Are you sure you want to cancel this AP Invoice?"
                            onConfirm={() => {
                              if (selectedAPI?.debit_notes?.length > 0 || selectedAPI?.payments?.length > 0) {
                                notification.error({
                                  message: 'This AP Invoice cannot be cancelled as it has linked Documents',
                                  placement: 'top',
                                  duration: 6,
                                });
                              } else {
                                updateState({ currentUpdate: 'CANCELLED' });
                                updateAPInvoiceStatus({
                                  url: SERVICE_ENDPOINT.UPDATE_ACCOUNT_PAYABLE_INVOICE_STATUS({ api_id: apiId }).url,
                                  body: {
                                    status: 'VOID',
                                  }
                                }, () => {
                                  manualRefetch();
                                  updateState({ currentUpdate: '' });
                                });
                              }
                            }}
                            okText="Yes"
                            cancelText="No"
                          >
                            <H3Text
                              text={(
                                <div style={{ width: '50px', textAlign: 'center' }}>
                                  {(updateAPInvoiceStatusLoading && currentUpdate === 'CANCELLED')
                                    ? <LoadingOutlined /> : (
                                      <span>Cancel</span>
                                    )}
                                </div>
                              )}
                              className="action-button action-button-big"
                            />
                          </Popconfirm>
                        )}
                      {window.screen.width > 425 && selectedAPI?.status === 'ISSUED' && ((Helpers.getPermission(Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.CREATE, user)) || (Helpers.getPermission(Helpers.permissionEntities.VENDOR_PAYOUT, Helpers.permissionTypes.CREATE, user))) && (
                        <Dropdown
                          overlay={(
                            <Menu>
                              {selectedAPI?.ap_invoice_lines?.some((line) => line?.is_landed_cost) && (
                                <Menu.Item disabled={isDataMaskingPolicyEnable && isHideCostPrice}>
                                  <div className="flex-align-center-justify-between">
                                    <H3Text
                                      text="Allocate Landing Cost To"
                                      onClick={() => updateState({ showAllocateLandingPriceDrawer: true })}
                                    />
                                    {(isDataMaskingPolicyEnable && isHideCostPrice) && (
                                      <Tooltip title={'You don\'t have access to allocate landed cost'}>
                                        <InfoCircleOutlined style={{ color: '#2d7df7', cursor: 'pointer' }} />
                                      </Tooltip>
                                    )}
                                  </div>
                                </Menu.Item>)}
                              {selectedAPI?.ap_invoice_lines?.some((line) => line?.product_sku_info?.product_type === 'STORABLE') && selectedAPI?.ap_invoice_lines?.filter((line) => line?.grn_line_id)?.length > 0 && (
                                <Menu.Item disabled={isDataMaskingPolicyEnable && isHideCostPrice}>
                                  <div className="flex-align-center-justify-between">
                                    <H3Text
                                      text="Allocate Landing Cost from"
                                      onClick={() => updateState({ showReverseAllocateLandingPriceDrawer: true })}
                                    />
                                    {(isDataMaskingPolicyEnable && isHideCostPrice) && (
                                      <Tooltip title={'You don\'t have access to allocate landed cost'}>
                                        <InfoCircleOutlined style={{ color: '#2d7df7', cursor: 'pointer' }} />
                                      </Tooltip>
                                    )}
                                  </div>
                                </Menu.Item>
                              )}
                              {Helpers.getPermission(Helpers.permissionEntities.DEBIT_NOTE,
                                Helpers.permissionTypes.CREATE, user) && selectedAPI?.status.toUpperCase() === 'ISSUED' && window.screen.width > 425 && (
                                  <Menu.Item>
                                    <H3Text
                                      text={(
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                          <div>
                                            + Debit Note
                                          </div>
                                          {!user?.tenant_info?.purchase_config?.sub_modules?.debit_note?.is_active && (
                                            <Popconfirm
                                              placement="topRight"
                                              title="This feature is not accessible within your current plan to use this feature contact us."
                                              onConfirm={() => globalThis.Intercom('showNewMessage')}
                                              okText="Contact Us"
                                              cancelText="Cancel"
                                            >
                                              <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={cdnUrl("crown2.png", "images")} alt="premium" />
                                            </Popconfirm>
                                          )}
                                        </div>
                                      )}
                                      onClick={() => {
                                        if (user?.tenant_info?.purchase_config?.sub_modules?.debit_note?.is_active) {
                                          updateState({ showDnFormDrawer: true });
                                        }
                                      }}
                                    />
                                  </Menu.Item>
                                )}

                              {Helpers.getPermission(Helpers.permissionEntities.VENDOR_PAYOUT,
                                Helpers.permissionTypes.CREATE, user) && selectedAPI?.status.toUpperCase() === 'ISSUED' && (selectedAPI?.grand_total - selectedAPI?.total_payment_made) > 0
                                && (
                                  <Menu.Item>
                                    <H3Text
                                      text={(
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                          <div>
                                            Record Payment
                                          </div>
                                          {!user?.tenant_info?.purchase_config?.sub_modules?.payment_outgoing?.is_active && (
                                            <Popconfirm
                                              placement="topRight"
                                              title="This feature is not accessible within your current plan to use this feature contact us."
                                              onConfirm={() => globalThis.Intercom('showNewMessage')}
                                              okText="Contact Us"
                                              cancelText="Cancel"
                                            >
                                              <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={cdnUrl("crown2.png", "images")} alt="premium" />
                                            </Popconfirm>
                                          )}
                                        </div>
                                      )}
                                      onClick={
                                        () => updateState({ showRecordPaymentDrawer: true })
                                      }
                                    />
                                  </Menu.Item>
                                )}
                              {Helpers.getPermission(Helpers.permissionEntities.VENDOR_PAYOUT,
                                Helpers.permissionTypes.CREATE, user) && selectedAPI?.status.toUpperCase() === 'ISSUED' && (selectedAPI?.grand_total - selectedAPI?.total_payment_made) > 0
                                && window.screen.width > 425 && (
                                  <Menu.Item>
                                    <H3Text
                                      text={(
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                          <div>
                                            Link Payment
                                          </div>
                                          {!user?.tenant_info?.purchase_config?.sub_modules?.payment_outgoing?.is_active && (
                                            <Popconfirm
                                              placement="topRight"
                                              title="This feature is not accessible within your current plan to use this feature contact us."
                                              onConfirm={() => globalThis.Intercom('showNewMessage')}
                                              okText="Contact Us"
                                              cancelText="Cancel"
                                            >
                                              <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={cdnUrl("crown2.png", "images")} alt="premium" />
                                            </Popconfirm>
                                          )}
                                        </div>
                                      )}
                                      onClick={() => updateState({ showLinkPaymentDrawer: true })}
                                    />
                                  </Menu.Item>
                                )}

                              {isTallyConnected && (
                                <Menu.Item>
                                  <div
                                    style={{ display: 'flex', alignItems: 'center', gap: '10px' }}
                                    onClick={() => {
                                      if (user?.tenant_info?.integration_config?.sub_modules?.tally?.is_active) {
                                        syncTallyGRN({ ap_invoice_id: selectedAPI?.ap_invoice_id || apiId },
                                          () => {
                                            manualRefetch();
                                          });
                                      }
                                    }}
                                  >
                                    <H3Image src={cdnUrl("tally-prime.png", "integrations")} style={{
                                      width: '20px',
                                      height: '20px',
                                    }} />
                                    <H3Text
                                      text={(
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                          <div>
                                            Push with Tally
                                          </div>
                                          {!user?.tenant_info?.integration_config?.sub_modules?.tally?.is_active && (
                                            <Popconfirm
                                              placement="topRight"
                                              title="This feature is not accessible within your current plan to use this feature contact us."
                                              onConfirm={() => globalThis.Intercom('showNewMessage')}
                                              okText="Contact Us"
                                              cancelText="Cancel"
                                            >
                                              <img className="barcode-restrict" style={{ marginLeft: '4px' }} src={cdnUrl("crown2.png", "images")} alt="premium" />
                                            </Popconfirm>
                                          )}
                                        </div>
                                      )}
                                    />
                                  </div>
                                </Menu.Item>
                              )}
                            </Menu>
                          )}
                          trigger={['click']}
                          overlayStyle={{ width: '194px' }}
                          placement="bottomLeft"
                        >
                          <H3Text
                            text={(
                              <div onClick={(e) => e.preventDefault()}>
                                More
                                <CaretDownOutlined />
                              </div>
                            )}
                            className="action-button action-button-big no-right-border"
                          />
                        </Dropdown>
                      )}
                    </div>
                  </div>
                )}
              </div>
              <div className='document-header'>
                <div className='ant-row'>
                  <div className="ant-col-md-24">
                    <H3Text
                      text={(
                        <span>
                          <UserOutlined />
                          {' '}
                          <span>
                            <Link
                              to={`/vendors/view/${selectedAPI?.seller_id}`}
                              target="_blank"
                            >
                              {`${selectedAPI?.internal_slr_code || 'N/A'} - ${selectedAPI?.tenant_seller_info?.seller_name}`}
                            </Link>
                          </span>
                        </span>
                      )}
                      className="document-header__party-name"
                    />
                  </div>

                  <div className="ant-col-md-12">
                    <Tooltip title={selectedAPI?.tenant_info?.tenant_name}>
                      <div className="document-header__field">
                        <H3Text text="Location" className="document-header__field-name" />
                        <H3Text text={selectedAPI?.tenant_info?.tenant_name?.length > 25 ? `${selectedAPI?.tenant_info?.tenant_name?.substring(0, 25)}...` : selectedAPI?.tenant_info?.tenant_name} className="document-header__field-value" />
                      </div>
                    </Tooltip>
                  </div>

                  <div className="ant-col-md-12">
                    <Tooltip title={selectedAPI?.tenant_department_info?.department_name}>
                      <div className="document-header__field">
                        <H3Text text="Department" className="document-header__field-name" />
                        <H3Text text={selectedAPI?.tenant_department_info?.department_name?.length > 25 ? `${selectedAPI?.tenant_department_info?.department_name?.substring(0, 25)}..` : selectedAPI?.tenant_department_info?.department_name} className="document-header__field-value" />
                      </div>
                    </Tooltip>
                  </div>

                  {selectedAPI?.ap_invoice_date && <div className="ant-col-md-12">
                    <div className="document-header__field">
                      <H3Text text="AP Invoice Date" className="document-header__field-name" />
                      <H3Text
                        text={selectedAPI?.ap_invoice_date ? (dayjs(selectedAPI.ap_invoice_date).format('DD/MM/YYYY')) : '-'}
                        className="document-header__field-value"
                      />
                    </div>
                  </div>}
                  {selectedAPI?.linked_grns?.length > 0 && (
                    <div className="ant-col-md-12">
                      <div className="document-header__field">
                        <H3Text text="GRN #" className="document-header__field-name" />
                        {selectedAPI?.linked_grns?.length === 1 ? (
                          <H3Text
                            text={<Link to={`/purchase/goods-receiving/view/${selectedAPI?.linked_grns?.[0]?.grn_id}`} target="_blank">{selectedAPI?.linked_grns?.[0]?.grn_number}</Link>}
                            className="document-header__field-value"
                          />) : (
                          (() => {
                            const firstGrn = selectedAPI?.linked_grns?.[0];
                            const remainingGrns = selectedAPI?.linked_grns?.slice(1) || [];
                            const remainingCount = remainingGrns.length;
                            return firstGrn ? (
                              <Tooltip
                                title={
                                  remainingCount > 0 ? (
                                    <div className="tally-locations-tooltip">
                                      {remainingGrns?.map((grn, index) => (
                                        <Link
                                          to={`/purchase/goods-receiving/view/${grn?.grn_id}`}
                                          target="_blank"
                                          style={{
                                            fontWeight: 600,
                                            color: '#1890ff',
                                            fontSize: '13px',
                                            display: 'block',
                                            marginBottom: '4px',
                                          }}
                                        >
                                          {grn?.grn_number}
                                        </Link>
                                      ))}
                                    </div>
                                  ) : (
                                    <div className="tally-locations-tooltip">No additional Pos</div>
                                  )
                                }
                                placement="top"
                                overlayInnerStyle={{ background: 'white', color: 'black' }}
                              >
                                <span >
                                  <Link
                                    to={`/purchase/goods-receiving/view/${firstGrn.grn_id}`}
                                    target="_blank"
                                    className="document-header__field-value"
                                  >
                                    {firstGrn.grn_number}
                                  </Link>
                                  <span
                                    style={{
                                      color: 'gray'
                                    }}
                                  >
                                    {remainingCount > 0 && ` +${remainingCount} more`}
                                  </span>
                                </span>
                              </Tooltip>
                            ) : (
                              <span>-</span>
                            );
                          })()
                        )}
                      </div>
                    </div>
                  )}
                  {selectedAPI?.vendor_invoice_number && (
                    <div className="ant-col-md-12">
                      <div className="document-header__field">
                        <H3Text text="Vendor Invoice #" className="document-header__field-name" />
                        <H3Text text={`${selectedAPI?.vendor_invoice_number}`} className="document-header__field-value" />
                      </div>
                    </div>
                  )}
                  {selectedAPI?.vendor_invoice_date && <div className="ant-col-md-12">
                    <div className="document-header__field">
                      <H3Text text="Vendor Invoice Date" className="document-header__field-name" />
                      <H3Text
                        text={selectedAPI?.vendor_invoice_date ? (dayjs(selectedAPI.vendor_invoice_date).format('DD/MM/YYYY')) : '-'}
                        className="document-header__field-value"
                      />
                    </div>
                  </div>}
                  {selectedAPI?.conversion_rate && (
                    <div className="ant-col-md-12">
                      <div className="document-header__field">
                        <H3Text text="Conversion Rate" className="document-header__field-name" />
                        <H3Text text={`${selectedAPI?.conversion_rate}`} className="document-header__field-value" />
                      </div>
                    </div>
                  )}
                  {selectedAPI?.tenant_seller_info?.gst_number && (
                    <div className="ant-col-md-12">
                      <div className="document-header__field">
                        <H3Text text="GSTIN" className="document-header__field-name" />
                        <H3Text text={`${selectedAPI?.tenant_seller_info?.gst_number}`} className="document-header__field-value" />
                      </div>
                    </div>
                  )}
                  {isTallyConnected && (
                    <React.Fragment>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Bill of entry date" className="document-header__field-name" />
                          <H3Text
                            text={selectedAPI?.bill_of_entry_date ? (dayjs(selectedAPI.bill_of_entry_date).format('DD/MM/YYYY')) : '-'}
                            className="document-header__field-value"
                          />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Bill of entry number" className="document-header__field-name" />
                          <H3Text text={`${selectedAPI?.bill_of_entry_number || '-'}`} className="document-header__field-value" />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Port Code" className="document-header__field-name" />
                          <H3Text text={`${selectedAPI?.port_code || '-'}`} className="document-header__field-value" />
                        </div>
                      </div>
                    </React.Fragment>
                  )}

                  {selectedAPI?.e_way_bill_number && (
                    <div className="ant-col-md-12">
                      <div className="document-header__field">
                        <H3Text text="e-Way Bill#" className="document-header__field-name" />
                        <H3Text text={`${selectedAPI?.e_way_bill_number}`} className="document-header__field-value" />
                      </div>
                    </div>
                  )}
                  {selectedAPI?.e_way_bill_date && (
                    <div className="ant-col-md-12">
                      <div className="document-header__field">
                        <H3Text text="e-Way Bill date" className="document-header__field-name" />
                        <H3Text
                          text={selectedAPI?.e_way_bill_date ? (dayjs(selectedAPI.e_way_bill_date).format('DD/MM/YYYY')) : '-'}
                          className="document-header__field-value"
                        />
                      </div>
                    </div>
                  )}
                  {selectedAPI?.e_way_bill?.[0]?.url && (
                    <div className="ant-col-md-12">
                      <div className="document-header__field">
                        <H3Text text="e-Way Bill" className="document-header__field-name" />
                        <H3Text
                          text={<a href={selectedAPI?.e_way_bill?.[0]?.url} target="_blank" rel="noreferrer">{selectedAPI?.e_way_bill?.[0]?.name}</a>}
                          style={{
                            whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', width: selectedAPI?.e_way_bill?.[0]?.name ? '90%' : '',
                          }}
                        />
                      </div>
                    </div>
                  )}
                  {isTallyConnected && (
                    <Fragment>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Tally Voucher Type" className="document-header__field-name" />
                          <H3Text text={`${selectedAPI?.purchase_voucher_type}`} className="document-header__field-value" />
                        </div>
                      </div>
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Tally Cost Centre" className="document-header__field-name" />
                          <H3Text text={selectedAPI?.cost_centre ? `${selectedAPI?.cost_centre}` : '-' } className="document-header__field-value" />
                        </div>
                      </div>
                    </Fragment>
                  )}
                  {
                    isTallyConnected && (
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Tally Status" className="document-header__field-name" />
                          {(selectedAPI?.tally_updated_at && !selectedAPI?.tally_warning_message) && (
                            <H3Text
                              text={(
                                <React.Fragment>
                                  <span className="status-tag">Pushed</span>
                                </React.Fragment>
                              )}
                              className="document-header__field-value"
                            />
                          )}
                          {selectedAPI?.tally_warning_message && (
                            <H3Text
                              text={(
                                <React.Fragment>
                                  <span
                                    className="status-tag"
                                    style={{
                                      backgroundColor: 'rgb(255, 0, 0)',
                                    }}
                                  >
                                    Error
                                  </span>
                                  &nbsp;
                                  <Tooltip
                                    title={selectedAPI?.tally_warning_message}
                                  >
                                    <InfoCircleOutlined style={{ color: 'rgb(255, 0, 0)', cursor: 'pointer' }} />
                                  </Tooltip>
                                </React.Fragment>
                              )}
                              className="document-header__field-value"
                            />
                          )}
                          {(!selectedAPI?.tally_voucher_id && !selectedAPI?.tally_warning_message && !selectedAPI?.tally_updated_at) && (
                            <H3Text
                              text={(
                                <React.Fragment>
                                  <span
                                    className="status-tag"
                                    style={{
                                      backgroundColor: 'rgb(70, 139, 247)',
                                    }}
                                  >
                                    Not Synced
                                  </span>
                                </React.Fragment>
                              )}
                              className="document-header__field-value"
                            />
                          )}
                        </div>
                      </div>
                    )
                  }
                  {
                    (selectedAPI?.integration_tally_config?.purchase_voucher_integration) && (user?.tenant_info?.purchase_account_selection === 'FROM_GRN') && selectedAPI?.tally_purchase_account && (
                      <div className="ant-col-md-12">
                        <div className="document-header__field">
                          <H3Text text="Purchase Account" className="document-header__field-name" />
                          <H3Text text={`${selectedAPI?.tally_purchase_account}`} className="document-header__field-value" />
                        </div>
                      </div>
                    )
                  }
                  {selectedAPI?.custom_fields?.length > 0 ? (
                    <ViewCustomFields
                      customFields={selectedAPI?.custom_fields?.filter((cf) => cf.is_active)}
                      wrapperClass="document-header__field"
                      labelClass="document-header__field-name"
                      valueClass="document-header__field-value"
                      isHorizontal
                    />
                  ) : ''}
                </div>
              </div>
              <br />
              <ViewAccountPayableInvoiceLines
                apInvoiceLines={selectedAPI?.ap_invoice_lines?.map((item) => ({
                  ...item,
                  lineCustomFields: item?.ap_invoice_line_custom_fields,
                }))}
                selectedAPI={selectedAPI}
                cfApInvoiceLine={selectedAPI?.ap_invoice_lines?.[0]?.ap_invoice_line_custom_fields}
                visibleColumns={visibleColumns}
                setVisibleColumns={(data) => updateState({ visibleColumns: data })}
                isVendorOverseas={isVendorOverseas}
                manualRefetch={manualRefetch}
              />
              <div className='view-document__totals-wrapper'>
                <div className="view__document-footer-left">
                  {selectedAPI?.remarks && (
                    <div className="view__document-tc" style={{ marginBottom: '10px' }}>
                      <H3Text text="Additional Remarks" className="view__document-tc-title" />
                      <H3Text text={selectedAPI?.remarks ? selectedAPI?.remarks : ''} className="view__document-tc-body" style={{ padding: '15px', }} />
                    </div>
                  )}
                  <Attachment
                    fileList={fileList}
                    disableCase={getAttachmentByIdLoading || updateAttachmentLoading}
                    labelClassName="orgFormLabel"
                    inputClassName=""
                    containerClassName={!apiId ? 'view__document-attachment-drawer' : 'view__document-attachment'}
                    handleFileChange={(value) => handleFileChange(value)}
                    updateEnable={Helpers.getPermission(Helpers.permissionEntities.ACCOUNT_PAYABLE_INVOICE, Helpers.permissionTypes.UPDATE, user)}
                  />
                </div>
                {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideComponent style={{
                  width: '40%',
                  marginLeft: 'auto',
                  marginTop: '10px'
                }} /> : (
                  <div className="view-document__totals">
                    <div className="view-document__totals-field">
                      <H3Text text="Sub Total" className="view-document__totals-field-name" />
                      <H3Text text={MONEY((selectedAPI?.base_price), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                    </div>
                    {selectedAPI?.discount_amount !== 0 && (
                      <div className="view-document__totals-field">
                        <H3Text
                          text={`Discount ${!selectedAPI?.is_line_wise_discount
                            ? `(${selectedAPI?.is_discount_in_percent
                              ? `${selectedAPI?.discount_percentage}%`
                              : `${MONEY(selectedAPI?.discount_amount, selectedAPI?.org_currency_info?.currency_code)}`
                            })`
                            : ''
                            }`}
                          className="view-document__totals-field-name"
                        />
                        {selectedAPI?.discount_amount > 0
                          ? (
                            <H3Text
                              text={`(-) ${MONEY((selectedAPI?.discount_amount), selectedAPI?.org_currency_info?.currency_code)}`}
                              className="view-document__totals-field-value danger-text"
                            />
                          )
                          : (
                            <H3Text
                              text={MONEY((selectedAPI?.discount_amount), selectedAPI?.org_currency_info?.currency_code)}
                              className="view-document__totals-field-value"
                            />
                          )}
                      </div>
                    )}
                    {renderFreight('top')}
                    {renderCharges(splitChargesData(selectedAPI?.other_charges).chargeWithTaxName)}
                    <div className="view-document__totals-field">
                      <H3Text text="Taxable Amount" className="view-document__totals-field-name" />
                      <H3Text text={MONEY((selectedAPI?.taxable_amount || 0), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                    </div>
                    {(selectedAPI?.tcs_id || selectedAPI?.tds_id) && !isVendorOverseas && (
                      <div className="view-document__totals-field">
                        <H3Text
                          text={(
                            <Fragment>
                              {selectedAPI?.tcs_id ? 'TCS' : 'TDS'}
                              &nbsp;
                              <span className="table-subscript">{`${selectedAPI?.tcs_id ? 'tcs' : 'tds'}@${selectedAPI?.tcs_info?.tax_value || selectedAPI?.tds_info?.tax_value}%`}</span>
                            </Fragment>
                          )}
                          className="view-document__totals-field-name"
                        />
                        <H3Text text={selectedAPI?.tcs_id ? `${MONEY((selectedAPI?.tcs_info?.tcs_amount || 0), selectedAPI?.org_currency_info?.currency_code)}` : `(-)${MONEY((selectedAPI?.tds_info?.tds_amount || 0), selectedAPI?.org_currency_info?.currency_code)}`} className={selectedAPI?.tcs_id ? 'view-document__totals-field-value' : '  view-document__totals-field-value danger-text'} />
                      </div>
                    )}
                    {renderFreight('bottom')}
                    {!isVendorOverseas && selectedAPI?.tax_info?.map((tax, i) => (
                      <div key={i} className="view-document__totals-field">
                        <H3Text text={tax?.tax_type_name} className="view-document__totals-field-nam" />
                        <H3Text text={MONEY((tax?.tax_amount), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value danger-text" />
                      </div>
                    ))}
                    {renderCharges(splitChargesData(selectedAPI?.other_charges).chargeWithoutTaxName)}
                    {Math.abs(selectedAPI?.round_off) !== 0 && (
                      <div className="view-document__totals-field">
                        <H3Text text="Round Off" className="view-document__totals-field-name" />
                        {selectedAPI?.round_off < 0
                          ? (
                            <H3Text
                              text={`(-) ${MONEY((Math.abs(selectedAPI?.round_off) || '0'), selectedAPI?.org_currency_info?.currency_code)}`}
                              className="view-document__totals-field-value danger-text"
                            />
                          )
                          : (
                            <H3Text
                              text={MONEY((selectedAPI?.round_off || '0'), selectedAPI?.org_currency_info?.currency_code)}
                              className="view-document__totals-field-value"
                            />
                          )}
                      </div>
                    )}
                    <div className="view-document__totals-field view-document__totals-field-total">
                      <H3Text text="Grand Total" className="view-document__totals-field-name" />
                      <H3Text text={MONEY((selectedAPI?.grand_total), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                    </div>
                    {selectedAPI?.total_payment_made ? (
                      <div className="view-document__totals-field">
                        <H3Text text="Payment Made" className="view-document__totals-field-name" />
                        <H3Text text={`(-) ${MONEY((selectedAPI?.total_payment_made), selectedAPI?.org_currency_info?.currency_code)}`} className="view-document__totals-field-value danger-text" />
                      </div>
                    ) : <Fragment />}
                    {Helpers.getValueTotalInObject(selectedAPI?.debit_note_info, 'db_total') > 0 && (
                      <div className="view-document__totals-field">
                        <H3Text text="Credit Applied" className="view-document__totals-field-name" />
                        <H3Text
                          text={`(-) ${MONEY(
                            Helpers.getValueTotalInObject(selectedAPI?.debit_note_info, 'document_db_total')
                            || Helpers.getValueTotalInObject(selectedAPI?.debit_note_info, 'db_total')
                            || 0,
                            selectedAPI?.org_currency_info?.currency_code,
                          )}`}
                          className="view-document__totals-field-value danger-text"
                        />
                      </div>
                    )}
                    <div className="view-document__totals-field view-document__totals-field-total">
                      <H3Text text="Total Due" className="view-document__totals-field-name" />
                      <H3Text text={MONEY((selectedAPI?.grand_total - (Helpers.getValueTotalInObject(selectedAPI?.debit_note_info, 'document_db_total') || Helpers.getValueTotalInObject(selectedAPI?.debit_note_info, 'db_total')) - selectedAPI?.total_payment_made), selectedAPI?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className={`${fixedMenuBar ? 'ant-col-md-8' : 'ant-col-md-7'} ant-col-xs-24`}>
            <div className={`view-right__wrapper ${!isQuickView ? 'is-page-view' : ''}`}>
              {selectedAPI?.debit_notes?.length && Helpers.getPermission(Helpers.permissionEntities.DEBIT_NOTE, Helpers.permissionTypes.READ, user)
                ? (
                  <div className="linked-doc__wrapper">
                    <H3Text text="ASSOCIATED DEBIT NOTES" className="linked-doc__title" />
                    {selectedAPI?.debit_notes?.map((item) => (
                      <div
                        key={item?.debit_note_number}
                        className="linked-doc__item-header"
                        onClick={() => {
                          window.open(`/purchase/debit-note/view/${item?.dn_id}`);
                        }}
                      >
                        <CaretRightOutlined />
                        <div className="linked-doc__item-header-id-status__wrapper">
                          <H3Text
                            text={`DN # ${item?.debit_note_number}`}
                            className="linked-doc__item-header-id"
                          />
                          <div
                            className="linked-doc__item-header-status"
                            style={{ backgroundColor: Helpers.getStatusColor(item?.status)?.color }}
                          >
                            {Helpers.getStatusColor(item?.status)?.text}
                          </div>
                        </div>
                        <H3Text
                          text={MONEY((item?.document_dn_grand_total || item?.dn_grand_total), selectedAPI?.org_currency_info?.currency_code)}
                          className={`linked-doc__item-header-total ${['VOID', 'REJECTED'].includes(item?.status) ? 'linked-doc__item-header-total-disabled' : ''}`}
                          hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                          popOverMessage={'You don\'t have access to view  amount'}
                        />
                      </div>
                    ))}
                  </div>
                )
                : null}
              {!(isDataMaskingPolicyEnable && (isHideCostPrice || isHideSellingPrice)) && selectedAPI?.payments?.length
                ? (
                  <div className="linked-doc__wrapper">
                    <H3Text text="LINKED PAYMENTS" className="linked-doc__title" />
                    {selectedAPI?.payments?.map((item) => (
                      <div
                        key={item?.payment_id}
                        className="linked-doc__item-header"
                        onClick={() => {
                          updateState({ showViewPayout: true });
                          updateState({ selectedPayment: item });
                        }}
                      >
                        <CaretRightOutlined />
                        <div className="linked-doc__item-header-id-status__wrapper">
                          <H3Text text={`${item?.applied_payment_type?.split('_')?.map((word) => word?.[0].toUpperCase())?.join('')} # ${item?.debit_note_number || item?.payment_id}`} className="linked-doc__item-header-id" />
                        </div>
                        <H3Text
                          text={MONEY((item?.amount))}
                          className={`linked-doc__item-header-total ${['VOID', 'REJECTED'].includes(item?.status) ? 'linked-doc__item-header-total-disabled' : ''}`}
                        />
                      </div>
                    ))}
                  </div>
                )
                : null}
              <TagSelector
                entityType="ACCOUNT_PAYABLE_INVOICE"
                entityId={apiId}
                selectedTags={selectedTags}
                isMultiple
                showSearch
                onChange={(value) => {
                  updateState({ selectedTags: value });
                }}
                placeholder="Select Tags"
                maxTagCount='responsive'
              />

              {selectedAPI?.status !== 'DRAFT' ? (
                <WorkFlowTimeLine
                  workflowSteps={selectedAPI?.workflow_steps || {}}
                  workflowStepId={selectedAPI?.workflow_step_id}
                  loading={apInvoiceApprovalWorkflowLoading}
                  updateWorkflowStep={(payload, callback) => {
                    apInvoiceApprovalWorkflow({ body: payload }, callback);
                  }}
                  HideCase={HideAdhocApprovals()}
                  entityId={Number(apiId)}
                  entityType="ACCOUNT_PAYABLE_INVOICE"
                  callback={() => {
                    manualRefetch();
                  }}
                />
              ) : ''}

              {selectedAPI && (
                <ActivityLog
                  entityId={selectedAPI?.ap_invoice_id}
                  entityType="account_payable_invoice"
                  entityName="Account Payable Invoice"
                />
              )}
            </div>
          </div>
        </div>
      )}
      <Drawer
        onClose={() => updateState({ showDnFormDrawer: false })}
        open={showDnFormDrawer}
        width={Helpers.getDrawerWidth(1150)}
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: `${Helpers.getDrawerWidth(1100)}px` }}>
            <H3Text text="Create Debit Note" className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => updateState({ showDnFormDrawer: false })} />
          </div>
        </div>
        <DebitNoteForm
          selectedGrnForDN={{
            ...selectedAPI,
            tenant_seller_info: {
              ...((selectedAPI && selectedAPI.tenant_seller_info) || {}),
            },
            ap_invoice_lines: selectedAPI?.ap_invoice_lines?.map((item) => ({
              ...item,
              received_qty: item?.quantity,
            })),
            grn_id: selectedAPI?.ap_invoice_id
          }}
          callback={() => {
            updateState({ showDnFormDrawer: false });
            manualRefetch();
          }}
          isQuickView
          dnFormFromAPInvoice
        />
      </Drawer>
      <Drawer
        open={showRecordPaymentDrawer}
        width="420px"
        onClose={() => updateState({ showRecordPaymentDrawer: false })}
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '375px' }}>
            <H3Text text="Record New Payment" className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => updateState({ showRecordPaymentDrawer: false })} />
          </div>
        </div>
        <RecordOutgoingPayment
          selectedGRN={selectedAPI}
          callback={() => {
            updateState({ showRecordPaymentDrawer: false });
            manualRefetch();
          }}
          recordPaymentFromAPInvoiceScreen
        />
      </Drawer>
      <Drawer
        open={showLinkPaymentDrawer}
        width="820px"
        onClose={() => updateState({ showLinkPaymentDrawer: false })}
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '775px' }}>
            <H3Text text="Link Payment" className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => updateState({ showLinkPaymentDrawer: false })} />
          </div>
        </div>
        <LinkOutgoingPayment
          selectedEntity={selectedAPI}
          callback={() => {
            updateState({ showLinkPaymentDrawer: false });
            manualRefetch();
          }}
          screen={'grn'}
          linkPaymentFromAPInvoice
        />
      </Drawer>
      <Drawer
        open={showViewPayout}
        onClose={() => updateState({ showViewPayout: false })}
        width="1250px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '1200px' }}>
            <H3Text text={`View Payment #${selectedPayment?.debit_note_number || selectedPayment?.payment_id}`} className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => updateState({ showViewPayout: false })} />
          </div>
        </div>
        <ViewPayment
          selectedPayment={selectedPayment}
          selectedPaymentId={selectedPayment?.payment_id}
          paymentType={selectedPayment?.applied_payment_type}
          callback={() => {
            updateState({ showViewPayout: false });
            manualRefetch();
          }}
        />
      </Drawer>
      <Drawer
        open={showAllocateLandingPriceDrawer}
        onClose={() => updateState({ showAllocateLandingPriceDrawer: false })}
        width="800px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '750px' }}>
            <H3Text text='Allocate Landing Cost' className="custom-drawer__title" />
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => updateState({ showAllocateLandingPriceDrawer: false })} />
          </div>
        </div>
        <AllocateLandingPrice
          entityLines={selectedAPI?.ap_invoice_lines?.filter((line) => line?.is_landed_cost)}
          setShowAPInvoiceItemSelectorDrawer={() => updateState({ showAPInvoiceItemSelectorDrawer: true })}
          selectedEntityID={selectedAPI?.ap_invoice_id}
          callback={() => {
            updateState({ showAllocateLandingPriceDrawer: false });
            manualRefetch();
          }}
        />
      </Drawer>
      <Drawer
        open={showReverseAllocateLandingPriceDrawer}
        onClose={() => {
          updateState({ showReverseAllocateLandingPriceDrawer: false });
        }}
        width="1250px"
        destroyOnClose
        mask={true}
        maskClosable={false}
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '1200px' }}>
            <H3Text text='Allocate Landing Cost' className="custom-drawer__title" />
            <Popconfirm
              placement="bottomLeft"
              title="Are you sure you want close this drawer? All the selected cost allocations (if any) will be cleared."
              onConfirm={() => updateState({ showReverseAllocateLandingPriceDrawer: false })} okText="Yes"
              cancelText="No" >
              <img src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" />
            </Popconfirm>
          </div>
        </div>
        <ReverseAllocateLandingPrice
          entity={selectedAPI}
          callback={() => {
            updateState({ showReverseAllocateLandingPriceDrawer: false });
            manualRefetch();
          }}
        />
      </Drawer>
    </div>
  );
};

const mapStateToProps = ({
  UserReducers, AttachmentReducers, APInvoiceApprovalWorkflow, UpdateAPInvoiceStatus, TallyIntegrationReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  org: UserReducers.org,
  selectedAttachment: AttachmentReducers.selectedAttachment,
  getAttachmentByIdLoading: AttachmentReducers.getAttachmentByIdLoading,
  updateAttachmentLoading: AttachmentReducers.updateAttachmentLoading,
  priceMasking: UserReducers.priceMasking,
  updateAPInvoiceStatusLoading: UpdateAPInvoiceStatus.loading,
  apInvoiceApprovalWorkflowLoading: APInvoiceApprovalWorkflow.loading,
  syncTallyGRNLoading: TallyIntegrationReducers.syncTallyGRNLoading,
  pullTallyVendorPaymentLoading: TallyIntegrationReducers.pullTallyVendorPaymentLoading,
});

const mapDispatchToProps = (dispatch) => ({
  getAttachmentById: (entityId, entityName, callback) => dispatch(
    AttachmentActions.getAttachmentById(entityId, entityName, callback),
  ),
  updateAttachment: (payload, callback) => dispatch(
    AttachmentActions.updateAttachment(payload, callback),
  ),
  downloadDocument: (payload, document) => dispatch(AnalyticsActions.downloadDocument(payload, document)),
  updateAPInvoiceStatus: (payload, callback) => dispatch(UpdateAPInvoiceStatus.actions.request(payload, callback)),
  apInvoiceApprovalWorkflow: (payload, callback) => dispatch(APInvoiceApprovalWorkflow.actions.request(payload, callback)),
  syncTallyGRN: (payload, callback) => dispatch(TallyIntegrationActions.syncTallyGRN(payload, callback)),
  pullTallyVendorPayment: (payload, callback) => dispatch(TallyIntegrationActions.pullTallyVendorPayment(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(AccountPayableInvoiceView));