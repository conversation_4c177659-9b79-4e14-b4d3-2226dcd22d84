// React & Redux
import React, { Fragment } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';

// Ant Design
import {
  Table, Select, Checkbox, Popconfirm,
  Dropdown,
  Tag,
} from 'antd';

// UUID
import { v4 as uuidv4 } from 'uuid';

// FontAwesome Icons
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTrashCan, faGear,
} from '@fortawesome/free-solid-svg-icons';

// Constants & Helpers
import { QUANTITY, DEFAULT_CUR_ROUND_OFF, entityNameEnum } from '@Apis/constants';
import Helpers from '@Apis/helpers';

// UI Components
import PRZText from '@Components/Common/UI/PRZText';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import PRZSelect from '../../../../Common/UI/PRZSelect';
import QuickAddButton from '@Components/Common/UILib/QuickAddButton';
import GRNLineCustomField from '../../../GoodsReceiving/GRNForm/GRNFormLines/GRNLineCustomField';

// Custom & Domain-Specific Components
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import ProductDescriptions from '../../../../Common/ProductDescriptions';
import ProductCategoryLabel from '../../../../Common/ProductCategoryLabel';
import SelectTax from '../../../../Admin/Common/SelectTax';

import TaxableType from '@Components/Purchase/Common/TaxableType';
import NatureOfTransaction from '@Components/Purchase/Common/NatureOfTransaction';
import { taxableTypeEnums } from '@Components/Purchase/Common/constants';
import { natureOfTransactionOptionCategory } from '@Components/Purchase/Common/NatureOfTransaction/natureOfTransactionConstants';
// Styles
import './style.scss';

const { Option } = Select;

const APInvoiceFormLines = ({
  user,
  MONEY,
  priceMasking,
  customClassName,
  apInvoiceTableData,
  handleProductChangeValue,
  handleProductChange,
  apInvoiceType,
  addNewRow,
  updateAPInvoiceTableData,
  tenantDepartmentId,
  selectedTenant,
  formSubmitted,
  handleMultiProductChange,
  loading,
  billFromState,
  billToState,
  isLineWiseDiscount,
  onChangeLineWiseDiscount,
  discountType,
  discountPercentage,
  visibleLineCfs,
  customFieldVisibilityChange,
  selectedCurrencyName,
  apiId,
  isCarryForward,
  handleDelete,
  handleFullQuantity,
  customLineInputChange,
  isVendorOverseas,
}) => {
  const { isDataMaskingPolicyEnable, isHideCostPrice } = priceMasking;

  const isTallyConnected = user?.user_tenants?.find((item) => item?.tenant_id === selectedTenant)?.it_id;

  const getColumns = () => {
    const apInvoiceColumns = [
      {
        title: '#',
        fixed: 'left',
        width: customClassName ? 'auto' : '40px',
        render: (item) => (item?.sq_no || apInvoiceTableData?.length),
      },
      {
        title: 'Product Name',
        fixed: 'left',
        width: '230px',
        render: (item) => (
          <div className="table__product-name">
            <ProductFilterV2
              customClass="product_selector"
              record={{
                ...item,
                internal_sku_code: item?.product_sku_info?.internal_sku_code,
                ref_product_code: item?.product_sku_info?.ref_product_code,
              }}
              required
              isTextarea
              isPurchaseProduct
              productTypes={item?.isLandedCost ? ['SERVICE'] : ['STORABLE', 'SERVICE', 'NON_STORABLE']}
              handleProductChangeValue={handleProductChangeValue}
              handleProductChange={handleProductChange}
              showClear={apInvoiceType === 'ADHOC'}
              onClear={(key) => {
                const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                const newData = copyData.map((items) => {
                  if (items.key === key) {
                    return {
                      key: uuidv4(),
                      asset1: '',
                      product_sku_name: '',
                      quantity: '',
                      selling_price: '',
                      taxId: '',
                      lot: '',
                      product_sku_id: null,
                      product_sku_info: null,
                      child_taxes: [{
                        tax_amount: 0,
                        tax_type_name: '',
                      }],
                      tally_purchase_account: null,
                    };
                  }
                  return {
                    ...items,
                  };
                });
                addNewRow();
                updateAPInvoiceTableData(newData);
              }}
              excludedProducts={
                apInvoiceTableData?.map((items) => JSON.stringify(items?.tenant_product_id)) || []
              }
              tenantDepartmentId={tenantDepartmentId}
              selectedTenant={selectedTenant}
              showError={formSubmitted && !item?.product_sku_name}
              enableAdvanceSearch
              handleMultiProductChange={handleMultiProductChange}
              loading={loading}
              showOnlyServiceProducts={item?.isLandedCost}
            />
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '5px' }}>
              {item?.product_sku_info?.product_type &&
                (<Tag color={item?.product_sku_info?.product_type === 'SERVICE' ? 'blue' : 'green'}>
                  {item?.product_sku_info?.product_type?.replaceAll('_', ' ')?.toProperCase()}
                </Tag>)}

              {item?.isLandedCost &&
                (<Tag color='volcano' style={{ fontSize: '10px' }}>
                  Landed Cost
                </Tag>)}
            </div>
            {item?.productCategoryInfo?.category_path?.length > 0 && (
              <ProductCategoryLabel
                categoryPath={item?.productCategoryInfo?.category_path}
                categoryName={item?.productCategoryInfo?.category_path?.at(
                  -1,
                )}
                containerStyle={{
                  width: 'fit-content',
                }}
              />
            )}
            {/* {item?.product_sku_info?.product_type === 'SERVICE' && <H3Text text="Service" className="table-subscript-tag" />}
            {item?.product_sku_info?.product_type === 'NON_STORABLE' && <H3Text text="Non Storable" className="table-subscript-tag" />} */}
            {apInvoiceType === 'MULTIGRN' && (<Link to={`/purchase/goods-receiving/view/${item?.source_id}`} target="_blank" >{<H3Text text={item?.sourceNumber} />}</Link>)}
            {
              !item?.remarkRequired
              && (
                <QuickAddButton
                  label="Add Description"
                  onClick={() => {
                    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                    const newData = copyData.map((record) => {
                      if (record.key === item.key) {
                        return { ...record, remarkRequired: !item?.remarkRequired };
                      }
                      return record;
                    });
                    if (JSON.stringify(apInvoiceTableData) !== JSON.stringify(newData)) {
                      updateAPInvoiceTableData(newData);
                    }
                  }}
                />
              )
            }
            {item?.remarkRequired ? (
              <ProductDescriptions
                descriptions={item?.remarks}
                modules={{
                  toolbar: false,
                }}
                onChange={(e) => {
                  const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                  const newData = copyData.map((record) => {
                    if (item.key === record.key) {
                      return { ...record, remarks: e, remarkRequired: !!e?.replace(/<[^>]+>/g, '') };
                    }
                    return record;
                  });

                  if (JSON.stringify(apInvoiceTableData) !== JSON.stringify(newData)) {
                    updateAPInvoiceTableData(newData);
                  }
                }}
                className="invoice-table__remarks"
                placeholder="Add description of this item.."
                key={item.key}
              />
            ) : ''}

          </div>
        ),
      },
      {
        title: 'Quantity',
        dataIndex: 'quantity',
        width: '180px',
        render: (text, record) => (
          <div className="create-api-table__input">
            <div className="create-api-table__input-wrapper" style={{ marginTop: '3px', padding: '0px', display: 'block' }}>
              <H3FormInput
                value={record?.quantity}
                type="number"
                containerClassName={`orgInputContainer ${(formSubmitted && Number(record.quantity) <= 0) ? 'create-pr__input' : ''}`}
                labelClassName="orgFormLabel"
                inputClassName="orgFormInput"
                onChange={(e) => {
                  const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                  const discountValue = Number(record?.discount) || 0;
                  const totalPrice = (Number(e.target.value) * record.offer_price);
                  const taxableValue = record?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
                    : Math.max(totalPrice - discountValue, 0);

                  copyData.map((item) => {
                    if (record.grn_line_id === item.grn_line_id && item.key === record.key) {
                      const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')?.cfId;
                      item.quantity = e.target.value;
                      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billFromState(), billToState())?.tax_info?.child_taxes;
                      item.assessable_value = record.isManualOverride ? record.assessable_value : Number(e.target.value) * record.offer_price;
                      item.lineCustomFields = customLineInputChange(e.target.value, cfId, item, true);
                    }
                    return apInvoiceTableData;
                  });
                  updateAPInvoiceTableData(copyData);
                }}
              />
            </div>
          </div>
        ),
        hidden: (apInvoiceType !== 'ADHOC'),
      },
      {
        title: 'Quantity',
        width: '180px',
        render: (item) => {
          return (
            <div className="create-api-table__input">
              {item.grn_line_id && (
                <div className="create-api-table__input-wrapper">
                  <H3Text text="Received" className="create-api-table__input-label" />
                  <div>
                    {QUANTITY(item?.received_qty, item?.uom_info?.[0]?.precision)}
                    &nbsp;
                    {item?.uom_info?.[0]?.uqc?.toProperCase()}
                  </div>
                </div>
              )}
              <div className="create-api-table__input-wrapper create-api-table__input-wrapper-small" style={{ marginTop: '3px' }}>
                <H3Text text="Invoice" className="create-api-table__input-label" />
                <H3FormInput
                  labelClassName="orgFormLabel"
                  inputClassName={`orgFormInput ${(formSubmitted && Number(item?.ap_received_qty) <= 0) ? 'orgFormInputError' : ''}`}
                  value={item.ap_received_qty}
                  name="valid quantity"
                  type="number"
                  onChange={(event) => {
                    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                    const discountValue = Number(item?.discount) || 0;
                    const totalPrice = (Number(event.target.value) * item.offer_price);
                    const taxableValue = item?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
                      : Math.max(totalPrice - discountValue, 0);

                    const updatedData = copyData.map((obj) => {
                      if ((obj.po_line_id === item.po_line_id && item.po_line_id) || (obj.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj.key === item?.key && item?.key)) {
                        const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')?.cfId;
                        return {
                          ...obj,
                          ap_received_qty: event.target.value,
                          child_taxes: Helpers.computeTaxation(taxableValue, item?.taxInfo, billFromState(), billToState())?.tax_info?.child_taxes,
                          assessable_value: item?.isManualOverride ? item?.assessable_value : Number(event.target.value) * item.offer_price,
                          lineCustomFields: customLineInputChange(event.target.value, cfId, item, true),

                        };
                      }
                      return obj;
                    });
                    updateAPInvoiceTableData(updatedData);
                  }}
                />
              </div>
            </div>
          );
        },
        hidden: (apInvoiceType === 'ADHOC'),
      },
      {
        title: 'Pricing',
        width: '100px',
        render: (item) => (
          <div className="create-api-table__input">
            <div className="create-api-table__input-wrapper create-api-table__input-wrapper-small-2" style={{ marginBottom: '3px' }}>
              <H3Text text="Unit Price" className="create-api-table__input-label" />
              &nbsp;
              <H3FormInput
                labelClassName="orgFormLabel"
                inputClassName={`orgFormInput ${(formSubmitted && (!item?.offer_price || item?.offer_price == 0)) ? 'orgFormInputError' : ''} `}
                value={item?.offer_price}
                name="unit price"
                type="number"
                onChange={(event) => {
                  const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                  const discountValue = Number(item.discount) || 0;
                  const totalPrice = (Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty)
                    * Number(event.target.value));
                  const taxableValue = item?.lineDiscountType === 'Percent' ? (
                    totalPrice * (1 - discountValue / 100)
                  ) : (
                    totalPrice - discountValue
                  );

                  const updatedData = copyData.map((obj) => {
                    if ((apiId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) && obj.key === item.key) {
                      const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Rate')?.cfId;
                      return {
                        ...obj,
                        offer_price: event.target.value,
                        child_taxes: Helpers.computeTaxation(taxableValue, item?.taxInfo, billFromState(), billToState())?.tax_info?.child_taxes,
                        assessable_value: item?.isManualOverride ? item?.assessable_value : Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty) * Number(event.target.value),
                        lineCustomFields: customLineInputChange(event.target.value, cfId, item, true),
                      };
                    }
                    return obj;
                  });
                  updateAPInvoiceTableData(updatedData);
                }}
                hideInput={isDataMaskingPolicyEnable && isHideCostPrice}
                popOverMessage="You don't have access to view unit price"
              />
            </div>
            {apInvoiceType === 'ADHOC' ? (
              <div className="flex-display create-api-table__input-wrapper create-api-table__input-wrapper-small-2" style={{ alignItems: 'center', marginBottom: '3px', whiteSpace: 'nowrap' }}>
                <H3Text text="UOM" className="create-api-table__input-label" />
                &nbsp;
                <div>
                  <div className="orgInputContainer">
                    <PRZSelect
                      filterOption={false}
                      className=""
                      value={item?.uomId}
                      onChange={(value) => {
                        const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                        copyData.map((data) => {
                          if (data.grn_line_id === item.grn_line_id && data.key === item.key) {
                            data.uomId = value;
                            data.uomInfo = data?.uom_list?.find(
                              (rec) => rec?.uom_id === value,
                            );
                            data.uom_info = data?.uom_list?.find(
                              (rec) => rec?.uom_id === value,
                            );
                          }
                          return data;
                        });
                        updateAPInvoiceTableData(copyData);
                      }}
                      disabled={!item?.uom_list}
                    >
                      {item?.uom_list?.map((uom) => (

                        <Option
                          key={uom?.uom_id}
                          value={uom?.uom_id}
                        >
                          {`${uom?.uqc?.toProperCase()} (${uom?.uom_name?.toProperCase()})`}

                        </Option>
                      ))}
                    </PRZSelect>
                  </div>
                </div>
              </div>
            ) : ''}
            <div className={isVendorOverseas ? 'display-none' : 'flex-display create-api-table__input-wrapper create-api-table__input-wrapper-small-2'} style={{ alignItems: 'center', marginBottom: '3px', whiteSpace: 'nowrap' }}>
              <H3Text text="Tax" className="create-api-table__input-label" />
              &nbsp;
              <div
                className="orgInputContainer"
              >
                <SelectTax
                  onChange={(value) => {
                    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                    const discountValue = Number(item.discount) || 0;
                    const totalPrice = (Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty)
                      * item?.offer_price);
                    const taxableValue = item?.lineDiscountType === 'Percent' ? (
                      totalPrice * (1 - discountValue / 100)
                    ) : (
                      totalPrice - discountValue
                    );

                    const updatedData = copyData.map((obj) => {
                      if ((apiId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) && obj.key === item.key) {
                        return {
                          ...obj,
                          taxId: value?.tax_id,
                          taxInfo: value,
                          child_taxes: Helpers.computeTaxation(taxableValue, value, billFromState(), billToState())?.tax_info?.child_taxes,
                        };
                      }
                      return obj;
                    });
                    updateAPInvoiceTableData(updatedData);
                  }}
                  selectedGST={item.taxId}
                />
              </div>
            </div>
            <div className="flex-display create-api-table__input-wrapper create-api-table__input-wrapper-small-2" style={{ alignItems: 'center', marginBottom: '3px', whiteSpace: 'nowrap' }}>
              <H3Text text="Discount" className="create-api-table__input-label" />
              &nbsp;
              {!isLineWiseDiscount ? (
                <Fragment>
                  <H3FormInput
                    value={item?.discount}
                    type="number"
                    containerClassName={`orgInputContainer ${(formSubmitted && Number(item.discount) < 0) ? 'create-pr__input' : ''}`}
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    onChange={(event) => {
                      const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                      const discountValue = Number(event.target.value) || 0;
                      const totalPrice = (Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty)
                        * item?.offer_price);
                      const taxableValue = item?.lineDiscountType === 'Percent'
                        ? totalPrice * (1 - discountValue / 100)
                        : Math.max(totalPrice - discountValue, 0);

                      const updatedData = copyData.map((obj) => {
                        if ((apiId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) && obj.key === item.key) {
                          return {
                            ...obj,
                            discount: event.target.value,
                            child_taxes: Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState(), billToState())?.tax_info?.child_taxes,
                          };
                        }
                        return obj;
                      });
                      updateAPInvoiceTableData(updatedData);
                    }}
                    disabled={isLineWiseDiscount || !Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty) || !item?.offer_price}
                  />
                  <div className="orgInputContainer discount-type">
                    <PRZSelect
                      value={item?.lineDiscountType}
                      onChange={(value) => {
                        const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                        const discountValue = Number(item?.discount) || 0;
                        const totalPrice = (Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty)
                          * item?.offer_price);
                        const taxableValue = value === 'Percent'
                          ? totalPrice * (1 - discountValue / 100)
                          : Math.max(totalPrice - discountValue, 0);

                        copyData.map((obj) => {
                          if ((apiId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) && obj.key === item.key) {
                            obj.lineDiscountType = value;
                            obj.child_taxes = Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState(), billToState())?.tax_info?.child_taxes;
                          }
                          return obj;
                        });
                        updateAPInvoiceTableData(copyData);
                      }}
                      style={{
                        width: '50px',
                      }}
                      disabled={isLineWiseDiscount}
                      placeholder=""
                    >
                      <Option key="Amount" value="Amount">
                        {`${selectedCurrencyName?.currency_symbol} `}
                      </Option>
                      <Option key="Percent" value="Percent">
                        %
                      </Option>
                    </PRZSelect>
                  </div>
                </Fragment>
              ) : (
                <div className="orgFormInput">
                  <H3Text
                    text={
                      item?.discount ? (item?.lineDiscountType === 'Percent' ? `${(item?.discount)?.toFixed(DEFAULT_CUR_ROUND_OFF)}%`
                        : MONEY((item?.discount)?.toFixed(DEFAULT_CUR_ROUND_OFF), selectedCurrencyName?.currency_code))
                        : (item?.lineDiscountType === 'Percent' ? '0%' : MONEY(0, selectedCurrencyName?.currency_code))
                    }
                  />
                </div>
              )}
            </div>

          </div>
        )
        ,
      },
      {
        title: 'Purchase Account',
        render: (item) => (
          <React.Fragment>
            {' '}
            <div className="create-grn-view__input-row">
              <div className="create-grn-view__input-row__input line_level_purchase_account">
                <PRZSelect
                  value={item?.tally_purchase_account}
                  onChange={(value) => {
                    // if (apInvoiceType === 'ADHOC') {
                    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                    const updatedData = copyData.map((obj) => {
                      if ((obj.grn_line_id === item.grn_line_id && obj?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                        return {
                          ...obj,
                          tally_purchase_account: value
                        };
                      }
                      return obj;
                    });
                    updateAPInvoiceTableData(updatedData);
                  }}
                  style={{
                    width: '150px',
                    borderRadius: '2px',
                    marginBottom: '5px',
                  }}
                  showError={formSubmitted && !item?.tally_purchase_account}
                  errorName="Purchase account"
                  errorClassName="input-error"
                >
                  {user?.tenant_info?.purchase_account_list?.map((purchase) => (
                    <Option key={purchase?.purchase_account_name} value={purchase?.purchase_account_name}>
                      {purchase.purchase_account_name}
                    </Option>
                  ))}
                </PRZSelect>
              </div>
            </div>
          </React.Fragment>
        ),
        className: user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ? '' : 'display-none',
      },
      {
        title: 'Total',
        render: (item) => {
          const discountValue = Number(item.discount) || 0;

          const totalPrice = (Number(apInvoiceType === 'ADHOC' ? item?.quantity : item?.ap_received_qty)
            * item?.offer_price);

          const taxableValue = item?.lineDiscountType === 'Percent' ? (
            totalPrice * (1 - discountValue / 100)
          ) : (
            totalPrice - discountValue
          );

          return (
            <div style={
              { minWidth: '60px' }
            }
            >
              <H3Text
                text={item.taxInfo
                  ? MONEY(
                    Number.isNaN(Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState(), billToState())?.taxed_value)
                      ? 0
                      : Math.max(Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState(), billToState())?.taxed_value, 0),
                    selectedCurrencyName?.currency_code,
                  )
                  : 0}
                hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                popOverMessage="You don't have access to view total amount"
              />
            </div>
          );
        },
      },
      {
        title: 'Assessable Value',
        width: '200px',
        render: (text, record) => (
          <div className='flex-align-c' style={{ gap: '5px' }}>
            <PRZText text={selectedCurrencyName?.currency_symbol} />
            <div className="create-api-table__input" style={{ maxWidth: '150px' }}>
              <div className="create-api-table__input-wrapper" style={{ marginTop: '3px', padding: '0px', display: 'block' }}>
                <H3FormInput
                  value={record?.assessable_value}
                  type="number"
                  containerClassName={'orgInputContainer'}
                  labelClassName="orgFormLabel"
                  inputClassName="orgFormInput"
                  onChange={(e) => {
                    const copyData = structuredClone(apInvoiceTableData);
                    copyData.map((item) => {
                      if (record.grn_line_id === item.grn_line_id && item.key === record.key) {
                        item.assessable_value = e.target.value;
                        item.isManualOverride = true;
                      }
                      return apInvoiceTableData;
                    });
                    updateAPInvoiceTableData(copyData);
                  }}
                />
              </div>
            </div>
          </div>
        ),
        hidden: !isTallyConnected,
      },
      {
        title: 'Taxation & Nature',
        // width: '150px',
        render: (item) => (
          <div className="create-api-table__input">
            <div className="flex-display create-api-table__input-wrapper create-api-table__input-wrapper-small-2" style={{ alignItems: 'center', marginBottom: '3px', whiteSpace: 'nowrap' }}>
              <H3Text text="Taxable Type" className="create-api-table__input-label" style={{ width: '80px' }} />
              &nbsp;
              <div>
                <div className="orgInputContainer">
                  <TaxableType
                    value={item?.taxableType}
                    onChange={(value) => {
                      const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                      copyData.map((data) => {
                        if (data.grn_line_id === item.grn_line_id && data.key === item.key) {
                          data.taxableType = value;
                          data.natureOfTransaction = value === taxableTypeEnums.Non_GST ? 'System Inferred' : '';
                        }
                        return data;
                      });
                      updateAPInvoiceTableData(copyData);
                    }}
                  />
                </div>
              </div>
            </div>
            <div className={'flex-display create-api-table__input-wrapper create-api-table__input-wrapper-small-2'} style={{ alignItems: 'center', marginBottom: '3px', whiteSpace: 'nowrap' }}>
              <H3Text text="Nature of Txn." className="create-api-table__input-label" style={{ width: '80px' }} />
              &nbsp;
              <div
                className="orgInputContainer"
              >
                <NatureOfTransaction
                  value={item?.natureOfTransaction}
                  onChange={(value) => {
                    const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                    copyData.map((data) => {
                      if (data.grn_line_id === item.grn_line_id && data.key === item.key) {
                        data.natureOfTransaction = value;
                      }
                      return data;
                    });
                    updateAPInvoiceTableData(copyData);
                  }}
                  taxableType={item?.taxableType}
                  entityName={entityNameEnum.ACCOUNT_PAYABLE_INVOICE}
                  optionTypes={natureOfTransactionOptionCategory.ALL}
                />
              </div>
            </div>
          </div>
        ),
        hidden: !isTallyConnected
      },
      {
        title: '',
        fixed: 'right',
        render: (text, record) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Popconfirm title="Are you sure you want to remove this item?" onConfirm={() => handleDelete(record?.key, record?.grn_line_id)} placement="topRight">
              <div className="delete-line-button__new" style={{ marginLeft: 'auto' }}>
                <FontAwesomeIcon icon={faTrashCan} />
              </div>
            </Popconfirm>

          </div>
        ),
      },
    ].filter((item) => item.hidden !== true);

    if (visibleLineCfs?.length) {
      apInvoiceColumns.splice(4, 0, {
        title: 'CUSTOM FIELDS',
        width: '236px',
        render: (record) => (
          <GRNLineCustomField
            cfWrapperClassName="create-grn__input-row"
            labelClassName="create-grn__input-row__label"
            customFields={
              (record?.lineCustomFields || []).filter(
                (cf) => cf.fieldName !== 'Rate' && cf.fieldName !== 'Quantity'
              )
            } formSubmitted={formSubmitted}
            customInputChange={(value, cfId) => customLineInputChange(value, cfId, record)}
            isHorizontalUi
            hideTitle
            isCarryForward={isCarryForward}
          />
        ),
      });
    }
    return apInvoiceColumns;
  };

  const getAPInvoiceColumns = () => {
    return getColumns();
  };

  const getDataSource = (data) => {
    if (data) {
      const tempData = [];
      const grnData = data;
      for (let i = 0; i < grnData.length; i++) {
        tempData.push({
          ...grnData[i],
          sq_no: i + 1,
        });
      }
      return tempData;
    }
    return [];
  };

  const apInvoiceTableDataLength = apInvoiceTableData?.length;
  return (
    <div>
      <Table
        title={() => (
          <div className="create-api-title__wrapper">
            <H3Text text={`AP Invoice Line Items (${apInvoiceTableData?.filter((item) => item.product_sku_name?.length > 0)?.length || '0'})`} className="create-po__input-row__label" />
            <div className="create-api-title-left">
              <Dropdown
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                overlay={(
                  <div className="line_actions-wrapper">
                    <Checkbox
                      checked={isLineWiseDiscount}
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(apInvoiceTableData));
                        copyData.map((i) => {
                          i.discount = 0;
                          i.lineDiscountType = e.target.checked ? discountType : 'Percent';
                          i.child_taxes = Helpers.computeTaxation(apInvoiceType === 'ADHOC' ? i?.quantity : i?.ap_received_qty * i?.offer_price, i?.taxInfo, billFromState(), billToState())?.tax_info?.child_taxes;
                        });
                        onChangeLineWiseDiscount(e.target.checked);
                        updateAPInvoiceTableData(copyData);
                        discountPercentage(0);
                      }}
                      disabled={loading}
                      className="line_actions"
                    >
                      AP Invoice Level discount
                    </Checkbox>
                  </div>
                )}
                placement="topRight"
                trigger={['click']}
              >
                <div style={{ marginRight: '5px' }} className="custom-doc-columns__wrapper">
                  Actions
                </div>
              </Dropdown>

              <Dropdown
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                overlay={(
                  <div className="custom-doc-columns__content">
                    <div className="custom-doc-columns__content-section">
                      <H3Text text="Visible Columns" className="custom-doc-columns__content-title" />
                      {
                        visibleLineCfs && visibleLineCfs.map((item) => (
                          <div key={item?.cfId}>
                            <Checkbox
                              disabled={item?.isRequired}
                              checked={item?.visible}
                              onChange={() => {
                                let grnTableDataCopy = JSON.parse(JSON.stringify(apInvoiceTableData));
                                grnTableDataCopy = grnTableDataCopy?.map((data) => {
                                  const newLineCustomFields = data?.lineCustomFields?.map((customField) => {
                                    if (customField?.cfId === item?.cfId) {
                                      return { ...customField, visible: !item?.visible };
                                    }
                                    return { ...customField };
                                  });

                                  return { ...data, lineCustomFields: newLineCustomFields };
                                });

                                updateAPInvoiceTableData(grnTableDataCopy);
                                customFieldVisibilityChange(!item?.visible, item?.cfId);
                              }}
                            >
                              {item?.fieldName}
                            </Checkbox>
                          </div>
                        ))
                      }
                    </div>
                  </div>
                )}
                trigger={['click']}
                placement="topRight"
              >
                <div style={{ marginLeft: '5px' }} className="custom-doc-columns__wrapper">
                  <FontAwesomeIcon icon={faGear} />
                  &nbsp;
                  More Columns
                </div>
              </Dropdown>
            </div>
          </div>
        )}
        bordered
        showHeader
        loading={loading}
        size="small"
        scroll={{ x: 'max-content' }}
        tableLayout="unset"
        top="none"
        bottom="bottomRight"
        columns={getAPInvoiceColumns()}
        dataSource={getDataSource(apInvoiceTableData)}
        pagination={false}
        rowKey={(record) => record.key}
      />
    </div>
  );
};

const mapStateToProps = ({
  UserReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  priceMasking: UserReducers.priceMasking,

});

const mapDispatchToProps = (dispatch) => ({

});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(APInvoiceFormLines));
