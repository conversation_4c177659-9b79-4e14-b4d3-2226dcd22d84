import React, { Fragment, memo } from 'react';

// Helpers
import Helpers from '@Apis/helpers';

// UI Components
import PRZButton from '@Components/Common/UI/PRZButton';
import PRZConfirmationPopover from '@Components/Common/UI/PRZConfirmationPopover';
import PRZInput from '@Components/Common/UI/PRZInput';
import PRZText from '@Components/Common/UI/PRZText';

// Common Components
import RestrictedAccessMessage from '@Components/Common/RestrictedAccess/RestrictedAccessMessage';

// Local Imports
import { ACTIONS } from '../reducer';

function GRNFormFooter({
  localDispatch,
  restrictMessage,
  selectedGRN,
  user,
  updateDocumentReason,
  createGRNLoading,
  updateGRNLoading,
  buttonClick,
  isAdhocGrnAllowed,
  handleCreateGRN,
}) {
  return (
    <div className="form__footer">
      {restrictMessage()?.length > 0 && (<RestrictedAccessMessage
        message={restrictMessage()}
      />)}
      {(selectedGRN) ? (
        <Fragment>
          {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
            <PRZConfirmationPopover
              title="Are you sure you want to update?"
              content={
                <Fragment>
                  <PRZText text="Reason" required />
                  <PRZInput
                    placeholder="Enter update reason"
                    value={updateDocumentReason}
                    onChange={(e) => localDispatch({ type: ACTIONS.UPDATE_FIELDS, payload: { updateDocumentReason: e.target.value } })}
                  />
                </Fragment>
              }
              onConfirm={() => {
                if (!createGRNLoading || !updateGRNLoading) {
                  handleCreateGRN(false, 'DRAFT');
                }
              }}
              confirmButtonText="Confirm"
              cancelButtonText="Back"
              confirmDisabled={!updateDocumentReason}
            >
              <PRZButton
                id="save-as-draft"
                type="default"
                wrapperStyle={{ marginRight: '10px' }}
                buttonStyle={{
                  width: '130px',
                  height: '40px',
                  border: '1px solid #2d7df7',
                  color: '#2d7df7',
                }}
                isLoading={(createGRNLoading || updateGRNLoading) && buttonClick !== 'APPROVE'}
                disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
              >
                Save as Draft
              </PRZButton>
            </PRZConfirmationPopover>
          )}
          {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
            <PRZConfirmationPopover
              title="Are you sure you want to update?"
              content={
                <Fragment>
                  <PRZText text="Reason" required />
                  <PRZInput
                    placeholder="Enter update reason"
                    value={updateDocumentReason}
                    onChange={(e) => localDispatch({ type: ACTIONS.UPDATE_FIELDS, payload: { updateDocumentReason: e.target.value } })}
                  />
                </Fragment>
              }
              onConfirm={() => {
                if (!createGRNLoading || !updateGRNLoading) {
                  handleCreateGRN(true, 'ISSUED');
                }
              }}
              confirmButtonText="Confirm"
              cancelButtonText="Back"
              confirmDisabled={!updateDocumentReason}
            >
              <PRZButton
                id="save-and-issue"
                isLoading={(createGRNLoading || updateGRNLoading) && buttonClick === 'APPROVE'}
                disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
                buttonStyle={{ width: '130px', height: '40px' }}
              >
                Save and Issue
              </PRZButton>
            </PRZConfirmationPopover>
          )}
        </Fragment>
      ) : (
        <Fragment>
          {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
            <PRZButton
              id="save-as-draft"
              type="default"
              onClick={() => {
                if (!createGRNLoading || !updateGRNLoading) {
                  handleCreateGRN(false, 'DRAFT');
                }
              }}
              isLoading={(createGRNLoading || updateGRNLoading) && buttonClick !== 'APPROVE'}
              disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
              wrapperStyle={{ marginRight: '10px' }}
              buttonStyle={{
                width: '130px',
                height: '40px',
                border: '1px solid #2d7df7',
                color: '#2d7df7',
              }}
            >
              Save as Draft
            </PRZButton>
          )}
          {Helpers.getPermission(Helpers.permissionEntities.GOOD_RECEIVING, Helpers.permissionTypes.CREATE, user) && (
            <PRZButton
              id="save-and-issue"
              onClick={() => {
                if (!createGRNLoading || !updateGRNLoading) {
                  handleCreateGRN(true, 'ISSUED');
                }
              }}
              isLoading={(createGRNLoading || updateGRNLoading) && buttonClick === 'APPROVE'}
              disabled={createGRNLoading || updateGRNLoading || isAdhocGrnAllowed}
              buttonStyle={{ width: '130px', height: '40px' }}
            >
              Save and Issue
            </PRZButton>
          )}
        </Fragment>
      )}
    </div>
  );
}

export default memo(GRNFormFooter);