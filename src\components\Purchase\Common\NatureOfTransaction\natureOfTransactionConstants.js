import { entityNameEnum } from '@Apis/constants';
import { taxableTypeEnums } from '../constants';

const natureOfTransactionOptionCategory = {
  PURCHASE: 'PURCHASE',
  SALES: 'SALES',
  ALL: 'ALL',
};

const natureOfTransactionOptionsConstants = {
  [entityNameEnum.ACCOUNT_PAYABLE_INVOICE]: {
    [natureOfTransactionOptionCategory.PURCHASE]: {
      [taxableTypeEnums.Exempted]: [
        'System Inferred',
        'Branch Transfer Inward',
        'Local Purchase - Exempt',
        'Interstate Purchase - Exempt',
        'Local Purchase Deemed Exports - Exempt',
        'Interstate Purchase Deemed Exports - Exempt',
        'Purchase from Composition Dealer',
        'Imports - Exempt',
        'Purchase from SEZ - Exempt',
        'Purchase from SEZ - LUT/Bond',
        'Purchase from SEZ (Without Bill of Entry) - Exempt',
        'High Sea Purchases',
        'Purchase from Customs Bonded Warehouse',
      ],
      [taxableTypeEnums.Nil_Rated]: [
        'System Inferred',
        'Local Purchase - Nil Rated',
        'Interstate Purchase - Nil Rated',
        'Local Purchase Deemed Exports - Nil Rated',
        'Interstate Purchase Deemed Exports - Nil Rated',
        'Imports - Nil Rated',
        'Purchase from SEZ - Nil Rated',
        'Purchase from SEZ (Without Bill of Entry) - Nil Rated',
      ],
      [taxableTypeEnums.Taxable]: [
        'System Inferred',
        'Local Purchase - Taxable',
        'Interstate Purchase - Taxable',
        'Local Purchase Deemed Exports - Taxable',
        'Interstate Purchase Deemed Exports - Taxable',
        'Imports - Taxable',
        'Purchase from SEZ - Taxable',
        'Purchase from SEZ (Without Bill of Entry) - Taxable',
      ],
      [taxableTypeEnums.Non_GST]: [
        'System Inferred',
      ],
    }
  },

  [entityNameEnum.EXPENSES]: {
    [natureOfTransactionOptionCategory.PURCHASE]: {
      [taxableTypeEnums.Exempted]: [
        'System Inferred',
        'Branch Transfer Inward',
        'Local Purchase - Exempt',
        'Interstate Purchase - Exempt',
        'Local Purchase Deemed Exports - Exempt',
        'Interstate Purchase Deemed Exports - Exempt',
        'Purchase from Composition Dealer',
        'Imports - Exempt',
        'Purchase from SEZ - Exempt',
        'Purchase from SEZ - LUT/Bond',
        'Purchase from SEZ (Without Bill of Entry) - Exempt',
        'High Sea Purchases',
        'Purchase from Customs Bonded Warehouse',
      ],
      [taxableTypeEnums.Nil_Rated]: [
        'System Inferred',
        'Local Purchase - Taxable',
        'Interstate Purchase - Taxable',
        'Local Purchase Deemed Exports - Taxable',
        'Interstate Purchase Deemed Exports - Taxable',
        'Imports - Taxable',
        'Purchase from SEZ - Taxable',
        'Purchase from SEZ (Without Bill of Entry) - Taxable',
      ],
      [taxableTypeEnums.Taxable]: [
        'System Inferred',
        'Local Purchase - Taxable',
        'Interstate Purchase - Taxable',
        'Local Purchase Deemed Exports - Taxable',
        'Interstate Purchase Deemed Exports - Taxable',
        'Imports - Taxable',
        'Purchase from SEZ - Taxable',
        'Purchase from SEZ (Without Bill of Entry) - Taxable',
      ],
      [taxableTypeEnums.Non_GST]: [
        'System Inferred',
      ],
    },

    [natureOfTransactionOptionCategory.SALES]: {
      [taxableTypeEnums.Exempted]: [
        'System Inferred',
        'Branch Transfer Outward',
        'Local Sales - Exempt',
        'Interstate Sales - Exempt',
        'Interstate Deemed Exports - Exempt',
        'Local Deemed Exports - Exempt',
        'Exports - Exempt',
        'Exports - LUT/Bond',
        'Sales to SEZ - Exempt',
        'Sales to SEZ - LUT/Bond',
        'High Sea Sales',
        'Sales from Customs Bonded Warehouse',
      ],
      [taxableTypeEnums.Nil_Rated]: [
        'System Inferred',
        'Local Sales - Nil Rated',
        'Interstate Sales - Nil Rated',
        'Interstate Deemed Exports - Nil Rated',
        'Local Deemed Exports - Nil Rated',
        'Exports - Nil Rated',
        'Sales to SEZ - Nil Rated',
      ],
      [taxableTypeEnums.Taxable]: [
        'System Inferred',
        'Local Sales - Taxable',
        'Interstate Sales - Taxable',
        'Interstate Deemed Exports - Taxable',
        'Local Deemed Exports - Taxable',
        'Exports - Taxable',
        'Sales to SEZ - Taxable',
      ],
      [taxableTypeEnums.Non_GST]: [
        'System Inferred',
      ],
    }
  }
};

export {
  natureOfTransactionOptionCategory,
  natureOfTransactionOptionsConstants,
};