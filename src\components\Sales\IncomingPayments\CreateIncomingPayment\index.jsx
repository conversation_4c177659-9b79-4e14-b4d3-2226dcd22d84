/* eslint-disable no-nested-ternary */
import React, { Component, Fragment } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import { Helmet } from 'react-helmet';
import {
  DatePicker, Select, Table, Upload, Checkbox, Drawer, Button,
} from 'antd';
import PropTypes from 'prop-types';
import {
  PlusOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import Constants, {
  H3_TITLE, toISTDate,
} from '@Apis/constants';
import H3Text from '@Uilib/h3Text';
import CustomerActions from '@Actions/customerActions';
import H3FormInput from '@Uilib/h3FormInput';
import PaymentOutgoingActions from '@Actions/paymentOutgoingActions';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import PaymentRequestActions from '@Actions/paymentRequestActions';
import InvoiceActions from '@Actions/invoiceActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import TenantActions from '@Actions/tenantActions';
import WorkflowActions from '@Actions/workflowActions';
import PayoutActions from '@Actions/payoutActions';
import H3Image from '@Uilib/h3Image';
import { cdnUrl } from "@Utils/cdnHelper";
import SelectCustomer from '../../../Common/SelectCustomer';
import CustomerForm from '../../Customer/CustomerHome/CreateCustomer/CustomerForm';
import './style.scss';
import PaymentModeActions from '../../../../actions/configurations/paymentModeAction';
import PRZSelect from '../../../Common/UI/PRZSelect';

const { Option } = Select;

const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

/**
 * @param status
 */
class CreateIncomingPayment extends Component {
  /**
   *
   * @param {*} props props
   * @param {*} state state
   * @return
   */
  static getDerivedStateFromProps(props, state) {
    const { getPaymentMode } = props;

    if (!props?.paymentModeResults?.length) {
      getPaymentMode('NOT_NUll');
    }
    if (props?.invoices?.data?.invoices?.filter((record) => ((record?.invoice_total_amount || (record?.invoice_grand_total * (record?.conversion_rate || 1))) - (record?.payment_made || (record?.total_payment_made * (record?.conversion_rate || 1))) - (record?.credits_applied * (record?.conversion_rate || 1))) > 0)?.[0]?.customer_id !== state?.invoiceData?.[0]?.customer_id) {
      return {
        ...state,
        invoiceData: props.invoices?.data?.invoices?.filter((record) => ((record?.invoice_total_amount || (record?.invoice_grand_total * (record?.conversion_rate || 1))) - (record?.payment_made || (record?.total_payment_made * (record?.conversion_rate || 1))) - (record?.credits_applied * (record?.conversion_rate || 1))) > 0)?.map((item) => ({ ...item, payment: 0 })),
        isUserReady: true,
      };
    }
    if (props.customFields && !state.isUserReadyOne) {
      return {
        ...state,
        customFields: props.customFields?.data?.custom_fields?.map((customField) => ({
          cfId: customField?.cf_id,
          fieldType: customField?.field_type,
          isRequired: customField?.is_required,
          isActive: customField?.is_active,
          fieldName: customField?.field_name,
          defaultData: customField?.default_data,
          placeholder: customField?.placeholder,
          fieldValue: customField?.field_type?.toUpperCase() !== 'ATTACHMENT' ? '' : [],
        })),
        isUserReadyOne: true,
      };
    }
    return state;
  }

  /**
   *
   * @param props
   */
  constructor(props) {
    super(props);
    this.state = {
      tenantId: '',
      paymentDate: dayjs(),
      selectedPaymentMode: '',
      columns: [
        {
          title: 'INVOICE NUMBER',
          render: (text, record) => (
            <Link to={`/sales/invoice/view/${record?.invoice_id}`} target="_blank">
              {record?.invoice_number}
            </Link>
          ),
        },
        // {
        //   title: 'CUSTOMER',
        //   render: (text, record) => (
        //     <div
        //       style={{
        //         color: '#2D7DF7', fontWeight: '600', fontSize: '12px', cursor: 'pointer',
        //       }}
        //       onClick={() => History.push(`/sales/customer/view/${record?.customer_id}`)}
        //     >
        //       {record?.customer_info?.customer_name || ' - '}
        //     </div>
        //   ),
        // },
        {
          title: 'ITEMS',
          render: (text, record) => `${record?.item_count}`,
        },
        {
          title: 'CREATED TIME',
          render: (text, record) => (
            <div>
              {toISTDate(record.created_at).format('DD/MM/YYYY')}
              <H3Text text={(record?.created_by_info ? `${record?.created_by_info.first_name} ${record?.created_by_info.last_name}` : 'Unknown')} className="table-subscript" />
            </div>
          ),

        },
        {
          title: 'DATE',
          render: (text, record) => `${record?.invoice_date ? toISTDate(record?.invoice_date)?.format('DD/MM/YYYY') : ''}`,
        },
        {
          title: 'TOTAL AMOUNT',
          dataIndex: 'seller_name',
          render: (text, record) => this.props.MONEY((record?.invoice_total_amount || (record?.invoice_grand_total * (record?.conversion_rate || 1))) + record?.invoice_round_off),
        },
        {
          title: 'AMOUNT DUE',
          dataIndex: 'seller_name',
          render: (text, record) => this.props.MONEY((record?.invoice_total_amount || (record?.invoice_grand_total * (record?.conversion_rate || 1))) - (record?.payment_made || (record?.total_payment_made * (record?.conversion_rate || 1))) - (record?.credits_applied * (record?.conversion_rate || 1)) + record?.invoice_round_off), // - Helpers.getValueTotalInObject(record?.credit_notes, 'cn_grand_total')
        },
        {
          title: 'PAYMENT',
          width: '150px',
          render: (text, record) => {
            const { amount, invoiceData } = this.state;
            return (
              <div>
                <H3FormInput
                  containerClassName="payment-amount__wrapper-outer "
                  labelClassName="orgFormLabel"
                  inputClassName="orgFormInput input"
                  type="number"
                  value={record?.payment}
                  onChange={(event) => {
                    const copyInvoiceData = JSON.parse(JSON.stringify(invoiceData));
                    const updateInvoiceData = copyInvoiceData.map((item) => {
                      if (item?.invoice_id === record?.invoice_id) {
                        return {
                          ...item,
                          payment: event.target.value,
                        };
                      }
                      return item;
                    });
                    this.setState({
                      invoiceData: updateInvoiceData,
                    });
                  }}
                  disabled={!amount || (!record?.payment && !this.checkMaxAmount()?.paymentExceed)}
                />
                <H3Text
                  text={(
                    <div>
                      Pay in Full
                    </div>
                  )}
                  onClick={() => this.checkMaxAmount()?.paymentExceed && this.handleFullPayment(record)}
                  className="goods-receiving__po_no-pay"
                />
                {((record?.payment > ((record?.grn_grand_total || (record?.invoice_total_amount + record?.invoice_round_of) || ((record?.invoice_grand_total * (record?.conversion_rate || 1)) + record?.invoice_round_of)) - (record?.payment_made || (record?.total_payment_made * (record?.conversion_rate || 1))) - (record?.credits_applied * (record?.conversion_rate || 1)))) || (record?.payment > ((record?.invoice_total_amount || (record?.invoice_grand_total * (record?.conversion_rate || 1))) - (record?.payment_made || (record?.total_payment_made * (record?.conversion_rate || 1))) - (record?.credits_applied * (record?.conversion_rate || 1)) + record?.invoice_round_off))) && <div className="input-error">Please enter valid amount*</div>}
              </div>
            );
          },
        },
      ],
      showAddCustomer: true,
      showNewCustomerModal: false,
    };
  }

  /**
   *
   */
  componentDidMount() {
    const {
      getInvoiceSuccess, getVendorWorkflows, user, getDocCFV2, getTenantsConfiguration,
    } = this.props;
    getTenantsConfiguration(user?.tenant_info?.tenant_id);
    getInvoiceSuccess(null);
    // getVendorWorkflows();
    // getDocCFV2(user?.tenant_info?.org_id, 'CUSTOMER_PAYMENT', '', '', '');
  }

  /**
   * @param {*} value
   * @param record
   */
  handleFullPayment(record) {
    const { invoiceData } = this.state;
    const copyInvoiceData = invoiceData?.map((item) => {
      if (item?.invoice_id === record?.invoice_id) {
        return {
          ...item,
          payment: this.checkMaxAmount(record?.invoice_id)?.remainingAmount > (((item?.invoice_total_amount + item?.invoice_round_off) || ((item?.invoice_grand_total * (item?.conversion_rate || 1)) + item?.invoice_round_off)) - (item?.payment_made || (item?.total_payment_made * (item?.conversion_rate || 1))) - (item?.credits_applied * (item?.conversion_rate || 1)))
            ? (((item?.invoice_total_amount + item?.invoice_round_off) || ((item?.invoice_grand_total * (item?.conversion_rate || 1)) + item?.invoice_round_off)) - (item?.payment_made || (item?.total_payment_made * (item?.conversion_rate || 1))) - (item?.credits_applied * (item?.conversion_rate || 1)))
            : this.checkMaxAmount(record?.invoice_id)?.remainingAmount,
        };
      }
      return {
        ...item,
      };
    });
    this.setState({
      invoiceData: copyInvoiceData,
    });
  }

  /**
   * @param paymentRequest
   */
  handleAddPayment(paymentRequest) {
    this.setState({ formSubmitted: true });
    const {
      createAndLinkPayment, createPaymentsOutgoing, history, user, updatePaymentOutgoingStatus,
    } = this.props;
    const {
      currentSelectedCustomer, amount, utrNumber, paymentDate, invoiceData, remarks, fileList, paymentType, customFields, selectedCustomer, tenantId, selectedPaymentMode,
    } = this.state;
    let paymentMode = '';
    let allPaymentsUnderAmount;
    // check if all GRNs have valid payment amount
    for (let i = 0; i < invoiceData?.length; i++) {
      if (Number(invoiceData[i]?.payment) > Number(((invoiceData[i]?.invoice_total_amount + invoiceData[i]?.invoice_round_off) || ((invoiceData[i]?.invoice_grand_total * (invoiceData[i]?.conversion_rate || 1)) + invoiceData[i]?.invoice_round_off)) - (invoiceData[i]?.payment_made || (invoiceData[i]?.total_payment_made * (invoiceData[i]?.conversion_rate || 1))) - (invoiceData[i]?.credits_applied * (invoiceData[i]?.conversion_rate || 1))) || (Number(invoiceData[i]?.payment) > Number(amount))) {
        allPaymentsUnderAmount = true;
      }
    }
    // if all validations are clear, start payment request creation process
    if (currentSelectedCustomer && amount && selectedPaymentMode && paymentDate && !allPaymentsUnderAmount && !customFields?.filter((customField) => customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length) {
      const invoiceDataLines = invoiceData?.filter((record) => record.payment).map((item) => ({
        invoice_id: item.invoice_id,
        amount: item.payment,
      }));
      // if GRN payments are also there, we will create and link payment
      if (paymentType === 'INVOICE_PAYMENT') {
        // check if the payment is an online payment request
        if (paymentRequest) {
          // get PICE payment mode ID and save it in paymentMode
          for (let i = 0; i < user?.tenant_info?.payment_accounts?.length; i++) {
            const paymentAcc = user?.tenant_info?.payment_accounts?.[i];
            if (paymentAcc?.account_name === 'PICE') {
              paymentMode = paymentAcc?.account_id;
            }
          }

          const payload = {
            payment_details: [
              {
                amount,
                paid_through_account_id: selectedPaymentMode,
                utr_number: utrNumber,
                tenant_id: tenantId,
                payment_date: paymentDate,
                customer_id: currentSelectedCustomer,
                attachments: fileList?.map((attachment) => ({
                  url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
                })) || [],
                remarks,
                approval_status: 'DRAFT',
                payment_status: 'PENDING',
              },
            ],
            invoice_details: invoiceDataLines,
            custom_fields: customFields?.map((customField) => ({
              cf_id: customField?.cfId,
              field_name: customField?.fieldName,
              field_value: customField?.fieldValue,
              field_type: customField?.fieldType,
              is_required: customField.isRequired,
              default_data: customField?.defaultData,
              placeholder: customField?.placeholder,
            })),
          };
          createAndLinkPayment(payload, () => {
            // if payment status is already PAYMENT_SUCCESS, we do not need to send payment for approval
            // now handled by backend
            history.push('/sales/incoming-payments');
          });
        } else {
          // in the case where it is not an online payment, we will set the value of CASH payment account in paymentMode
          for (let i = 0; i < user?.tenant_info?.payment_accounts?.length; i++) {
            const paymentAcc = user?.tenant_info?.payment_accounts?.[i];
            if (paymentAcc?.account_name === 'CASH') {
              paymentMode = paymentAcc?.account_id;
            }
          }
          const payload = {
            payment_details: [
              {
                amount,
                tenant_id: tenantId,
                paid_through_account_id: selectedPaymentMode,
                utr_number: utrNumber,
                payment_date: paymentDate,
                customer_id: currentSelectedCustomer,
                attachments: fileList?.map((attachment) => ({
                  url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
                })) || [],
                remarks,
                approval_status: 'ISSUED',
                payment_status: 'PAYMENT_SUCCESS',

              },
            ],
            invoice_details: invoiceDataLines,
            custom_fields: customFields?.map((customField) => ({
              cf_id: customField?.cfId,
              field_name: customField?.fieldName,
              field_value: customField?.fieldValue,
              field_type: customField?.fieldType,
              is_required: customField.isRequired,
              default_data: customField?.defaultData,
              placeholder: customField?.placeholder,
            })),
          };
          createAndLinkPayment(payload, () => {
            history.push('/sales/incoming-payments');
          });
        }
      } else if (paymentRequest && paymentType !== 'INVOICE_PAYMENT') {
        // if GRN lines are not there and payment request has to be PICE
        for (let i = 0; i < user?.tenant_info?.payment_accounts?.length; i++) {
          const paymentAcc = user?.tenant_info?.payment_accounts?.[i];
          if (paymentAcc?.account_name === 'PICE') {
            paymentMode = paymentAcc?.account_id;
          }
        }
        const payload = [
          {
            amount,
            customer_id: currentSelectedCustomer,
            tenant_id: tenantId,
            paid_through_account_id: selectedPaymentMode,
            utr_number: utrNumber,
            payment_date: paymentDate,
            attachments: fileList?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) || [],
            remarks,
            approval_status: 'ISSUED',
            payment_status: 'PAYMENT_SUCCESS',
            custom_fields: customFields?.map((customField) => ({
              cf_id: customField?.cfId,
              field_name: customField?.fieldName,
              field_value: customField?.fieldValue,
              field_type: customField?.fieldType,
              is_required: customField.isRequired,
              default_data: customField?.defaultData,
              placeholder: customField?.placeholder,
            })),
          },
        ];
        createPaymentsOutgoing(payload, (payment) => {
          this.setState({ selectedPayment: payment?.payments?.[0] });
          history.push('/sales/incoming-payments');
        });
      } else if (!paymentRequest && paymentType !== 'INVOICE_PAYMENT') {
        for (let i = 0; i < user?.tenant_info?.payment_accounts?.length; i++) {
          const paymentAcc = user?.tenant_info?.payment_accounts?.[i];
          if (paymentAcc?.account_name === 'CASH') {
            paymentMode = paymentAcc?.account_id;
          }
        }
        const payload = [
          {
            amount,
            paid_through_account_id: selectedPaymentMode,
            utr_number: utrNumber,
            tenant_id: tenantId,
            payment_date: paymentDate,
            customer_id: currentSelectedCustomer,
            attachments: fileList?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })) || [],
            remarks,
            approval_status: 'ISSUED',
            payment_status: 'PAYMENT_SUCCESS',
            custom_fields: customFields?.map((customField) => ({
              cf_id: customField?.cfId,
              field_name: customField?.fieldName,
              field_value: customField?.fieldValue,
              field_type: customField?.fieldType,
              is_required: customField.isRequired,
              default_data: customField?.defaultData,
              placeholder: customField?.placeholder,
            })),
          },
        ];
        createPaymentsOutgoing(payload, () => {
          history.push('/sales/incoming-payments');
        });
      }
    }
  }

  /**
   * @param invoiceId
   */
  checkMaxAmount(invoiceId) {
    const { invoiceData, amount } = this.state;
    let totalAmount = 0;
    // eslint-disable-next-line no-return-assign
    if (invoiceId) {
      // eslint-disable-next-line no-unused-expressions, no-return-assign
      invoiceData?.filter((invoice) => invoice?.invoice_id !== invoiceId)?.map((item) => totalAmount += (Number(item.payment)));
    } else {
      // eslint-disable-next-line no-unused-expressions, no-return-assign
      invoiceData?.map((item) => totalAmount += (Number(item.payment)));
    }
    return { totalAmount, remainingAmount: amount - totalAmount, paymentExceed: totalAmount < amount };
  }

  /**
   *
   */
  resetAllPayments() {
    const { invoiceData } = this.state;
    const copyInvoiceData = invoiceData?.map((item) => ({
      ...item,
      payment: 0,
    }));
    this.setState({
      invoiceData: copyInvoiceData,
    });
  }

  /**
   * @param value
   * @param fieldValue
   * @param cfId
   */
  customInputChange(fieldValue, cfId) {
    const { customFields } = this.state;
    const newCustomField = customFields.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),

          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      customFields: newCustomField,
    });
  }

  computetotal() {
    const { invoiceData } = this.state;
    const suminvTotal = invoiceData?.reduce((sum, invoice) => sum + (invoice?.invoice_total_amount || (invoice?.invoice_grand_total * (invoice?.conversion_rate || 1))) + invoice.invoice_round_off, 0);
    const sumtotpayment = invoiceData?.reduce((sum, invoice) => sum + (invoice?.payment_made || (invoice.total_payment_made * (invoice?.conversion_rate || 1))) + (invoice?.credits_applied * (invoice?.conversion_rate || 1)), 0);

    return { sumInvoiceGrandTotal: suminvTotal, sumTotalPaymentMade: sumtotpayment };
  }

  /**
   * @param value
   */
  showNewCustomerInSelect = (value) => {
    this.setState({
      tenantId: value?.tenant_id,
      currentSelectedCustomer: value?.customer_id,
      billingAddress: value?.customer_info?.billing_address_details,
      shippingAddress: value?.customer_info?.shipping_address_details,
      gstNumber: value?.customer_info?.gst_number,
      selectedCustomerInfo: value?.customer_info,
      paymentType: '',
      fullAmountSelected: false,
      amount: 0,
    });
  }

  getDataSource() {
    const { invoiceData } = this.state;
    const copyData = JSON.parse(JSON.stringify(invoiceData || []));

    return copyData;
  }

  /**
   *
   * @return {JSX.Element}
   */
  render() {
    const {
      currentSelectedCustomer, formSubmitted, amount, utrNumber, paymentDate, columns,
      remarks, fileList, paymentType, fullAmountSelected,
      tenantId, invoiceData, showAddCustomer, showNewCustomerModal, selectedPaymentMode,
    } = this.state;
    const {
      user, createAndLinkPaymentLoading, getInvoiceLoading,
      getInvoice, createPaymentsOutgoingLoading, updatePaymentOutgoingStatusLoading,
      MONEY, getCustomers, MONEY_SYMBOL, paymentModeResults,
    } = this.props;

    // const oldCSS = () => (
    //   <Fragment>
    //     <div className="create-payin__wrapper">
    //       <Drawer
    //         open={showNewCustomerModal}
    //         onClose={() => this.setState({ showNewCustomerModal: false })}
    //         width="720px"
    //         destroyOnClose
    //       >
    //         <div className="custom-drawer__header-wrapper">
    //           <div className="custom-drawer__header" style={{ width: '680px' }}>
    //             <H3Text
    //               text="Add New Customer"
    //               className="custom-drawer__title"
    //             />
    //             <H3Image
    //               src={cdnUrl("icon-close-blue.png", "icons")}
    //               className="custom-drawer__close-icon"
    //               onClick={() => this.setState({ showNewCustomerModal: false })}
    //             />
    //           </div>
    //         </div>
    //         <CustomerForm
    //           callback={(createdCustomer) => {
    //             getCustomers(
    //               '',
    //               user?.tenant_info?.tenant_id,
    //               1,
    //               1000,
    //               '',
    //               (newCustomers) => {
    //                 this.showNewCustomerInSelect(
    //                   newCustomers?.customers.filter(
    //                     (customer) => customer?.customer_id === createdCustomer.customer_id,
    //                   )[0],
    //                 );
    //                 this.setState({ showNewCustomerModal: false });
    //               },
    //             );
    //           }}
    //         />
    //       </Drawer>
    //       <div className="incoming-payment__wrapper">
    //         <div className="ant-row">
    //           <div
    //             className="ant-col-md-12"
    //             style={{
    //               marginBottom: formSubmitted && !tenantId ? '10px' : '',
    //             }}
    //           >
    //             <div className="create-payment__input-row">
    //               <H3Text text="Select Customer" required className="create-payment__input-row__label" />
    //               <div className="orgInputContainer create-payment__input-row__input" style={{ height: '34px' }}>
    //                 <SelectCustomer
    //                   hideTitle
    //                   showAddCustomer={showAddCustomer}
    //                   selectedCustomer={currentSelectedCustomer}
    //                   addCustomer={() => this.setState({ showNewCustomerModal: true })}
    //                   onChange={(value) => {
    //                     const customerId = value?.customer_id;
    //                     this.setState({
    //                       tenantId: value?.tenant_id,
    //                       currentSelectedCustomer: value?.customer_id,
    //                       billingAddress: value?.customer_info?.billing_address_details,
    //                       shippingAddress: value?.customer_info?.shipping_address_details,
    //                       gstNumber: value?.customer_info?.gst_number,
    //                       selectedCustomerInfo: value?.customer_info,
    //                       paymentType: '',
    //                       fullAmountSelected: false,
    //                       amount: 0,
    //                     });
    //                     getInvoice('', user?.tenant_info?.tenant_id, '', '', '', customerId, '', 'CONFIRMED');
    //                   }}
    //                   containerClass="create-invoice__input-row orgInputContainer"
    //                   inputClassName="create-invoice__input-row__input"
    //                   labelClassName="create-invoice__input-row__label"
    //                   showError={formSubmitted && !tenantId}
    //                   tenantId={user?.tenant_info?.tenant_id}
    //                 // disabled={createInvoiceLoading || updateInvoiceLoading || selectedOrderForInvoice}
    //                 />
    //               </div>
    //             </div>
    //           </div>
    //           <div className="ant-col-md-12" />
    //           <div className="ant-col-md-12">
    //             <div className="create-payment__input-row">
    //               <H3Text text="Payment Type" required className="create-payment__input-row__label" />
    //               <div className="orgInputContainer create-payment__input-row__input">
    //                 <Select
    //                   filterOption={false}
    //                   value={paymentType}
    //                   onChange={(val) => { this.setState({ paymentType: val, fullAmountSelected: false, amount: 0 }); this.computetotal(); }}
    //                   style={{
    //                     width: '100%',
    //                     border: '1px solid rgba(68, 130, 218, 0.2)',
    //                     borderRadius: '2px',
    //                     height: '32px',
    //                   }}
    //                   bordered={false}
    //                   disabled={!currentSelectedCustomer || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
    //                 >
    //                   <Option key="CUSTOMER_ADVANCE" value="CUSTOMER_ADVANCE">
    //                     Customer Advance
    //                   </Option>
    //                   <Option key="INVOICE_PAYMENT" value="INVOICE_PAYMENT">
    //                     Invoice Payment
    //                   </Option>
    //                 </Select>
    //                 {formSubmitted && !paymentType && <div className="input-error">Please select a payment type*</div>}
    //               </div>
    //             </div>
    //           </div>
    //           <div className="ant-col-md-12" />
    //           <div className="ant-col-md-12">
    //             <div className="create-payment__input-row">
    //               <H3Text text="Payment Account" required className="create-payment__input-row__label" />
    //               <div className="orgInputContainer create-payment__input-row__input">
    //                 <Select
    //                   filterOption={false}
    //                   value={selectedPaymentMode}
    //                   onChange={(val) => this.setState({ selectedPaymentMode: val })}
    //                   style={{
    //                     width: '100%',
    //                     border: '1px solid rgba(68, 130, 218, 0.2)',
    //                     borderRadius: '2px',
    //                     height: '32px',
    //                   }}
    //                   bordered={false}
    //                   disabled={!currentSelectedCustomer || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
    //                 >
    //                   {paymentModeResults?.map((item) => (
    //                     <Option key={item?.account_id} value={item?.account_id}>
    //                       {item?.account_name?.split('_')?.join(' ')?.toUpperCase()}
    //                     </Option>
    //                   ))}
    //                 </Select>
    //                 {formSubmitted && !selectedPaymentMode && <div className="input-error">Please select a payment mode*</div>}
    //               </div>
    //             </div>
    //           </div>
    //           <div className="ant-col-md-12" />
    //           <div className="ant-col-md-12">
    //             <div className="create-payment__input-row " style={{ marginBottom: '10px' }}>
    //               <H3Text text="Payment Amount" required className="create-payment__input-row__label" />
    //               <div className="payment-amount__wrapper-outer create-payment__input-row__input">
    //                 <div className="payment-amount__wrapper">
    //                   <H3Text text={MONEY_SYMBOL} className="payment-amount__currency" />
    //                   <H3FormInput
    //                     name="payment amount"
    //                     type="number"
    //                     disabled={!currentSelectedCustomer || fullAmountSelected || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
    //                     containerClassName="payment-amount__wrapper-outer "
    //                     labelClassName="orgFormLabel"
    //                     inputClassName="orgFormInput input"
    //                     placeholder=""
    //                     onChange={(event) => {
    //                       this.resetAllPayments();
    //                       this.setState({ amount: event.target.value });
    //                     }}
    //                     value={amount}
    //                   />
    //                 </div>
    //                 {formSubmitted && !amount && <div className="input-error">Please enter payment amount*</div>}
    //                 {invoiceData && paymentType === 'INVOICE_PAYMENT' && (
    //                   <Checkbox
    //                     onChange={() => {
    //                       const invoiceDataCopy = JSON.parse(JSON.stringify(invoiceData));
    //                       for (let i = 0; i < invoiceDataCopy?.length; i++) {
    //                         invoiceDataCopy[i].payment = (invoiceDataCopy[i]?.invoice_total_amount || (invoiceDataCopy[i]?.invoice_grand_total * (invoiceDataCopy[i]?.conversion_rate || 1))) - (invoiceDataCopy[i]?.payment_made || (invoiceDataCopy[i]?.total_payment_made * (invoiceDataCopy[i]?.conversion_rate || 1))) - (invoiceDataCopy[i]?.credits_applied * (invoiceDataCopy[i]?.conversion_rate || 1)) + invoiceDataCopy[i]?.invoice_round_off;
    //                       }
    //                       this.setState({
    //                         amount: this.computetotal()?.sumInvoiceGrandTotal - this.computetotal()?.sumTotalPaymentMade,
    //                         fullAmountSelected: !fullAmountSelected,
    //                         invoiceData: invoiceDataCopy,
    //                       });
    //                     }}
    //                     disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
    //                     checked={fullAmountSelected}
    //                     className="payment-amount__checkbox"
    //                   >
    //                     Pay total due amount (
    //                     {MONEY((this.computetotal()?.sumInvoiceGrandTotal - this.computetotal()?.sumTotalPaymentMade) || 0)}
    //                     )
    //                   </Checkbox>
    //                 )}
    //               </div>
    //             </div>

    //           </div>
    //           <div className="ant-col-md-12" />
    //           <div className="ant-col-md-12">
    //             <div className="create-payment__input-row">
    //               <H3Text text="Payment Date" required className="create-payment__input-row__label" />
    //               <div className="create-payment__input-row__input">
    //                 <DatePicker
    //                   value={paymentDate}
    //                   onChange={(value) => {
    //                     this.setState({ paymentDate: value });
    //                   }}
    //                   style={{
    //                     border: '1px solid rgba(68, 130, 218, 0.2)',
    //                     borderRadius: '2px',
    //                     height: '32px',
    //                     padding: '1px 3px',
    //                     width: '100%',
    //                     background: 'white',
    //                   }}
    //                   disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}

    //                 />
    //                 {formSubmitted && !paymentDate && <div className="input-error">Please select a payment date*</div>}
    //               </div>
    //             </div>
    //           </div>
    //           <div className="ant-col-md-12" />
    //           <div className="ant-col-md-12">
    //             <div className="create-payment__input-row orgInputContainer" style={{ marginTop: '10px' }}>
    //               <H3Text text="Reference #" className="create-payment__input-row__label" />
    //               <H3FormInput
    //                 name="payment reference number"
    //                 type="text"
    //                 containerClassName=" create-payment__input-row__input"
    //                 labelClassName="orgFormLabel"
    //                 inputClassName="orgFormInput input"
    //                 placeholder=""
    //                 onChange={(event) => this.setState({ utrNumber: event.target.value })}
    //                 value={utrNumber}
    //                 disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
    //               />
    //             </div>
    //           </div>

    //         </div>
    //         {paymentType === 'INVOICE_PAYMENT' && (
    //           <div className="incoming-payment__table">
    //             <Table
    //               size="small"
    //               scroll="scroll"
    //               columns={columns}
    //               loading={createAndLinkPaymentLoading || getInvoiceLoading}
    //               dataSource={this.getDataSource() || []}
    //               pagination={false}
    //             />
    //           </div>
    //         )}
    //         <div className="ant-row">
    //           <div className="ant-col-md-12">
    //             <div className="create-payment__input-row">
    //               <H3Text text="Internal Remark" className="create-payment__input-row__label" />
    //               <textarea
    //                 className="orgFormInput create-payment__input-row__input"
    //                 value={remarks}
    //                 onChange={(e) => this.setState({ remarks: e.target.value })}
    //                 disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
    //               />
    //             </div>
    //             <div className="create-payment__input-row" style={{ marginTop: '10px' }}>
    //               <H3Text text=" Attachment(s)" className="create-payment__input-row__label" />
    //               <div style={{ marginLeft: '0px' }}>
    //                 <Upload
    //                   action={Constants.UPLOAD_FILE}
    //                   listType="picture-card"
    //                   fileList={fileList}
    //                   onPreview={this.handlePreview}
    //                   onChange={(fileListData) => this.setState({ fileList: fileListData.fileList })}
    //                   disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}

    //                 >
    //                   {fileList?.length >= 20 ? null : uploadButton}
    //                 </Upload>
    //               </div>
    //             </div>
    //           </div>
    //           {paymentType === 'INVOICE_PAYMENT' && (
    //             <div className="ant-col-md-12">
    //               <div className="purchase-incoming-payment-totals">
    //                 <div className="purchase-incoming-payment__field">
    //                   <H3Text text="Amount Used" className="purchase-incoming-payment__field-name" />
    //                   <H3Text
    //                     text={MONEY(this.checkMaxAmount()?.totalAmount || '0')}
    //                     className="purchase-incoming-payment__field-value"
    //                   />
    //                 </div>
    //                 <div className="purchase-incoming-payment__field">
    //                   <H3Text text="Amount Remaining" className="purchase-incoming-payment__field-name" />
    //                   <H3Text
    //                     text={MONEY(this.checkMaxAmount()?.remainingAmount || '0')}
    //                     className="purchase-incoming-payment__field-value"
    //                   />
    //                 </div>
    //               </div>
    //             </div>
    //           )}
    //         </div>
    //       </div>
    //       <div className="purchase-incoming-payment__footer">
    //         <Button
    //           type="primary"
    //           onClick={() => {
    //             if (!createAndLinkPaymentLoading) {
    //               this.handleAddPayment(false);
    //             }
    //           }}
    //           isLoading={createAndLinkPaymentLoading || createPaymentsOutgoingLoading || updatePaymentOutgoingStatusLoading}
    //           disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading || updatePaymentOutgoingStatusLoading}
    //         >
    //           Record Payment
    //         </Button>
    //         {/* {tenantsConfiguration?.data[0]?.tenant_pice_users?.length && ( */}
    //         {/*  <H3Button */}
    //         {/*    text="Request Online Payment" */}
    //         {/*    buttonType={defaultButtonTypes.BLUE_ROUNDED} */}
    //         {/*    style={{ */}
    //         {/*      width: '220px', */}
    //         {/*      padding: '7px 0px', */}
    //         {/*      marginLeft: '0', */}
    //         {/*    }} */}
    //         {/*    onClick={() => { */}
    //         {/*      if (!createAndLinkPaymentLoading) { */}
    //         {/*        this.handleAddPayment(true); */}
    //         {/*      } */}
    //         {/*    }} */}
    //         {/*    isLoading={createAndLinkPaymentLoading || createPaymentsOutgoingLoading} */}
    //         {/*    disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading} */}
    //         {/*  /> */}
    //         {/* )} */}

    //       </div>
    //     </div>
    //   </Fragment>
    // );

    return (
      <Fragment>
        <Drawer
          open={showNewCustomerModal}
          onClose={() => this.setState({ showNewCustomerModal: false })}
          width="720px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '680px' }}>
              <H3Text
                text="Add New Customer"
                className="custom-drawer__title"
              />
              <H3Image
                src={cdnUrl("icon-close-blue.png", "icons")}
                className="custom-drawer__close-icon"
                onClick={() => this.setState({ showNewCustomerModal: false })}
              />
            </div>
          </div>
          <CustomerForm
            callback={(createdCustomer) => {
              getCustomers(
                '',
                user?.tenant_info?.tenant_id,
                1,
                1000,
                '',
                (newCustomers) => {
                  this.showNewCustomerInSelect(
                    newCustomers?.customers.filter(
                      (customer) => customer?.customer_id === createdCustomer.customer_id,
                    )[0],
                  );
                  this.setState({ showNewCustomerModal: false });
                },
              );
            }}
          />
        </Drawer>
        <div className="form__wrapper form-component" style={{ paddingTop: '90px' }}>
          <div className="ant-row">
            <div className="ant-col-md-24">
              <div className="form__section">
                <div className='flex-display flex-align-c mg-bottom-5 pd-right-15'>
                  <H3Text text="PART A" className="form__section-title" />
                  <div className="form__section-line" />
                </div>
                <div className='form__section-inputs mg-bottom-20'>
                  <div className="ant-row">
                    <div
                      className="ant-col-md-6"
                    >
                      <div className="form__input-row">
                        <H3Text text="Select Customer" required className="form__input-row__label" />
                        <div className={`orgInputContainer form__input-row__input ${formSubmitted && !currentSelectedCustomer ? 'form__input-row__input-error' : ''}`}>
                          <SelectCustomer
                            hideTitle
                            showAddCustomer={showAddCustomer}
                            selectedCustomer={currentSelectedCustomer}
                            addCustomer={() => this.setState({ showNewCustomerModal: true })}
                            onChange={(value) => {
                              const customerId = value?.customer_id;
                              this.setState({
                                tenantId: value?.tenant_id,
                                currentSelectedCustomer: value?.customer_id,
                                billingAddress: value?.customer_info?.billing_address_details,
                                shippingAddress: value?.customer_info?.shipping_address_details,
                                gstNumber: value?.customer_info?.gst_number,
                                selectedCustomerInfo: value?.customer_info,
                                paymentType: '',
                                fullAmountSelected: false,
                                amount: 0,
                              });
                              getInvoice('', user?.tenant_info?.tenant_id, '', '', '', customerId, '', 'CONFIRMED');
                            }}
                            // containerClass="form__input-row"
                            // inputClassName="form__input-row__input orgInputContainer"
                            // labelClassName="form__input-row__label"
                            showError={formSubmitted && !tenantId}
                            tenantId={user?.tenant_info?.tenant_id}
                          // disabled={createInvoiceLoading || updateInvoiceLoading || selectedOrderForInvoice}
                          />
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <H3Text text="Payment Type" required className="form__input-row__label" />
                        <div className={`orgInputContainer form__input-row__input ${formSubmitted && !paymentType ? 'form__input-row__input-error' : ''}`}>
                          <PRZSelect
                            filterOption={false}
                            value={paymentType}
                            onChange={(val) => { this.setState({ paymentType: val, fullAmountSelected: false, amount: 0 }); this.computetotal(); }}
                            disabled={!currentSelectedCustomer || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
                          >
                            <Option key="CUSTOMER_ADVANCE" value="CUSTOMER_ADVANCE">
                              Customer Advance
                            </Option>
                            <Option key="INVOICE_PAYMENT" value="INVOICE_PAYMENT">
                              Invoice Payment
                            </Option>
                          </PRZSelect>
                          {formSubmitted && !paymentType && <div className="input-error">Please select a payment type*</div>}
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <H3Text text="Payment Account" required className='form__input-row__label' />
                        <div className={`orgInputContainer form__input-row__input ${formSubmitted && !selectedPaymentMode ? 'form__input-row__input-error' : ''}`}>
                          <PRZSelect
                            filterOption={false}
                            value={selectedPaymentMode}
                            onChange={(val) => this.setState({ selectedPaymentMode: val })}
                            disabled={!currentSelectedCustomer || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
                          >
                            {paymentModeResults?.map((item) => (
                              <Option key={item?.account_id} value={item?.account_id}>
                                {item?.account_name?.split('_')?.join(' ')?.toUpperCase()}
                              </Option>
                            ))}
                          </PRZSelect>
                          {formSubmitted && !selectedPaymentMode && <div className="input-error">Please select a payment mode*</div>}
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row " style={{ marginBottom: '10px' }}>
                        <H3Text text="Payment Amount" required className="form__input-row__label" />
                        <div className="payment-amount__wrapper-outer form__input-row__input">
                          <div className="payment-amount__wrapper">
                            <H3Text text={MONEY_SYMBOL} className="payment-amount__currency" />
                            <H3FormInput
                              name="payment amount"
                              type="number"
                              disabled={!currentSelectedCustomer || fullAmountSelected || (createAndLinkPaymentLoading || createPaymentsOutgoingLoading)}
                              containerClassName="payment-amount__wrapper-outer "
                              labelClassName="orgFormLabel"
                              inputClassName={`orgFormInput input ${formSubmitted && !amount ? 'form__input-row__input-error' : ''}`}
                              placeholder=""
                              onChange={(event) => {
                                this.resetAllPayments();
                                this.setState({ amount: event.target.value });
                              }}
                              value={amount}
                            />
                          </div>
                          {formSubmitted && !amount && <div className="input-error">Please enter payment amount*</div>}
                          {invoiceData && paymentType === 'INVOICE_PAYMENT' && (
                            <Checkbox
                              onChange={() => {
                                const invoiceDataCopy = JSON.parse(JSON.stringify(invoiceData));
                                for (let i = 0; i < invoiceDataCopy?.length; i++) {
                                  invoiceDataCopy[i].payment = (invoiceDataCopy[i]?.invoice_total_amount || (invoiceDataCopy[i]?.invoice_grand_total * (invoiceDataCopy[i]?.conversion_rate || 1))) - (invoiceDataCopy[i]?.payment_made || (invoiceDataCopy[i]?.total_payment_made * (invoiceDataCopy[i]?.conversion_rate || 1))) - (invoiceDataCopy[i]?.credits_applied * (invoiceDataCopy[i]?.conversion_rate || 1)) + invoiceDataCopy[i]?.invoice_round_off;
                                }
                                this.setState({
                                  amount: this.computetotal()?.sumInvoiceGrandTotal - this.computetotal()?.sumTotalPaymentMade,
                                  fullAmountSelected: !fullAmountSelected,
                                  invoiceData: invoiceDataCopy,
                                });
                              }}
                              disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                              checked={fullAmountSelected}
                              className="payment-amount__checkbox"
                            >
                              Pay total due amount (
                              {MONEY((this.computetotal()?.sumInvoiceGrandTotal - this.computetotal()?.sumTotalPaymentMade) || 0)}
                              )
                            </Checkbox>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <H3Text text="Payment Date" required className="form__input-row__label" />
                        <div className={`form__input-row__input ${formSubmitted && !paymentDate ? 'form__input-row__input-error' : ''}`}>
                          <DatePicker
                            value={paymentDate}
                            onChange={(value) => {
                              this.setState({ paymentDate: value });
                            }}
                            style={{
                              border: '1px solid rgba(68, 130, 218, 0.2)',
                              borderRadius: '2px',
                              padding: '1px 3px',
                              width: '100%',
                              background: 'white',
                            }}
                            disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}

                          />
                          {formSubmitted && !paymentDate && <div className="input-error">Please select a payment date*</div>}
                        </div>
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form__input-row orgInputContainer">
                        <H3Text text="Reference #" className="form__input-row__label" />
                        <H3FormInput
                          name="payment reference number"
                          type="text"
                          containerClassName=" form__input-row__input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput input"
                          placeholder=""
                          onChange={(event) => this.setState({ utrNumber: event.target.value })}
                          value={utrNumber}
                          disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className='form__section'>
                <div className='flex-display flex-align-c mg-bottom-5 pd-right-15'>
                  <H3Text text="PART B" className="form__section-title" />
                  <div className="form__section-line" />
                </div>
                <div className='form__section-inputs mg-bottom-20'>
                  <div className='ant-row'>
                    <div className="ant-col-md-6">
                      <div className="form__input-row">
                        <H3Text text="Internal Remark" className="form__input-row__label" />
                        <textarea
                          className="orgFormInput form__input-row__input"
                          value={remarks}
                          onChange={(e) => this.setState({ remarks: e.target.value })}
                          disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                        />
                      </div>
                    </div>
                    <div className="ant-col-md-6">
                      <div className="form_input-row">
                        <H3Text text=" Attachment(s)" className="form__input-row__label" />
                        <div style={{ marginLeft: '0px' }}>
                          <Upload
                            action={Constants.UPLOAD_FILE}
                            listType="picture-card"
                            fileList={fileList}
                            onPreview={this.handlePreview}
                            onChange={(fileListData) => this.setState({ fileList: fileListData.fileList })}
                            disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading}
                          >
                            {fileList?.length >= 20 ? null : uploadButton}
                          </Upload>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className='form__lines-wrapper'>
            {paymentType === 'INVOICE_PAYMENT' && (
              <div className="incoming-payment__table">
                <Table
                  size="small"
                  scroll="scroll"
                  columns={columns}
                  loading={createAndLinkPaymentLoading || getInvoiceLoading}
                  dataSource={this.getDataSource() || []}
                  pagination={false}
                />
              </div>
            )}
          </div>
          <div className='form__data-wrapper'>
            <div className='ant-row'>
              <div className="ant-col-md-24">
                <div className='form-section'>
                  <div className='form__section-inputs mg-bottom-20'>
                    <div className="ant-row">
                      <div className="ant-col-md-6" />
                      <div className="ant-col-md-6" />
                      <div className="ant-col-md-6" />
                      {paymentType === 'INVOICE_PAYMENT' && (
                        <div className="ant-col-md-6">
                          <div className="purchase-incoming-payment-totals">
                            <div className="purchase-incoming-payment__field">
                              <H3Text text="Amount Used" className="purchase-incoming-payment__field-name" />
                              <H3Text
                                text={MONEY(this.checkMaxAmount()?.totalAmount || '0')}
                                className="purchase-incoming-payment__field-value"
                              />
                            </div>
                            <div className="purchase-incoming-payment__field">
                              <H3Text text="Amount Remaining" className="purchase-incoming-payment__field-name" />
                              <H3Text
                                text={MONEY(this.checkMaxAmount()?.remainingAmount || '0')}
                                className="purchase-incoming-payment__field-value"
                              />
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="form__footer">
            <H3Button
              text="Record Payment"
              buttonType={defaultButtonTypes.BLUE_ROUNDED}
              style={{
                padding: '7px 10px',
                marginRight: '10px',
                minWidth: '130px',
              }}
              onClick={() => {
                if (!createAndLinkPaymentLoading) {
                  this.handleAddPayment(false);
                }
              }}
              isLoading={createAndLinkPaymentLoading || createPaymentsOutgoingLoading || updatePaymentOutgoingStatusLoading}
              disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading || updatePaymentOutgoingStatusLoading}
            />
            {/* {tenantsConfiguration?.data[0]?.tenant_pice_users?.length && ( */}
            {/*  <H3Button */}
            {/*    text="Request Online Payment" */}
            {/*    buttonType={defaultButtonTypes.BLUE_ROUNDED} */}
            {/*    style={{ */}
            {/*      width: '220px', */}
            {/*      padding: '7px 0px', */}
            {/*      marginLeft: '0', */}
            {/*    }} */}
            {/*    onClick={() => { */}
            {/*      if (!createAndLinkPaymentLoading) { */}
            {/*        this.handleAddPayment(true); */}
            {/*      } */}
            {/*    }} */}
            {/*    isLoading={createAndLinkPaymentLoading || createPaymentsOutgoingLoading} */}
            {/*    disabled={createAndLinkPaymentLoading || createPaymentsOutgoingLoading} */}
            {/*  /> */}
            {/* )} */}
          </div>
        </div>
      </Fragment>
    )
  }
}

const mapStateToProps = ({
  UserReducers, SellerReducers, PaymentOutgoingReducers, InvoiceReducers,
  TenantReducers, WorkflowsReducers, CFV2Reducers, PaymentModeReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  MONEY_SYMBOL: UserReducers.MONEY_SYMBOL,
  sellers: SellerReducers.sellers,
  getInvoiceLoading: InvoiceReducers.getInvoiceLoading,
  invoices: InvoiceReducers.invoices,
  createPaymentsOutgoingLoading: PaymentOutgoingReducers.createPaymentsOutgoingLoading,
  createAndLinkPaymentLoading: PaymentOutgoingReducers.createAndLinkPaymentLoading,
  updatePaymentOutgoingStatusLoading: PaymentOutgoingReducers.updatePaymentOutgoingStatusLoading,
  tenantsConfiguration: TenantReducers.tenantsConfiguration,
  vendorWorkflows: WorkflowsReducers.vendorWorkflows,
  selectedPaymentOutgoing: PaymentOutgoingReducers.selectedPaymentOutgoing,
  environment: UserReducers.environment,
  getDocCFV2Loading: CFV2Reducers.getDocCFV2Loading,
  customFields: CFV2Reducers.customFields,
  paymentModeResults: PaymentModeReducers.paymentModeResults,
});
const mapDispatchToProps = (dispatch) => ({
  getCustomers: (keyword, tenantId, page, limit, customerId, callback) => dispatch(CustomerActions.getCustomers(keyword, tenantId, page, limit, customerId, callback)),
  createAndLinkPayment: (payload, callback) => dispatch(PaymentOutgoingActions.createAndLinkPayment(payload, callback)),
  createPaymentsOutgoing: (payload, callback) => dispatch(PaymentOutgoingActions.createPaymentsOutgoing(payload, callback)),
  createPaymentRequest: (payload, callback) => dispatch(PaymentRequestActions.createPaymentRequest(payload, callback)),
  updatePaymentOutgoingStatus: (payload, callback) => dispatch(PaymentOutgoingActions.updatePaymentOutgoingStatus(payload, callback)),
  getTenantsConfiguration: (tenantId) => dispatch(TenantActions.getTenantsConfiguration(tenantId)),
  getVendorWorkflows: () => dispatch(WorkflowActions.getVendorWorkflows()),
  getPaymentOutgoingById: (tenantId, paymentId, callback) => dispatch(PaymentOutgoingActions.getPaymentOutgoingById(tenantId, paymentId, callback)),
  createPaymentSession: (payload, callback) => dispatch(PayoutActions.createPaymentSession(payload, callback)),
  getInvoice: (orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status) => dispatch(InvoiceActions.getInvoice(orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status)),
  getInvoiceSuccess: (invoices) => dispatch(InvoiceActions.getInvoiceSuccess(invoices)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getDocCFV2Success: (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getPaymentMode: (accountType, callback) => dispatch(PaymentModeActions.getPaymentMode(accountType, callback)),
});

CreateIncomingPayment.propTypes = {
  getCustomers: PropTypes.func,
  createAndLinkPaymentLoading: PropTypes.bool,
  sellers: PropTypes.any,
  user: PropTypes.any,
  invoiceData: PropTypes.any,
  getInvoiceLoading: PropTypes.bool,
  getInvoice: PropTypes.func,
  createAndLinkPayment: PropTypes.func,
  history: PropTypes.func,
  getInvoiceSuccess: PropTypes.func,
  createPaymentsOutgoing: PropTypes.func,
  createPaymentsOutgoingLoading: PropTypes.bool,
  updatePaymentOutgoingStatus: PropTypes.func,
  updatePaymentOutgoingStatusLoading: PropTypes.bool,
  getTenantsConfiguration: PropTypes.func,
  tenantsConfiguration: PropTypes.any,
  getVendorWorkflows: PropTypes.func,
  getDocCFV2Loading: PropTypes.bool,
  customFields: PropTypes.any,
  getDocCFV2: PropTypes.any,

};

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CreateIncomingPayment));
