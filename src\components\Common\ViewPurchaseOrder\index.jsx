// Core Libraries
import React, { useEffect, useReducer } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';

// UI Libraries
import { Modal, DatePicker } from 'antd';
import dayjs from 'dayjs';
import 'react-quill/dist/quill.bubble.css';

// Utility Functions
import { toISTDate } from '@Apis/constants';

// Custom Components
import SendPoMail from '@Components/Common/ViewPurchaseOrder/SendPoMail';
import SendWhatsapp from '@Components/Common/ViewPurchaseOrder/SendWhatsapp';

// Helpers
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import { updatePOExpiryDate, handleBlanketPoFields, MenuItems, getLinkedDocumentInfo, HideAdhocApprovals } from './helper';

// Redux Actions
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import GrnActions from '@Actions/grnActions';
import inventoryLocationActions from '@Actions/settings/inventoryLocationActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import ActivityLogActions from '@Actions/activityLogActions';
import ZohoIntegrationActions from '@Actions/integrations/zohoIntegrationActions';
import AttachmentActions from '@Actions/attachmentActions';

// Modules
import { CreateSoFromPo } from '../../../modules/createSoFromPo';

// current file imports
import ViewLoadingSkull from '../ViewLoadingSkull';
import ViewPayment from '../ViewPayment';
import GRNForm from '../../Purchase/GoodsReceiving/GRNForm';
import ViewGRN from '@Components/Purchase/GoodsReceiving/GoodsReceivingHome/ViewGRN';
import SplitStatus from './SplitStatus';
import POLines from './POLines';
import { SplitStatusPo } from '../../../modules/purchase/purchaseOrder';
import ViewDocRightSection from '../ViewDocRightSection';
import { initialState, viewPurchaseOrderReducer as reducer } from './reducer';
import ViewPurchaseOrderLeftSectionHeader from './ViewPurchaseOrderHeader';
import ViewPurchaseOrderDetails from './ViewPurchaseOrderDetails';
import ViewPurchaseOrderAddresses from './ViewPurchaseOrderAddresses';
import ViewPurchaseOrderFooter from './ViewPurchaseOrderFooter';
import PRZDrawer from '../UI/PRZDrawer';
import GRNFormV2 from '@Components/Purchase/GoodsReceiving/GRNForm/index.refactored';
import './style.scss';

function ViewPurchaseOrder({ updatePoStatusLoading, updatePoWorkflowStepLoading, user, updatePoWorkflowStep, grnData, history, splitStatusPo, SplitStatusPoLoading, getPurchaseOrderByIdLoading, getPurchaseOrderById, isUserReadyFromPendingScreen, selectedPurchaseOrder, handleClose, updatePoStatus, getGRN, deletePurchaseOrderLoading, deletePurchaseOrder, getAttachmentByIdLoading, updateAttachmentLoading, syncPO, syncPOLoading, createSoFromPo, createSoFromPoLoading, updatePurchaseOrderExpiryDateLoading, closePoLoading, closePo, isQuickView, moCallback, MONEY, downloadDocument, fromMOScreen, priceMasking, inventoryLocations, getInventoryLocations, getPurchaseOrderByIdSuccess, updatePurchaseOrderExpiryDate, updateAttachment, getAttachmentById, selectedAttachment, getActivityLogSuccess,
}) {
  const [state, dispatch] = useReducer(reducer, initialState);

  const { isUserReady, currentUpdate, isGetAttachment, formSubmitted, poExpiryDate, selectedPayment, showViewPayout, visibleColumns, selectedRowKeys, selectedRows, fileList, selectedGRN, openGRNModalView, showEmailDrawer, showWhatsappDrawer, openDateModal, showNewGrnForm, showSplitStatus, selectedTags, cfPurchaseOrdersLine, isUserReadyForPrefillData
  } = state;

  useEffect(() => {
    getActivityLogSuccess(null);
    return () => {
      getPurchaseOrderByIdSuccess(null);
    };
  }, []);

  useEffect(() => {
    if (
      user?.tenant_info?.tenant_id &&
      window.location.href.includes('/approval') &&
      window.location.search.includes('id=') &&
      !isUserReady
    ) {
      const poId = window.location.search.split('id=')[1];
      const tenants = Helpers.getTenantEntityPermission(
        user.user_tenants,
        Helpers.permissionEntities.PURCHASE_ORDER,
        Helpers.permissionTypes.READ
      ).join(',');

      getPurchaseOrderById(poId, tenants);

      const grnPermission = Helpers.getTenantEntityPermission(
        user.user_tenants,
        Helpers.permissionEntities.GOOD_RECEIVING,
        Helpers.permissionTypes.READ
      ).join(',');

      getGRN(grnPermission,null,poId,'PURCHASE_ORDER','DRAFT,ISSUED,REJECTED,VOID,PENDING_FOR_QC,SENT_FOR_APPROVAL',30,1,null);
      dispatch({ type: 'SET_USER_READY', payload: true });
    }

    if (selectedPurchaseOrder && !isUserReadyForPrefillData && (isUserReady || isUserReadyFromPendingScreen)) {
      getInventoryLocations(selectedPurchaseOrder?.tenant_department_id, '', null, true, null, null);

      const lineCf =
        selectedPurchaseOrder?.purchase_order_lines[0]?.po_line_custom_fields;
      dispatch({
        type: 'SET_ATTACHMENT_LIST',
        payload: selectedPurchaseOrder?.attachments,
      });
      dispatch({
        type: 'SET_SELECTED_TAGS',
        payload:
          selectedPurchaseOrder?.tags?.length > 0
            ? selectedPurchaseOrder?.tags
            : [],
      });
      dispatch({ type: 'SET_CF_PURCHASE_ORDERS_LINE', payload: lineCf });
      dispatch({
        type: 'SET_VISIBLE_COLUMNS',
        payload: CustomFieldHelpers.updateVisibleColumns(
          lineCf,
          visibleColumns
        ),
      });
      dispatch({ type: 'SET_USER_READY_FOR_PREFILL_DATA', payload: true });
    }

    if (selectedAttachment &&isGetAttachment &&!getAttachmentByIdLoading &&!updateAttachmentLoading) {
      dispatch({ type: 'SET_ATTACHMENT_LIST', payload: selectedAttachment });
      dispatch({ type: 'SET_IS_GET_ATTACHMENT', payload: false });
    }
  }, [user, selectedPurchaseOrder, selectedAttachment, isGetAttachment, getAttachmentByIdLoading, updateAttachmentLoading, isUserReady, isUserReadyForPrefillData]);

  const now = dayjs().endOf('day');

  const { isDataMaskingPolicyEnable, isHideCostPrice } = priceMasking;

  const fixedMenuBar = user?.side_menu_bar_type === 'FIXED';

  const isVendorOverseas =
    (selectedPurchaseOrder?.seller_info?.seller_type === 'OVERSEAS' ||
      selectedPurchaseOrder?.seller_type === 'OVERSEAS') &&
    user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings
      ?.hide_tax_for_overseas;

  return (
    <div
      className={`view-document__wrapper ${
        !isQuickView ? 'view-document__wrapper-page' : ''
      }`}
    >
      <Modal
        title="Warning"
        style={{
          top: 30,
        }}
        open={openDateModal}
        onOk={() =>
          updatePOExpiryDate({
            updatePurchaseOrderExpiryDate,
            getPurchaseOrderById,
            user,
            poExpiryDate: poExpiryDate,
            dispatch,
          })
        }
        onCancel={() => {
          dispatch({ type: 'TOGGLE_DATE_MODAL', payload: false });
          dispatch({ type: 'SET_PO_EXPIRY_DATE', payload: '' });
          dispatch({ type: 'SET_FORM_SUBMITTED', payload: false });
        }}
      >
        <p>
          This purchase order got expired on&nbsp;
          <span style={{ color: 'red', marginLeft: '2px' }}>
            {toISTDate(selectedPurchaseOrder?.po_expiry_date).format(
              'MMM DD, YYYY'
            )}
          </span>
        </p>
        <p>
          You cannot make changes to the purchase order without extending the
          expiry date.
        </p>
        <p>Please select a new expiry date to proceed.</p>
        <div>
          <DatePicker
            value={poExpiryDate}
            disabled={updatePurchaseOrderExpiryDateLoading}
            disabledDate={(current) =>
              dayjs().add(-1, 'days') >= current ||
              dayjs().add(1, 'month') <= current
            }
            onChange={(value) => {
              dispatch({ type: 'SET_PO_EXPIRY_DATE', payload: value });
            }}
            style={{
              border: '1px solid rgba(68, 130, 218, 0.2)',
              borderRadius: '2px',
              height: '32px',
              padding: '1px 5px',
              width: '100%',
              background: 'white',
              marginBottom:
                formSubmitted && !poExpiryDate ? '0px' : '10px',
            }}
          />
          {formSubmitted && !poExpiryDate && (
            <div
              className="input-error"
              style={{
                marginBottom:
                  formSubmitted && !poExpiryDate ? '10px' : '0px',
              }}
            >
              *Please select a valid PO date
            </div>
          )}
        </div>
      </Modal>
      {!getPurchaseOrderByIdLoading ? (
        <div className="ant-row">
          <div
            className={`${
              fixedMenuBar ? 'ant-col-md-16' : 'ant-col-md-17'
            } ant-col-xs-24`}
          >
            <div
              className={`view-left__wrapper ${
                !isQuickView ? 'is-page-view' : ''
              }`}
            >
              <ViewPurchaseOrderLeftSectionHeader
                user={user}
                MONEY={MONEY}
                selectedPurchaseOrder={selectedPurchaseOrder}
                isQuickView={isQuickView}
                grnData={grnData}
                dispatch={dispatch}
                menu={MenuItems({ selectedPurchaseOrder, downloadDocument })}
                updatePoStatus={updatePoStatus}
                moCallback={moCallback}
                history={history}
                getPurchaseOrderById={getPurchaseOrderById}
                updatePoStatusLoading={updatePoStatusLoading}
                currentUpdate={currentUpdate}
                fromMOScreen={fromMOScreen}
                closePo={closePo}
                closePoLoading={closePoLoading}
                syncPO={syncPO}
                syncPOLoading={syncPOLoading}
                getGRN={getGRN}
                createSoFromPo={createSoFromPo}
                createSoFromPoLoading={createSoFromPoLoading}
                deletePurchaseOrder={deletePurchaseOrder}
                deletePurchaseOrderLoading={deletePurchaseOrderLoading}
                now={now}
                inventoryLocations={inventoryLocations}
              />
              <ViewPurchaseOrderDetails
                selectedPurchaseOrder={selectedPurchaseOrder}
                user={user}
              />
              <ViewPurchaseOrderAddresses
                selectedPurchaseOrder={selectedPurchaseOrder}
                user={user}
              />
              <br />
              <POLines
                poLines={
                  selectedPurchaseOrder?.purchase_order_lines?.map((item) => ({
                    ...item,
                    lineCustomFields: item?.po_line_custom_fields,
                  })) || []
                }
                selectedPurchaseOrder={selectedPurchaseOrder}
                cfPurchaseOrdersLine={cfPurchaseOrdersLine}
                visibleColumns={handleBlanketPoFields({
                  value: visibleColumns,
                  selectedPurchaseOrder,
                })}
                setVisibleColumns={(data) =>
                  dispatch({ type: 'SET_VISIBLE_COLUMNS', payload: data })
                }
                selectedRowKeys={selectedRowKeys}
                selectedRows={selectedRows}
                handleSelectedRows={(keys, data) => {
                  dispatch({
                    type: 'SET_SELECTED_ROWS',
                    payload: { rows: data, keys },
                  });
                }}
                showSplitStatus={showSplitStatus}
                setShowSplitStatus={(val) =>
                  dispatch({ type: 'TOGGLE_SPLIT_STATUS', payload: val })
                }
                sellerInfo={selectedPurchaseOrder?.seller_info}
              />
              <ViewPurchaseOrderFooter
                selectedPurchaseOrder={selectedPurchaseOrder}
                user={user}
                MONEY={MONEY}
                isDataMaskingPolicyEnable={isDataMaskingPolicyEnable}
                isHideCostPrice={isHideCostPrice}
                getAttachmentByIdLoading={getAttachmentByIdLoading}
                updateAttachmentLoading={updateAttachmentLoading}
                updateAttachment={updateAttachment}
                getAttachmentById={getAttachmentById}
                dispatch={dispatch}
                isVendorOverseas={isVendorOverseas}
                fileList={fileList}
              />
            </div>
          </div>
          <ViewDocRightSection
            MONEY={MONEY}
            user={user}
            isQuickView={isQuickView}
            entityType="PURCHASE_ORDER"
            entityId={selectedPurchaseOrder?.po_id}
            tagSelectorProps={{
              isEnabled: true,
              selectedTags: selectedTags,
              isMultipleTagsAllowed: true,
              tagSearchAllowed: true,
              onChange: (value) =>
                dispatch({ type: 'SET_SELECTED_TAGS', payload: value }),
              placeholder: 'Select Tags',
            }}
            workflowTimelineProps={{
              isEnabled:
                selectedPurchaseOrder?.status?.toLowerCase() !== 'draft',
              workflowSteps: selectedPurchaseOrder?.workflow_steps || {},
              workflowStepId: selectedPurchaseOrder?.workflow_step_id,
              loading: updatePoWorkflowStepLoading,
              updateStep: updatePoWorkflowStep,
              hideCase: HideAdhocApprovals(selectedPurchaseOrder),
              callback: () => {
                if (window.location.href.includes('/approval')) {
                  getPurchaseOrderById(
                    window.location.search?.split('id=')[1],
                    Helpers.getTenantEntityPermission(
                      user?.user_tenants,
                      Helpers.permissionEntities.PURCHASE_ORDER,
                      Helpers.permissionTypes.READ
                    ).join(',')
                  );
                } else {
                  handleClose(selectedPurchaseOrder?.workflow_steps);
                }
              },
            }}
            activityLogProps={{
              isEnabled: selectedPurchaseOrder,
              entityName: 'Purchase Order',
              entityType: 'purchase_order',
            }}
            linkedDocumentsInfo={getLinkedDocumentInfo({
              selectedPurchaseOrder,
              grnData,
              user,
              isQuickView,
              openGRNModalView: openGRNModalView,
              isDataMaskingPolicyEnable,
              isHideCostPrice,
              showViewPayout,
              dispatch,
            })}
          />
        </div>
      ) : (
        <ViewLoadingSkull
          isQuickView={isQuickView}
          fixedMenuBar={fixedMenuBar}
        />
      )}

      <PRZDrawer
        onClose={() =>
          dispatch({ type: 'TOGGLE_NEW_GRN_FORM', payload: false })
        }
        open={showNewGrnForm}
        width={1180}
        headerWidth={1130}
        title="Create Goods Receiving Note"
      >
        {/* <GRNForm
          selectedPoForGrn={{
            ...selectedPurchaseOrder,
            tags: selectedTags,
            purchase_order_lines: !user?.tenant_info?.purchase_config
              ?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po
              ? selectedPurchaseOrder?.purchase_order_lines?.filter(
                (line) => line?.quantity > line?.total_received_qty
              )
              : selectedPurchaseOrder?.purchase_order_lines,
          }}
          callback={() => {
            dispatch({ type: 'TOGGLE_NEW_GRN_FORM', payload: false });
            getPurchaseOrderById(
              window.location.search?.split('id=')[1],
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.PURCHASE_ORDER,
                Helpers.permissionTypes.READ
              ).join(',')
            );
            getGRN(
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.GOOD_RECEIVING,
                Helpers.permissionTypes.READ
              ).join(','),
              null,
              selectedPurchaseOrder?.po_id ||
                window.location.search?.split('id=')[1],
              'PURCHASE_ORDER',
              'DRAFT,ISSUED,REJECTED,VOID,PENDING_FOR_QC,SENT_FOR_APPROVAL',
              30,
              1,
              null
            );
          }}
          changeRoute
        /> */}
        <GRNFormV2
          selectedPoForGrn={{
            ...selectedPurchaseOrder,
            tags: selectedTags,
            purchase_order_lines: !user?.tenant_info?.purchase_config
              ?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po
              ? selectedPurchaseOrder?.purchase_order_lines?.filter(
                (line) => line?.quantity > line?.total_received_qty
              )
              : selectedPurchaseOrder?.purchase_order_lines,
          }}
          callback={() => {
            dispatch({ type: 'TOGGLE_NEW_GRN_FORM', payload: false });
            getPurchaseOrderById(
              window.location.search?.split('id=')[1],
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.PURCHASE_ORDER,
                Helpers.permissionTypes.READ
              ).join(',')
            );
            getGRN(
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.GOOD_RECEIVING,
                Helpers.permissionTypes.READ
              ).join(','),
              null,
              selectedPurchaseOrder?.po_id ||
                window.location.search?.split('id=')[1],
              'PURCHASE_ORDER',
              'DRAFT,ISSUED,REJECTED,VOID,PENDING_FOR_QC,SENT_FOR_APPROVAL',
              30,
              1,
              null
            );
          }}
        />
      </PRZDrawer>

      <PRZDrawer
        onClose={() => {
          getGRN(
            Helpers.getTenantEntityPermission(
              user?.user_tenants,
              Helpers.permissionEntities.GOOD_RECEIVING,
              Helpers.permissionTypes.READ
            ).join(','),
            null,
            selectedPurchaseOrder?.po_id ||
              window.location.search?.split('id=')[1],
            'PURCHASE_ORDER',
            'DRAFT,ISSUED,REJECTED,VOID,PENDING_FOR_QC,SENT_FOR_APPROVAL',
            30,
            1,
            null
          );
          dispatch({ type: 'TOGGLE_GRN_MODAL', payload: false });
        }}
        open={openGRNModalView}
        width={1150}
        headerWidth={1100}
        title="View Goods Receiving Note"
      >
        <ViewGRN
          selectedGrnId={selectedGRN?.documentId}
          showGrnAndLabelDropDown
          callback={() => {
            dispatch({ type: 'TOGGLE_GRN_MODAL', payload: false });
            getPurchaseOrderById(
              window.location.search?.split('id=')[1],
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.PURCHASE_ORDER,
                Helpers.permissionTypes.READ
              ).join(',')
            );
            getGRN(
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.GOOD_RECEIVING,
                Helpers.permissionTypes.READ
              ).join(','),
              null,
              selectedPurchaseOrder?.po_id ||
                window.location.search?.split('id=')[1],
              'PURCHASE_ORDER',
              'DRAFT,ISSUED,REJECTED,VOID,PENDING_FOR_QC,SENT_FOR_APPROVAL',
              30,
              1,
              null
            );
          }}
          isQuickView
        />
      </PRZDrawer>
      <PRZDrawer
        open={showEmailDrawer}
        width="420px"
        headerWidth="375px"
        mask
        onClose={() =>
          dispatch({ type: 'TOGGLE_EMAIL_DRAWER', payload: false })
        }
        title="Send Purchase Order to Vendor"
      >
        <SendPoMail
          callback={() =>
            dispatch({ type: 'TOGGLE_EMAIL_DRAWER', payload: false })
          }
        />
      </PRZDrawer>
      <PRZDrawer
        open={showWhatsappDrawer}
        width="360px"
        mask
        onClose={() =>
          dispatch({ type: 'TOGGLE_WHATSAPP_DRAWER', payload: false })
        }
        headerWidth="310px"
        title="Share on Whatsapp"
      >
        <SendWhatsapp
          selectedEntity={selectedPurchaseOrder}
          entityName="purchase_order"
          callback={() =>
            dispatch({ type: 'TOGGLE_WHATSAPP_DRAWER', payload: false })
          }
        />
      </PRZDrawer>
      <PRZDrawer
        open={showViewPayout}
        onClose={() => dispatch({ type: 'TOGGLE_VIEW_PAYOUT', payload: false })}
        width="1150px"
        headerWidth="1100px"
        title="View Payment"
      >
        <ViewPayment
          selectedPayment={selectedPayment}
          selectedPaymentId={selectedPayment?.payment_id}
          callback={() => {
            dispatch({ type: 'TOGGLE_VIEW_PAYOUT', payload: false });
            getPurchaseOrderById(window.location.search?.split('id=')[1],Helpers.getTenantEntityPermission(  user?.user_tenants,  Helpers.permissionEntities.PURCHASE_ORDER,  Helpers.permissionTypes.READ).join(','));
          }}
        />
      </PRZDrawer>
      <PRZDrawer
        open={showSplitStatus}
        onClose={() =>
          dispatch({ type: 'TOGGLE_SPLIT_STATUS', payload: false })
        }
        width="750px"
        headerWidth="700px"
        title="View Split Status"
      >
        <SplitStatus
          selectedRows={selectedRows}
          callback={() => {
            dispatch({
              type: 'SET_SELECTED_ROWS',
              payload: { rows: [], keys: [] },
            });
            dispatch({ type: 'TOGGLE_SPLIT_STATUS', payload: false });
            getPurchaseOrderById(
              window.location.search?.split('id=')[1],
              Helpers.getTenantEntityPermission(
                user?.user_tenants,
                Helpers.permissionEntities.PURCHASE_ORDER,
                Helpers.permissionTypes.READ
              ).join(',')
            );
          }}
          splitStatusPo={splitStatusPo}
          SplitStatusPoLoading={SplitStatusPoLoading}
        />
      </PRZDrawer>
    </div>
  );
}

const mapStateToProps = ({UserReducers, PurchaseOrderReducers, PayoutReducers, GRNReducers, AttachmentReducers, ZohoIntegrationReducers, InventoryLocationReducers, CreateSoFromPo, SplitStatusPo}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  approveApprovePOLoading: PurchaseOrderReducers.approveApprovePOLoading,
  approveRejectPOLoading: PurchaseOrderReducers.approveRejectPOLoading,
  getPurchaseOrderByIdLoading: PurchaseOrderReducers.getPurchaseOrderByIdLoading,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  getPoPaymentLoading: PayoutReducers.getPoPaymentLoading,
  updatePurchaseOrderExpiryDateLoading: PurchaseOrderReducers.updatePurchaseOrderExpiryDateLoading,
  updatePoStatusLoading: PurchaseOrderReducers.updatePoStatusLoading,
  updatePoWorkflowStepLoading: PurchaseOrderReducers.updatePoWorkflowStepLoading,
  getGRNLoading: GRNReducers.getGRNLoading,
  grnData: GRNReducers.grnData,
  deletePurchaseOrderLoading: PurchaseOrderReducers.deletePurchaseOrderLoading,
  selectedAttachment: AttachmentReducers.selectedAttachment,
  getAttachmentByIdLoading: AttachmentReducers.getAttachmentByIdLoading,
  updateAttachmentLoading: AttachmentReducers.updateAttachmentLoading,
  syncPOLoading: ZohoIntegrationReducers.syncPOLoading,
  closePoLoading: PurchaseOrderReducers.closePoLoading,
  priceMasking: UserReducers.priceMasking,
  inventoryLocations: InventoryLocationReducers.inventoryLocations,
  createSoFromPoLoading: CreateSoFromPo.loading,
  SplitStatusPoLoading: SplitStatusPo.loading,
});

const mapDispatchToProps = (dispatch) => ({
  updatePurchaseOrderExpiryDate: (payload, callback) =>
    dispatch(PurchaseOrderActions.updatePurchaseOrderExpiryDate(payload, callback)),
  updatePoStatus: (payload, callback) =>
    dispatch(PurchaseOrderActions.updatePoStatus(payload, callback)),
  deletePurchaseOrder: (payload, callback) =>
    dispatch(PurchaseOrderActions.deletePurchaseOrder(payload, callback)),
  updatePoWorkflowStep: (payload, callback) =>
    dispatch(PurchaseOrderActions.updatePoWorkflowStep(payload, callback)),
  getPurchaseOrderById: (poId, tenantId) =>
    dispatch(PurchaseOrderActions.getPurchaseOrderById(poId, tenantId)),
  getPurchaseOrderByIdSuccess: (selectedPurchaseOrder) =>
    dispatch(PurchaseOrderActions.getPurchaseOrderByIdSuccess(selectedPurchaseOrder)),
  getGRN: ( tenantId, grnId, grnEntityId, grnEntityType, status, limit, page, tenantSellerId) =>
    dispatch(GrnActions.getGRN(tenantId,grnId,grnEntityId,grnEntityType,status,limit,page,tenantSellerId)),
  getAttachmentById: (entityId, entityName, callback) =>
    dispatch(AttachmentActions.getAttachmentById(entityId, entityName, callback)),
  updateAttachment: (payload, callback) =>
    dispatch(AttachmentActions.updateAttachment(payload, callback)),
  syncPO: (payload, callback) =>
    dispatch(ZohoIntegrationActions.syncPO(payload, callback)),
  closePo: (payload, callback) =>
    dispatch(PurchaseOrderActions.closePo(payload, callback)),
  getActivityLogSuccess: (activityLog) =>
    dispatch(ActivityLogActions.getActivityLogSuccess(activityLog)),
  downloadDocument: (payload, document) =>
    dispatch(AnalyticsActions.downloadDocument(payload, document)),
  getInventoryLocations: (tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable, tenantId, departmentId, callback) =>
    dispatch(inventoryLocationActions.getInventoryLocations( tenantDepartmentId, inventoryLocationPath, parentLocationId, isStorable, tenantId, departmentId, callback)),
  createSoFromPo: (payload, callback) =>
    dispatch(CreateSoFromPo.actions.request(payload, callback)),
  splitStatusPo: (payload, callback) =>
    dispatch(SplitStatusPo.actions.request(payload, callback)),
});

export default connect(mapStateToProps,mapDispatchToProps)(withRouter(ViewPurchaseOrder));
