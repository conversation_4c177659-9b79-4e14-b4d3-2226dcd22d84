// 📦 React & Hooks
import React, { Component, Fragment, useEffect, useState } from 'react';
import ReactQuill from 'react-quill';

// 🚦 Routing
import { Link, withRouter } from 'react-router-dom';

// 🧩 Redux
import { connect } from 'react-redux';

// 🧾 Redux Actions
import SoActions from '@Actions/sales/soActions';
import AttachmentActions from '@Actions/attachmentActions';
import UnicommerceIntegrationActions from '@Actions/integrations/unicommerceIntegrationActions';
import ShopifyIntegrationActions from '@Actions/integrations/shopifyIntegrationActions';
import AnalyticsActions from '@Actions/application/analyticsActions';
import ActivityLogActions from '@Actions/activityLogActions';

// 📅 Utilities
import dayjs from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import { QUANTITY, toISTDate } from '@Apis/constants';
import documentUINamesConstant from '@Apis/documentUINamesConstant';

// 🧠 Helpers & Constants
import Constants from '@Apis/constants';
import Helpers from '@Apis/helpers';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import { handleFileChangeHelper, handleDrawerStateHelper, INITIAL_STATE, tenantIdProductMapping, renderFreightHelper, HideAdhocApprovalsHelper, splitChargesData, renderChargesHelper, getTotalQuantity, menuHelper, addAlpha, getShopifyStatusProps } from '../helper';

// 🎨 UI Components - H3 UI Kit
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';
import H3FormInput from '@Uilib/h3FormInput';

// 🎛️ Common Components
import TagSelector from '@Components/Common/Selector/TagSelector';
import SendWhatsapp from '@Components/Common/ViewPurchaseOrder/SendWhatsapp';
import ViewCustomFields from '@Components/Common/CustomField/ViewCustomFields';
import Attachment from '@Components/Common/Attachment';
import ActivityLog from '@Components/Common/ActivityLog';
import SelectCustomStatus from '@Components/Common/SelectCustomStatus';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import SelectAppUser from '@Components/Common/SelectAppUser';
import HideComponent from '@Components/Common/RestrictedAccess/HideComponent';
import ViewLoadingSkull from '@Components/Common/ViewLoadingSkull';
import ViewDocumentFooterDetails from '@Components/Common/ViewDocFooterLeftSection';

// 🧰 Ant Design Components
import { Button, Checkbox, DatePicker, Drawer, Dropdown, Menu, notification, Popconfirm, Popover, Tabs, Tooltip } from 'antd';

// 🎨 Icons
import { AimOutlined, CaretRightOutlined, CheckOutlined, CopyOutlined, EditFilled, EditOutlined, LinkOutlined, LoadingOutlined, UserOutlined, WhatsAppOutlined } from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCaretDown, faCircleInfo, faLock, faPrint, faUnlock } from '@fortawesome/free-solid-svg-icons';

// 🖼️ Static Assets
import { cdnUrl } from '@Utils/cdnHelper';

function ViewSOFooter({
  props, state, updateState
}) {

  const {
    getSOByIdSuccess, user, getSOById, match, isQuickView, selectedSoId, selectedTenantId, selectedSO, selectedAttachment, getAttachmentByIdLoading, updateAttachmentLoading, updateAttachment, getAttachmentById, MONEY, updateSOStatusLoading, updateSOWorkflowStepLoading, updateSOWorkflowStep, history, getSOByIdLoading, updateSOStatus, syncSO, deleteSOLoading, deleteSO, syncSOLoading, syncShopifySO, syncShopifySOLoading, cancelShopifyOrder, cancelShopifyOrderLoading, getReturns, downloadDocument, unLinkMOFromSo, unLinkMOFromSOLoading, quickUpdateSO, getActivityLog, priceMasking,
  } = props;

  const {
    isUserReady, isUserReadyForPrefillData, isGetAttachment, showNewInvoiceForm, showNewPackingSlipForm, isShowDrawerForChangeLocation, selectedReturn, currentUpdate, updatedGstNumber, isGstNumberEditable, cancellationReason, soMapping, fulfillmentId, notifyCustomer, isShowDrawerForReturn, selectedAddressType, showAddressDrawer, isShowDrawerForRefund, currentTenantId, currentOrderId, fileList, showWhatsappDrawer, isPaymentTermsEditable, updateAccountManager, shopifyLocationId, isShowDrawerForCreateFulfillment, selectedFulfillment, currentTab, updatedPaymentTerms, updateDeliveryDate, updateEstimateExpiryDate, productSkuTenantIdMapping, tenantId, addShopifyADHOC, showConvertToMo, selectedTags, showConvertToPr, showDCDrawer, visibleColumns, cfSalesOrderLine, isPaymentRemark, updatePaymentRemark, showReservationDrawer, showUpdateReservationDrawer, selectedOrderLine, showNewSalesForm, salesOrderType, documentUINames, showConsumptionDrawer, isEstimate
  } = state;

  const handleFileChange = (fileListData) => (
    handleFileChangeHelper({ fileListData, props, state, updateState })
  );

  const renderFreight = (position) => (
    renderFreightHelper({ position, props, state, updateState })
  );

  const renderCharges = (charge) => (
    renderChargesHelper({ charge, props, state, updateState })
  );

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  return (
    <div className="view-document__totals-wrapper">
      {/* <div className="view__document-footer-left">
        {selectedSO?.shopify_so_object?.note && (
          <div className="view__document-tc">
            <H3Text text="Order Notes" className="view__document-tc-title" />
            <div className="view__document-tc-body">
              {selectedSO?.shopify_so_object ? selectedSO?.shopify_so_object?.note : ''}
            </div>
          </div>
        )}
        {selectedSO?.terms_and_conditions && (
          <div className="view__document-tc">
            <H3Text text="Terms and Conditions" className="view__document-tc-title" />
            <div className="view__document-tc-body">
              <ReactQuill
                value={selectedSO?.terms_and_conditions ? selectedSO?.terms_and_conditions : ''}
                readOnly
                theme="bubble"
              />
            </div>
          </div>
        )}

        <Attachment
          fileList={fileList}
          disableCase={getAttachmentByIdLoading || updateAttachmentLoading}
          labelClassName="orgFormLabel"
          inputClassName=""
          containerClassName="view__document-attachment-drawer"
          handleFileChange={(value) => handleFileChange(value)}
          updateEnable={isEstimate ? Helpers.getPermission(Helpers.permissionEntities.SALES_ESTIMATE, Helpers.permissionTypes.UPDATE, user) : Helpers.getPermission(Helpers.permissionEntities.SALES_ORDER, Helpers.permissionTypes.UPDATE, user)}
        />
      </div> */}
      <ViewDocumentFooterDetails
        remark={selectedSO?.shopify_so_object?.note}
        termsAndConditions={selectedSO?.terms_and_conditions}
        fileList={fileList}
        disableAttachment={getAttachmentByIdLoading || updateAttachmentLoading}
        onFileChange={handleFileChange}
        canUpdateAttachment={isEstimate ? Helpers.getPermission(Helpers.permissionEntities.SALES_ESTIMATE, Helpers.permissionTypes.UPDATE, user) : Helpers.getPermission(Helpers.permissionEntities.SALES_ORDER, Helpers.permissionTypes.UPDATE, user)}
      />
      {(isDataMaskingPolicyEnable && isHideSellingPrice) ? <HideComponent style={{ width: "fit-content" }} /> : <div className="view-document__totals">
        <div className="view-document__totals-field">
          <H3Text text="Sub Total" className="view-document__totals-field-name" />
          <H3Text text={MONEY((selectedSO?.order_base_price), selectedSO?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
        </div>
        {renderFreight('top')}
        {selectedSO?.order_discount !== 0 && (
          <div className="view-document__totals-field">
            <H3Text text="Discount" className="view-document__totals-field-name" />
            {selectedSO?.order_discount > 0
              ? (
                <H3Text
                  text={`(-) ${MONEY((selectedSO?.order_discount), selectedSO?.org_currency_info?.currency_code)}`}
                  className="view-document__totals-field-value danger-text"
                />
              )
              : (
                <H3Text
                  text={MONEY((selectedSO?.order_discount), selectedSO?.org_currency_info?.currency_code)}
                  className="view-document__totals-field-value"
                />
              )}
          </div>
        )}
        {renderFreight('bottom')}
        {renderCharges(splitChargesData(selectedSO?.other_charges).chargeWithTaxName)}
        <div className="view-document__totals-field">
          <H3Text text="Taxable Amount" className="view-document__totals-field-name" />
          <H3Text text={MONEY((selectedSO?.taxable_amount || 0), selectedSO?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
        </div>
        {selectedSO?.tax_info?.map((tax, i) => (
          <div className="view-document__totals-field" key={i}>
            <H3Text text={tax?.tax_type_name} className="view-document__totals-field-name" />
            <H3Text text={MONEY((tax?.tax_amount), selectedSO?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
          </div>
        ))}
        {renderCharges(splitChargesData(selectedSO?.other_charges).chargeWithoutTaxName)}
        <div className="view-document__totals-field">
          <H3Text text="Payable Amount" className="view-document__totals-field-name" />
          <H3Text text={MONEY((selectedSO?.order_grand_total), selectedSO?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
        </div>
        {!selectedSO?.shopify_order_id ? (
          <div className="view-document__totals-field">
            <H3Text text="Round Off" className="view-document__totals-field-name" />
            {selectedSO?.order_round_off < 0
              ? (
                <H3Text
                  text={`(-) ${MONEY((Math.abs(selectedSO?.order_round_off)), selectedSO?.org_currency_info?.currency_code)}`}
                  className="view-document__totals-field-value danger-text"
                />
              )
              : (
                <H3Text
                  text={MONEY((Math.abs(selectedSO?.order_round_off)), selectedSO?.org_currency_info?.currency_code)}
                  className="view-document__totals-field-value"
                />
              )}
          </div>
        ) : ''}
        <div className="view-document__totals-field view-document__totals-field-total">
          {/* <H3Text text={`${isEstimate ? 'Estimate Total' : 'Order Total'}`} className="view-so__totals-field-name" /> */}
          <H3Text text={`${documentUINames.shortUIName} Total`} className="view-document__totals-field-name" />
          <H3Text text={MONEY((selectedSO?.shopify_order_id ? selectedSO?.order_grand_total : selectedSO?.order_grand_total + selectedSO?.order_round_off), selectedSO?.org_currency_info?.currency_code)} className="view-document__totals-field-value" />
        </div>
      </div>}
    </div>
  );
};

export default ViewSOFooter;