// 📦 React & Hooks
import React, { Fragment, useState } from 'react';

// 🚦 Routing
import { <PERSON>, withRouter } from 'react-router-dom';

// 🧩 Redux
import { connect } from 'react-redux';

// 🧾 Redux Actions
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import MOActions from '@Actions/moActions';
import GrnActions from '@Actions/grnActions';

// 📅 Utilities
import dayjs from 'dayjs';
import { toISTDate } from '@Apis/constants';

// 🧠 Helpers & Constants
import Helpers from '@Apis/helpers';

// 🔧 API Hooks
// (No API hooks present)

// 🎨 UI Components - H3 UI Kit
import H3Text from '@Uilib/h3Text';
import H3Image from '@Uilib/h3Image';

// 🎛️ Common Components
import ViewPurchaseOrder from '@Components/Common/ViewPurchaseOrder';
import HideValue from '../../../../Common/RestrictedAccess/HideValue';

// 📄 Module-Specific Components
import GRNForm from '../../../../Purchase/GoodsReceiving/GRNForm';
import GRNFormV2 from '@Components/Purchase/GoodsReceiving/GRNForm/index.refactored';

// 🧰 Ant Design Components
import { Drawer, notification, Table, Tag, Tooltip } from 'antd';

// 🎨 Icons
import { ExclamationCircleOutlined, LoadingOutlined } from '@ant-design/icons';

// 🖼️ Static Assets
// (No static assets present)

// 🖌️ Styles
import './style.scss';
import { cdnUrl } from '@Utils/cdnHelper';

/**
 * @param status
 */
function WorkOrdersList({ purchaseOrders, user, loading, getPurchaseOrderByIdLoading, selectedPurchaseOrder, getPurchaseOrderById, selectedMO, callback, getGRN, moId, getMOById, getMOByIdSuccess, MONEY, history, priceMasking }) {
  const [showNewGrnForm, setShowNewGrnForm] = useState(false);
  const [showPurchaseOrder, setShowPurchaseOrder] = useState(false);
  const [currentAction, setCurrentAction] = useState('');
  const [selectedPO, setSelectedPO] = useState(selectedPurchaseOrder);
  const getExpiryStatus = (expiryDate) => {
    const today = dayjs().startOf('day');
    const expiry = dayjs(expiryDate).startOf('day');
    const daysDifference = Number(expiry.diff(today, 'day'));
    if (daysDifference > 7) {
      const weeks = Math.floor(daysDifference / 7);
      // const remainingDays = daysDifference % 7;
      return `Expires in ${weeks} week${weeks > 1 ? 's' : ''} `;
    }
    if (daysDifference > 2) {
      return `Expires in ${daysDifference} days`;
    }
    if (daysDifference === 2) {
      return 'Expires in 2 days';
    }
    if (daysDifference === 1) {
      return 'Expires Tommorow';
    }
    if (daysDifference === 0) {
      return 'Expires Today';
    }
    if (daysDifference === -1) {
      return 'Expired 1 day ago';
    }
    if (daysDifference === -2) {
      return 'Expired 2 days ago';
    }
    if (daysDifference >= -7) {
      return 'Expired a week ago';
    }
    if (daysDifference >= -30) {
      const months = Math.abs(Math.floor(daysDifference / 30));
      return `Expired ${months} month${months > 1 ? 's' : ''} ago`;
    }
  };

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } =
    priceMasking;

  const getProductColumns = () => {
    const productListColumns = [
      {
        title: '',
        render: (record) => (
          <Link to={`/approval?type=po&id=${record.po_id}`}>
            <div className="mobile-list__item">
              <div style={{ display: 'flex' }}>
                <H3Text
                  text={`#${record?.po_number}`}
                  className="mobile-list__item-number"
                />
                <div
                  className="mobile-list__item-status"
                  style={{
                    backgroundColor: Helpers.getStatusColor(record.status)
                      .color,
                  }}
                >
                  {Helpers.getStatusColor(record.status).text?.toUpperCase()}
                </div>
              </div>
              <H3Text
                text={`Amount - ${MONEY(record?.po_grand_total || '0')}`}
                className="mobile-list__item-info"
              />
              <H3Text
                text={
                  <React.Fragment>
                    Receiving Status -&nbsp;
                    <span
                      style={{
                        color: Helpers.getStatusColor(
                          record?.po_receiving_status || 'NOT_RECEIVED'
                        ).color,
                      }}
                    >
                      {Helpers.getStatusColor(
                        record?.po_receiving_status || 'NOT_RECEIVED'
                      ).text?.toUpperCase()}
                    </span>
                  </React.Fragment>
                }
                className="mobile-list__item-info"
              />
              <H3Text
                text={`Created by ${record?.created_by_info ? `${record?.created_by_info.first_name} ${record?.created_by_info.last_name}` : 'Unknown'} on ${toISTDate(record?.created_at).format('DD/MM/YYYY')}`}
                className="mobile-list__item-footer"
              />
            </div>
          </Link>
        ),
        responsive: ['xs'],
      },
      {
        title: 'Order #',
        dataIndex: 'po_number',
        fixed: 'left',
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'SubContractor',
        render: (text, record) => (
          <Fragment>
            <div
              style={{
                color: 'rgb(106, 106, 106)',
                fontWeight: '600',
                fontSize: '12px',
                cursor: 'pointer',
              }}
            />
            <div
              style={{
                color: '#2D7DF7',
                fontWeight: '600',
                fontSize: '12px',
                cursor: 'pointer',
              }}
              onClick={() =>
                history.push(
                  `/vendors/view/${record?.tenant_seller_info?.seller_id}`
                )
              }
            >
              {record && record.tenant_seller_info
                ? `${record?.tenant_seller_info?.internal_slr_code ? `${record?.tenant_seller_info?.internal_slr_code} - ` : ''}${record.tenant_seller_info?.seller_name}`?.trim()
                : ''}
            </div>
            <div>
              {record?.seller_address?.city ? record.seller_address.city : ''}
            </div>
          </Fragment>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Items',
        render: (text, record) => record.total_po_items,
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Amount',
        render: (text, record) =>
          isDataMaskingPolicyEnable && isHideCostPrice ? (
            <HideValue
              showPopOver
              popOverMessage={'You don\'t have access to view amount'}
            />
          ) : (
            MONEY(record.po_grand_total || '0')
          ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Created Date',
        render: (text, record) => (
          <div>
            {toISTDate(record?.created_at).format('DD/MM/YYYY')}
            {/* <H3Text text={(record?.created_by_info ? `${record?.created_by_info.first_name} ${record?.created_by_info.last_name}` : 'Unknown')} className="table-subscript" /> */}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Order Date',
        render: (text, record) => (
          <div>{toISTDate(record?.po_date).format('DD/MM/YYYY')}</div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Payment Terms',
        render: (text, record) => (
          <div>
            {!record?.payment_terms?.[0]?.due_days
              ? 'On Delivery'
              : `${record?.payment_terms?.[0]?.due_days} Days Credit`}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Status',
        render: (item) => (
          <div
            className="qr__status"
            style={{
              backgroundColor: Helpers.getStatusColor(item.status).color,
            }}
          >
            {Helpers.getStatusColor(item.status).text}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Expiry Date',
        render: (item) => (
          <Tooltip
            title={toISTDate(item?.po_expiry_date).format('DD/MM/YYYY')}
            placement="topLeft"
            style={{ cursor: 'pointer' }}
          >
            {item?.po_expiry_date != null && (
              <Tag
                icon={<ExclamationCircleOutlined />}
                color={
                  getExpiryStatus(item?.po_expiry_date)?.includes(
                    'Expires Today'
                  )
                    ? 'warning'
                    : getExpiryStatus(item?.po_expiry_date)?.includes('Expired')
                      ? 'error'
                      : 'green'
                }
              >
                {getExpiryStatus(item?.po_expiry_date)}
              </Tag>
            )}
          </Tooltip>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
      {
        title: 'Receiving',
        render: (item) => (
          <div
            style={{
              color: Helpers.getStatusColor(
                item?.po_receiving_status || 'NOT_RECEIVED'
              ).color,
            }}
          >
            {Helpers.getStatusColor(
              item?.po_receiving_status || 'NOT_RECEIVED'
            ).text?.toUpperCase()}
          </div>
        ),
        responsive: ['sm', 'md', 'lg', 'xxl'],
      },
    ];
    productListColumns.push({
      title: '',
      key: 'action',
      render: (text, record) => (
        <div className="list__actions">
          <div
            className="list__actions-button"
            style={{ width: '70px', whiteSpace: 'nowrap' }}
            onClick={() => {
              if (record.status !== 'ISSUED') {
                notification.open({
                  message:
                    'This work order is not yet issued to the manufacturer. Please issue the order before creating goods receipt',
                  placement: 'top',
                  duration: 6,
                  type: 'warning',
                });
              } else {
                setCurrentAction('GRN');
                if (!getPurchaseOrderByIdLoading) {
                  getPurchaseOrderById(
                    record?.po_id,
                    selectedMO?.tenant_id,
                    () => {
                      setShowNewGrnForm(true);
                      setCurrentAction('');
                    }
                  );
                }
              }
            }}
          >
            {currentAction === 'GRN' && getPurchaseOrderByIdLoading ? (
              <LoadingOutlined />
            ) : (
              '+ GRN'
            )}
          </div>
          <div
            className="list__actions-button list__actions-button-dark"
            style={{ width: '70px', whiteSpace: 'nowrap' }}
            onClick={() => {
              setCurrentAction('VIEW_PO');
              if (!getPurchaseOrderByIdLoading) {
                getPurchaseOrderById(
                  record?.po_id,
                  selectedMO?.tenant_id,
                  () => {
                    getGRN(
                      Helpers.getTenantEntityPermission(
                        user?.user_tenants,
                        Helpers.permissionEntities.GOOD_RECEIVING,
                        Helpers.permissionTypes.READ
                      ).join(','),
                      null,
                      record?.po_id,
                      'PURCHASE_ORDER',
                      'DRAFT,ISSUED,REJECTED,VOID,PENDING_FOR_QC,SENT_FOR_APPROVAL',
                      10,
                      1,
                      null
                    );
                    setShowPurchaseOrder(true);
                    setCurrentAction('');
                  }
                );
              }
            }}
          >
            {currentAction === 'VIEW_PO' && getPurchaseOrderByIdLoading ? (
              <LoadingOutlined />
            ) : (
              'View'
            )}
          </div>
        </div>
      ),
      fixed: 'right',
      responsive: ['sm', 'md', 'lg', 'xxl'],
      className: 'list__actions-column',
    });
    return productListColumns;
  };

  const handleClose = (workflowSteps) => {
    setSelectedPO({
      ...selectedPurchaseOrder,
      workflow_steps: workflowSteps,
      status: 'SENT_FOR_APPROVAL',
    });
    getPurchaseOrderById(selectedPO?.po_id);
    getMOByIdSuccess(false);
    getMOById(selectedMO?.tenant_id, moId);
  };

  return (
    <div className="subcontractor-wo__wrapper">
      <div className="">
        <Table
          title={() => (
            <H3Text
              text={`${purchaseOrders?.length || '0'} Work Orders`}
              className="subcontractor-wo__title"
            />
          )}
          loading={loading}
          showHeader
          size="small"
          scroll={{ x: 'max-content' }}
          columns={getProductColumns()}
          rowKey="po_id"
          dataSource={purchaseOrders || []}
          pagination={false}
        />
      </div>
      <Drawer
        onClose={() => setShowNewGrnForm(false)}
        open={showNewGrnForm}
        width="1250px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '1200px' }}>
            <H3Text
              text="Create Goods Receiving Note"
              className="custom-drawer__title"
            />
            <H3Image
              src={cdnUrl('icon-close-blue.png', 'icons')}
              className="custom-drawer__close-icon"
              onClick={() => setShowNewGrnForm(false)}
            />
          </div>
        </div>
        {/* <GRNForm
          workOrder
          selectedPoForGrn={selectedPurchaseOrder}
          callback={() => {
            setShowNewGrnForm(false);
            callback();
          }}
          changeRoute={false}
        /> */}
        <GRNFormV2
          workOrder
          selectedPoForGrn={selectedPurchaseOrder}
          callback={() => {
            setShowNewGrnForm(false);
            callback();
          }}
        />
      </Drawer>
      <Drawer
        onClose={() => {
          setShowPurchaseOrder(false);
          callback();
        }}
        open={showPurchaseOrder}
        width="1140px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '1095px' }}>
            <H3Text text="View Work Order" className="custom-drawer__title" />
            <H3Image
              src={cdnUrl('icon-close-blue.png', 'icons')}
              className="custom-drawer__close-icon"
              onClick={() => {
                setShowPurchaseOrder(false);
                callback();
              }}
            />
          </div>
        </div>
        <ViewPurchaseOrder
          fromMOScreen
          moCallback={() => {
            setShowPurchaseOrder(false);
            callback();
          }}
          handleClose={(workflowSteps) => handleClose(workflowSteps)}
          isQuickView
        />
      </Drawer>
    </div>
  );
}

/**
 *
 * @param UserReducers
 * @returns {{user}}
 */
const mapStateToProps = ({ UserReducers, PurchaseOrderReducers }) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  getPurchaseOrderByIdLoading:
    PurchaseOrderReducers.getPurchaseOrderByIdLoading,
  selectedPurchaseOrder: PurchaseOrderReducers.selectedPurchaseOrder,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
  getMOById: (tenantId, moId) => dispatch(MOActions.getMOById(tenantId, moId)),
  getMOByIdSuccess: (selectedMO) =>
    dispatch(MOActions.getMOByIdSuccess(selectedMO)),
  getPurchaseOrderById: (poId, tenantId, callback) =>
    dispatch(
      PurchaseOrderActions.getPurchaseOrderById(poId, tenantId, callback)
    ),
  getGRN: (
    tenantId,
    grnId,
    grnEntityId,
    grnEntityType,
    status,
    limit,
    page,
    tenantSellerId
  ) =>
    dispatch(
      GrnActions.getGRN(
        tenantId,
        grnId,
        grnEntityId,
        grnEntityType,
        status,
        limit,
        page,
        tenantSellerId
      )
    ),
});

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(withRouter(WorkOrdersList));
