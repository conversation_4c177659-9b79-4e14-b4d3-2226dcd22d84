// 📦 React & Related
import React, { Fragment, useState } from 'react';
import { connect } from 'react-redux';
import { Link, withRouter } from 'react-router-dom';

// 🧩 Third-Party Libraries
import { Drawer, Progress, Table, Tag, Tooltip } from 'antd';

// 🛠️ Utilities & Constants
import Helpers from '@Apis/helpers';
import { DEFAULT_CUR_ROUND_OFF, QUANTITY, toISTDate } from '@Apis/constants';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';

// 🎯 Redux Actions
import ProductActions from '@Actions/productActions';

// 🧱 UI Components
import H3Text from '@Uilib/h3Text';
import PRZModal from '../../../../Common/UI/PRZModal';
import ProductCategoryLabel from '../../../../Common/ProductCategoryLabel';
import RecentTransactions from '@Components/Inventory/ManageProducts/ViewProduct/RecentTransactions';
import { textTruncate } from '@Components/Common/UI/PRZHelper/helpers';
import { cdnUrl } from "@Utils/cdnHelper";
import HideValue from '@Components/Common/RestrictedAccess/HideValue';
import H3Image from '@Uilib/h3Image';
import ViewAllocatedLandingPrice from '../ViewAllocatedLandingPrice';
import { EyeFilled } from '@ant-design/icons';
import PRZText from '@Components/Common/UI/PRZText';

// 🎨 Styles
import './style.scss';

function ViewAccountPayableInvoiceLines({ apInvoiceLines, selectedAPI, visibleColumns, cfApInvoiceLine, priceMasking, user, MONEY, isVendorOverseas, manualRefetch,
}) {
  const [state, setState] = useState({
    showPRZModal: false,
    selectedDocumentId: null,
    showAllocatedLandingPriceDrawer: false,
    selectedAPInvoiceLine: null,
  });
  const {
    showPRZModal,
    selectedDocumentId,
    showAllocatedLandingPriceDrawer,
    selectedAPInvoiceLine,
  } = state;
  const updateState = (object) => {
    setState({
      ...state,
      ...object,
    });
  };

  const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

  const renderLandedCost = (record) => {
    const maskingEnabled = isDataMaskingPolicyEnable && isHideCostPrice;
    if (maskingEnabled) {
      return <HideValue showPopOver popOverMessage={'You don\'t have access to view landed cost'} />;
    }
    else if (record?.allocations_from?.length > 0) {
      const totalAllocated = record?.allocations_from?.reduce((acc, cur) => acc + Number(cur?.allocation_amount), 0);
      return (
        <div>
          {totalAllocated ? (
            <div className="flex-align-center-justify-between" onClick={() => updateState({ showAllocatedLandingPriceDrawer: true, selectedAPInvoiceLine: record })} style={{ cursor: 'pointer' }}>
              <H3Text text={MONEY(totalAllocated, selectedAPI?.org_currency_info?.currency_code)} />
              <Tooltip title='View Allocated Landing Price'>
                <EyeFilled />
              </Tooltip>
            </div>
          ) : '-'}
        </div>
      );
    }
    else if (record?.is_landed_cost) {
      const landedCostAllocated = record?.base_value_before_tax - (record?.balance_landed_cost ?? 0);
      return (
        <div onClick={() => updateState({ showAllocatedLandingPriceDrawer: true, selectedAPInvoiceLine: record })} style={{ cursor: 'pointer' }}>
          {`${MONEY(landedCostAllocated ?? 0, selectedAPI?.org_currency_info?.currency_code)} / ${MONEY(record?.base_value_before_tax, selectedAPI?.org_currency_info?.currency_code)}`}
          <Progress
            percent={Number.parseInt((Math.abs(record?.base_value_before_tax - record?.balance_landed_cost) / record?.base_value_before_tax) * 100)}
            status="active"
            size="small"
            strokeColor={{ from: '#108ee9', to: '#87d068' }}
            trailColor="#2d7df71a"
            strokeLinecap="round"
            strokeWidth={6}
          />
        </div>
      );
    }
    else {
      return '-';
    }
  };

  const getColumns = () => {

    const columns = [
      {
        title: '',
        responsive: ['xs'],
        render: (record) => (
          <Link to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`} target="_blank">
            <div className="mobile-list__item">
              <Link to={`/inventory/product/view/${record?.product_sku_info?.product_sku_id}`} target="_blank">
                <H3Text text={`SKU Number: ${record?.product_sku_info?.internal_sku_code}`} className="mobile-list__item-number" />
              </Link>
              <H3Text text={record?.product_sku_info?.product_sku_name} className="mobile-list__item-info" />
              <H3Text text={`Quantity: ${QUANTITY(record?.quantity, record?.uom_info?.[0]?.precision)} ${record?.uom_info?.[0]?.qc?.toProperCase()}`} className="mobile-list__item-info" />
              <H3Text text={`Required By Date: ${toISTDate(record?.required_by_date, 'DD/MM/YYYY')}`} className="mobile-list__item-info" />
              <H3Text text={`Planning Number: ${record?.planning_number}`} className="mobile-list__item-info" />
            </div>
          </Link>
        ),
        visible: visibleColumns?.PRODUCT?.visible,
      },
      {
        title: 'Product',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => {
          return (
            <Fragment>
              <div onClick={() => updateState({ showPRZModal: true, selectedDocumentId: record?.product_sku_info?.product_sku_id })}>
                <a>
                  {`#${record?.product_sku_info?.internal_sku_code}`}
                </a>
              </div>
              <div className="flex-display" style={{ display: 'flex', flexDirection: 'column' }}>
                <div style={{ maxWidth: '200px' }}>
                  {`${record.product_sku_info?.ref_product_code ? `${record.product_sku_info?.ref_product_code} - ` : ''}${record?.product_sku_info?.product_sku_name}`?.trim()}
                  <div
                    dangerouslySetInnerHTML={{
                      __html: (isDataMaskingPolicyEnable && isHideCostPrice) ? '' : record?.remarks,
                    }}
                    className="table-subscript"
                    style={{
                      width: '200px',
                      whiteSpace: 'pre-wrap',
                    }}
                  />
                  {(isDataMaskingPolicyEnable && isHideCostPrice) && <HideValue showPopOver />}
                  {!record?.tenant_product_id && <H3Text text="New Product" className="view-pi-selection__new-product" />}
                </div>
                {record?.grn_number &&
                  <div>
                    <Link
                      to={`/purchase/goods-receiving/view/${record?.grn_id}`}
                      target="_blank"
                    >
                      {`${record?.grn_number || ''}`}
                    </Link>
                  </div>
                }
                <div className="flex-align-center-justify-between" style={{ gap: '10px' }}>
                  {record?.product_sku_info?.product_category_info?.category_path?.length > 0 && (
                    <ProductCategoryLabel
                      categoryPath={record?.product_sku_info?.product_category_info?.category_path}
                      categoryName={record?.product_sku_info?.product_category_info?.category_path?.at(
                        -1,
                      )}
                      containerStyle={{
                        width: 'fit-content',
                      }}
                    />
                  )}
                  {record?.is_landed_cost && (
                    <Tag color='volcano' style={{ fontSize: '10px' }}>
                      Landed Cost
                    </Tag>
                  )}
                  {Helpers.getPermission(Helpers.permissionEntities.PURCHASE_INDENT, Helpers.permissionTypes.CREATE, user) && (
                    <RecentTransactions
                      tenantId={selectedAPI?.tenant_id}
                      productSkuId={record?.product_sku_info?.product_sku_id}
                      internalSkuCode={record?.product_sku_info?.internal_sku_code}
                      refProductCode={record?.product_sku_info?.ref_product_code}
                      productSkuName={record?.product_sku_info?.product_sku_name}
                    />
                  )}
                </div>
              </div>
            </Fragment>
          );
        },
        visible: visibleColumns?.PRODUCT?.visible,
      },
      {
        title: 'Quantity',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => `${QUANTITY(item?.quantity, item?.uom_info?.[0]?.precision)} ${item?.uom_info?.[0]?.uqc?.toProperCase()}`,
        visible: visibleColumns?.QUANTITY?.visible,
      },
      {
        title: 'Landed Cost',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => renderLandedCost(item),
        visible: visibleColumns?.LANDED_COST?.visible,
      },
      {
        title: 'Unit Price',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (
          <div>
            {(isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={'You don\'t have access to view unit price'} /> : MONEY((item?.offer_price), selectedAPI?.org_currency_info?.currency_code)}
            {!isVendorOverseas && (<H3Text
              text={`+ tax@${item?.tax_info?.tax_value}%`}
              className="danger-subtext"
            />)}
          </div>
        ),
        visible: visibleColumns?.UNIT_PRICE?.visible,
      },
      {
        title: 'Discount',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (record) => (
          <Fragment>
            {
              Number(record?.line_discount_amount) > 0
                ? (
                  <Fragment>
                    {(record?.is_discount_in_percent ? `${Number(record?.line_discount_percentage).toPrecision(DEFAULT_CUR_ROUND_OFF) || '0'}%` : MONEY(record?.line_discount_amount, selectedAPI?.org_currency_info?.currency_code))}
                  </Fragment>
                )
                : '-'
            }
          </Fragment>
        ),
        visible: visibleColumns?.DISCOUNT?.visible,
        className: !selectedAPI?.is_line_wise_discount ? 'display-none' : '',
      },
      {
        title: 'Purchase Account',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => item?.tally_purchase_account,
        visible: visibleColumns?.PURCHASE_ACCOUNT?.visible,
      },
      {
        title: 'Total',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (isDataMaskingPolicyEnable && isHideCostPrice) ? <HideValue showPopOver popOverMessage={'You don\'t have access to view unit price'} /> : MONEY((((Number(item.quantity || 1) * Number(item.offer_price || 1)) * (item.line_discount_percentage ? Number(100 - item.line_discount_percentage) / 100 : 1)) * (1 + Number(item?.tax_info?.tax_value) / 100)), selectedAPI?.org_currency_info?.currency_code),
        visible: visibleColumns?.TOTAL?.visible,
      },
      {
        title: 'Assessable Value',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (
          <Fragment>
            <PRZText text={`${item?.assessable_value ? MONEY(QUANTITY(item?.assessable_value, item?.uom_info?.[0]?.precision), selectedAPI?.base_currency_info?.currency_code) : '-'}`} />
            {selectedAPI?.base_currency_info?.currency_code !== selectedAPI?.org_currency_info?.currency_code && (<PRZText text={`${item?.document_assessable_value ? MONEY(QUANTITY(item?.document_assessable_value, item?.uom_info?.[0]?.precision), selectedAPI?.org_currency_info?.currency_code) : '-'}`} />)}
          </Fragment>
        ),
        visible: visibleColumns?.ASSESSABLE_VALUE?.visible && selectedAPI?.integration_tally_config?.purchase_voucher_integration,
      },
      {
        title: 'Taxable Type',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (
          <span>
            {textTruncate({ text: item?.taxable_type, truncateTextLength: 30, enableTooltip: true }) || '-'}
          </span>
        ),
        visible: visibleColumns?.TAXABLE_TYPE?.visible && selectedAPI?.integration_tally_config?.purchase_voucher_integration,
      },
      {
        title: 'Nature of Transaction',
        responsive: ['sm', 'md', 'lg', 'xxl'],
        render: (item) => (
          <span>
            {textTruncate({ text: item?.nature_of_transaction, truncateTextLength: 30, enableTooltip: true }) || '-'}
          </span>
        ),
        visible: visibleColumns?.NATURE_OF_TRANSACTION?.visible && selectedAPI?.integration_tally_config?.purchase_voucher_integration,
      }
    ];
    if (cfApInvoiceLine?.length) {
      columns.splice(4, 0, ...CustomFieldHelpers.renderCustomLineColumns(false, cfApInvoiceLine, visibleColumns, null));
    };

    return columns.filter((item) => item.visible);
  };

  const getData = (data) => (JSON.parse(JSON.stringify(data)));
  return (
    <Fragment>
      <div className='pi-table-wrapper'>
        <Table
          bordered
          showHeader
          title={() => (`${apInvoiceLines?.length || 0} AP Invoice Lines `)}
          size='small'
          columns={getColumns()}
          dataSource={getData(apInvoiceLines || [])}
          pagination={false}
          scroll={{ x: 'max-content' }}
        />
      </div>
      <PRZModal
        isOpen={showPRZModal}
        onClose={() => {
          updateState({ showPRZModal: false, selectedDocumentId: null });
        }}
        entityType="product"
        documentId={selectedDocumentId}
      />
      <Drawer
        open={showAllocatedLandingPriceDrawer}
        onClose={() => updateState({ showAllocatedLandingPriceDrawer: false })}
        width="950px"
        destroyOnClose
      >
        <div className="custom-drawer__header-wrapper">
          <div className="custom-drawer__header" style={{ width: '900px' }}>
            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: '100%' }} className="custom-drawer__title">
              {`View Allocated Landing Price - ${selectedAPInvoiceLine?.product_sku_info?.internal_sku_code} ${selectedAPInvoiceLine?.product_sku_info?.product_sku_name}`}
            </div>
            <H3Image src={cdnUrl("icon-close-blue.png", "icons")} className="custom-drawer__close-icon" onClick={() => updateState({ showAllocatedLandingPriceDrawer: false })} />
          </div>
        </div>
        <ViewAllocatedLandingPrice
          selectedEntityLine={selectedAPInvoiceLine}
          callback={() => {
            updateState({ showAllocatedLandingPriceDrawer: false });
            manualRefetch();
          }}
          curencyCode={selectedAPI?.org_currency_info?.currency_code}
        />
      </Drawer>
    </Fragment>
  );
};

const mapStateToProps = ({ UserReducers, ProductReducers }) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  selectedOrgProduct: ProductReducers.selectedOrgProduct,
  getOrgProductByIdLoading: ProductReducers.getOrgProductByIdLoading,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = (dispatch) => ({
  getOrgProductById: (orgId, tenantId, productSkuId, callback) => dispatch(ProductActions.getOrgProductById(orgId, tenantId, productSkuId, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(ViewAccountPayableInvoiceLines));