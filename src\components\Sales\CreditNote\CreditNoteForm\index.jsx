import React, { Component, Fragment } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import Decimal from 'decimal.js';
import { Helmet } from 'react-helmet';
import {
  DatePicker, Drawer, Select, Upload, Checkbox, Popconfirm,
  notification,
  Alert,
  Tooltip,
  Popover,
} from 'antd';
import {
  EditFilled, PlusOutlined, PlusCircleFilled,
  InfoCircleOutlined,
} from '@ant-design/icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCircleXmark, faCircleInfo, } from '@fortawesome/free-solid-svg-icons';
import { v4 as uuidv4 } from 'uuid';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import Constants, {
  H3_TITLE, ISTDateFormat, QUANTITY, DEFAULT_CUR_ROUND_OFF,
} from '@Apis/constants';
import H3Text from '@Uilib/h3Text';
import Helpers from '@Apis/helpers';
import H3Image from '@Uilib/h3Image';
import CustomerActions from '@Actions/customerActions';
import { cdnUrl } from "@Utils/cdnHelper";
import CNActions from '@Actions/cnActions';
import H3FormInput from '@Uilib/h3FormInput';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import PurchaseOrderActions from '@Actions/purchaseOrderActions';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import OfferActions from '@Actions/offerActions';
import InvoiceActions from '@Actions/invoiceActions';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import SelectDepartment from '@Components/Common/SelectDepartment';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import CNLines from './CNLines';
import SelectCustomer from '../../../Common/SelectCustomer';
import CustomerForm from '../../Customer/CustomerHome/CreateCustomer/CustomerForm';
import RichTextEditor from '../../../Common/RichTextEditor';
import SelectTaxType from '../../../Admin/Common/SelectTaxType';
import SelectExtraCharge from '../../../Admin/Common/SelectExtraCharge';
import CustomFieldV3 from '@Components/Common/CustomFieldV3';
import CurrencyConversionV2 from '../../../Common/CurrencyConversionV2';
import ChargesTaxInput from '@Components/Common/ChargesTaxInput';
import FormHelpers from '../../../../helpers/FormHelpers';
import PRZSelect from '../../../Common/UI/PRZSelect';
import DocumentNumberSeqInput from '../../../Admin/Common/DocumentNumberSeqInput';
import CustomDocumentColumns from '../../../Common/CustomDocumentColumns';
import SelectAppUser from '../../../Common/SelectAppUser';
import './style.scss';

const { Option } = Select;
const uploadButton = (
  <div>
    <PlusOutlined />
    <div style={{ marginTop: 8 }}>Upload</div>
  </div>
);

class CreditNoteForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      data: [],
      chargeData: [],
      cnDate: dayjs(),
      isLineWiseDiscount: true,
      showAddCustomer: true,
      showNewCustomerModal: false,
      taxTypeName: 'TCS',
      selectedCurrencyName: '',
      selectedCurrencyID: '',
      child_taxes: [{
        tax_amount: 0,
        tax_type_name: '',
      }],
      creditNoteWithReturnedQyt: false,
      creditNoteWithoutLines: false,
      isAutomaticConversionRate: null,
      currencyConversionRate: '',
      narration: '',
      selectedTenantTallyIntegrationId: props?.user?.tenant_info?.it_id,
      discountType: 'Percent',
      isReturnSlip: false,
      salesAccount: props?.user?.tenant_info?.tally_configuration?.sales_account_name,
      accountManager: null,
      visibleColumns: {
        PRODUCT: {
          label: 'Product',
          visible: true,
          disabled: true,
        },
        CONSUMED_QUANTITY: {
          label: 'Consumed Quantity',
          visible: true,
          disabled: true,
        },
        RETURNED_QUANTITY: {
          label: 'Returned',
          visible: true,
          disabled: true,
        },
        QUANTITY: {
          label: 'Quantity',
          visible: true,
          disabled: true,
        },
        QUANTITY_TO_BE_RETURNED: {
          label: 'Quantity to be Returned',
          visible: false,
          disabled: true,
        },
        UNIT_PRICE: {
          label: 'Unit Price',
          visible: true,
          disabled: true,
        },
        TAX: {
          label: 'Tax',
          visible: true,
          disabled: true,
        },
        DISCOUNT: {
          label: 'Discount',
          visible: true,
          disabled: true,
        },
        LINE_TOTAL: {
          label: 'Line Total',
          visible: true,
          disabled: true,
        },
      }
    };
  }

  componentWillUnmount() {
    const {
      getCNByIdSuccess,
    } = this.props;
    getCNByIdSuccess(null);
  }

  componentDidMount() {
    const {
      getCNById, user, match, selectedInvoiceForCN, getInvoice, getInvoiceSuccess, getDocCFV2, getCurrencies, isReturnSlip,
    } = this.props;
    const { visibleColumns } = this.state;
    getInvoiceSuccess(null);
    let entityName = 'CREDIT_NOTE';
    if (selectedInvoiceForCN?.invoice_type === 'CONSUMPTION_ORDER' || isReturnSlip || window.location.href.includes('/return-slip')) {
      entityName = 'RETURN_SLIP';
    }
    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName,
    };
    getDocCFV2(payload);
    getCurrencies();
    const { creditNoteId, returnSlipId } = match.params;
    if (creditNoteId) {
      getCNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.CREDIT_NOTE, Helpers.permissionTypes.READ).join(',') || user?.tenant_info?.tenant_id, creditNoteId);
    } else if (returnSlipId) {
      getCNById(Helpers.getTenantEntityPermission(user?.user_tenants, Helpers.permissionEntities.RETURN_SLIP, Helpers.permissionTypes.READ).join(',') || user?.tenant_info?.tenant_id, returnSlipId, 'RETURN_SLIP');
    }

    if (selectedInvoiceForCN) {
      const invoiceLines = selectedInvoiceForCN?.invoice_lines?.filter((item) => !item?.bundle_products?.length)?.map((orderLine) => {

        const discountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(orderLine?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(orderLine?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = orderLine?.is_discount_in_percent ? (orderLine.quantity * orderLine.unit_price) * (1 - discountValue / 100) : Math.max(orderLine.quantity * orderLine.unit_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(orderLine?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
        return {
          key: uuidv4(),
          asset1: orderLine?.product_sku_info?.assets?.[0]?.url || '',
          product_sku_name: orderLine?.product_sku_info?.product_sku_name,
          product_category_info: orderLine?.product_sku_info?.product_category_info,
          product_sku_info: orderLine?.product_sku_info,
          invoice_line_id: orderLine?.invoice_line_id,
          remarks: orderLine?.remarks?.replace(/<[^>]+>/g, '') || '',
          remarkRequired: !!orderLine?.remarks?.replace(/<[^>]+>/g, ''),
          invoice_qty: QUANTITY(orderLine?.quantity, orderLine?.uom_info[0]?.precision),
          returned_qty: QUANTITY(orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          quantity: QUANTITY(orderLine?.quantity - orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          unitPrice: Number(orderLine?.unit_price),
          uomId: Number(orderLine?.uom_id),
          uom_info: orderLine?.uom_info[0],
          product_batches: this.getBatches(orderLine?.product_batches, orderLine?.quantity - orderLine?.cn_quantity),
          uomGroup: Number(orderLine?.group_id),
          taxId: Number(orderLine?.tax_id),
          taxInfo: orderLine?.tax_group_info,
          child_taxes: Helpers.computeTaxation(taxableValue, orderLine?.tax_group_info, user?.tenant_info?.state, selectedInvoiceForCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          discount: discountValue,
          unitDiscount: unitDiscountValue,
          lineDiscountType: orderLine?.is_discount_in_percent ? 'Percent' : 'Amount',
          showUnitDiscount: orderLine?.is_discount_in_percent && unitDiscountValue > 0,
          hsn_code: orderLine?.hsn_code,
          order_line_id: orderLine?.order_line_id,
          tenant_product_id: orderLine?.tenant_product_id,
          updateStock: true,
          manufacturingDateFormat: orderLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: orderLine?.product_sku_info?.expiry_date_format,
        };
      });
      const bundleProductInvoiceLines = [];
      selectedInvoiceForCN?.invoice_lines?.filter((item) => item?.bundle_products?.length)?.map((item) => {
        bundleProductInvoiceLines.push(...(item.bundle_products || []).map((innerItem) => ({
          ...innerItem,
          bundle_name: item?.product_sku_info?.product_sku_name,
        })));
      });
      invoiceLines.push(...(bundleProductInvoiceLines || []).map((orderLine) => {
        const discountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(orderLine?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(orderLine?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = orderLine?.is_discount_in_percent
          ? (orderLine.quantity * orderLine.unit_price) * (1 - discountValue / 100)
          : Math.max(orderLine.quantity * orderLine.unit_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(orderLine?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
        return {
          key: uuidv4(),
          asset1: orderLine?.product_sku_info?.assets?.[0]?.url || '',
          product_sku_name: orderLine?.product_sku_info?.product_sku_name,
          product_sku_info: orderLine?.product_sku_info,
          invoice_line_id: orderLine?.invoice_line_id,
          remarks: orderLine?.remarks?.replace(/<[^>]+>/g, '') || '',
          remarkRequired: !!orderLine?.remarks?.replace(/<[^>]+>/g, ''),
          invoice_qty: QUANTITY(orderLine?.quantity, orderLine?.uom_info[0]?.precision),
          returned_qty: QUANTITY(orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          quantity: QUANTITY(orderLine?.quantity - orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          unitPrice: 1,
          uomId: Number(orderLine?.uom_id),
          uom_info: orderLine?.uom_info[0],
          product_batches: this.getBatches(orderLine?.product_batches, orderLine?.quantity - orderLine?.cn_quantity),
          uomGroup: Number(orderLine?.group_id),
          taxId: Number(orderLine?.tax_id),
          taxInfo: orderLine?.tax_group_info,
          child_taxes: Helpers.computeTaxation(taxableValue, orderLine?.tax_group_info, user?.tenant_info?.state, selectedInvoiceForCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          hsn_code: orderLine?.hsn_code,
          discount: discountValue,
          unitDiscount: unitDiscountValue,
          lineDiscountType: orderLine?.is_discount_in_percent ? 'Percent' : 'Amount',
          showUnitDiscount: orderLine?.is_discount_in_percent && unitDiscountValue > 0,
          order_line_id: orderLine?.order_line_id,
          tenant_product_id: orderLine?.tenant_product_id,
          updateStock: true,
          isBundled: true,
          bundle_name: orderLine?.bundle_name,
          manufacturingDateFormat: orderLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: orderLine?.product_sku_info?.expiry_date_format,
        };
      }));
      this.setState({
        selectedCustomer: selectedInvoiceForCN?.customer_id,
        selectedCustomerInfo: selectedInvoiceForCN?.customer_info,
        gstNumber: selectedInvoiceForCN?.customer_info?.gst_number,
        billingAddress: isReturnSlip ? selectedInvoiceForCN?.shipping_address_info : selectedInvoiceForCN?.billing_address_info,
        shippingAddress: isReturnSlip ? selectedInvoiceForCN?.ship_from_address_info : selectedInvoiceForCN?.shipping_address_info,
        data: invoiceLines || [],
        paymentTerms: Number(selectedInvoiceForCN?.payment_terms?.[0]?.due_days),
        selectedInvoice: selectedInvoiceForCN?.invoice_id,
        termsAndConditions: selectedInvoiceForCN?.terms_and_conditions,
        fileList: selectedInvoiceForCN?.attachments,
        selectedInvoiceForCN,
        discountPercentage: selectedInvoiceForCN?.is_discount_in_percent ? selectedInvoiceForCN?.discount_percentage?.toFixed(DEFAULT_CUR_ROUND_OFF) : selectedInvoiceForCN?.discount_amount?.toFixed(DEFAULT_CUR_ROUND_OFF),
        isLineWiseDiscount: selectedInvoiceForCN?.is_line_wise_discount,
        discountType: selectedInvoiceForCN?.is_discount_in_percent ? 'Percent' : 'Amount',
        chargeData: selectedInvoiceForCN?.other_charges?.map((item) => ({
          ...item,
          chargeKey: uuidv4(),
          chargesTaxId: item?.tax_info?.tax_id,
          chargesSacCode: item?.charge_sac_code,
          chargesTaxInfo: item?.tax_info,
          chargesTax: item?.tax_info?.tax_value,
          tallyLedgerName: item?.ledger_name || null,
          chargesTaxData: {
            ...item?.tax_info,
            child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, selectedInvoiceForCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          },
        })) || [],
        taxType: selectedInvoiceForCN?.tcs_id || null,
        taxTypeId: selectedInvoiceForCN?.tcs_id ? selectedInvoiceForCN?.tcs_info?.tax_id : null,
        taxTypeInfo: selectedInvoiceForCN?.tcs_id ? selectedInvoiceForCN?.tcs_info : null,
        selectedCurrencyID: selectedInvoiceForCN?.org_currency_info?.org_currency_id,
        selectedCurrencyName: selectedInvoiceForCN?.org_currency_info,
        taxTypeName: selectedInvoiceForCN?.tcs_id ? selectedInvoiceForCN?.tcs_info?.tax_type_name : 'TCS',
        isAutomaticConversionRate: false,
        currencyConversionRate: selectedInvoiceForCN?.conversion_rate,
        selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedInvoiceForCN?.tenant_id)?.it_id,
        salesAccount: selectedInvoiceForCN?.tally_sales_account_name,
        selectedTenant: selectedInvoiceForCN?.tenant_id,
        tenantDepartmentId: selectedInvoiceForCN?.tenant_department_id || user?.tenant_info?.default_store_id,
        accountManager: selectedInvoiceForCN?.account_manager_id || null,
      });
    }
    this.setState({
      isReturnSlip: window.location.href.includes('/return-slip') || isReturnSlip,
      visibleColumns: (window.location.href.includes('/return-slip') || isReturnSlip) ? {
        ...visibleColumns,
        UNIT_PRICE: {
          ...visibleColumns?.UNIT_PRICE,
          visible: false,
        },
        TAX: {
          ...visibleColumns?.TAX,
          visible: false,
        },
        DISCOUNT: {
          ...visibleColumns?.DISCOUNT,
          visible: false,
        },
        LINE_TOTAL: {
          ...visibleColumns?.LINE_TOTAL,
          visible: false,
        },
      } : visibleColumns,
    });
  }

  static getDerivedStateFromProps(props, state) {
    const { CurrenciesResults, user, selectedInvoiceForCN } = props;
    const { isReturnSlip } = state;

    if (CurrenciesResults && CurrenciesResults?.length > 0) {
      const defaultCurrency = CurrenciesResults?.find((currency) => currency.is_default === true);
      if (defaultCurrency && !state.selectedCurrencyID) {
        return {
          ...state,
          selectedCurrencyID: defaultCurrency?.org_currency_id,
          selectedCurrencyName: defaultCurrency,
          currencyConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate ? defaultCurrency?.automatic_conversion_rate : defaultCurrency?.conversion_rate,
          isAutomaticConversionRate: user?.tenant_info?.global_config?.settings?.automatic_conversion_rate,
        };
      }
    }
    if (props.cfV2DocCreditNote && !state.isUserReadyForCF) {
      const invoiceCustomFields = props.selectedInvoiceForCN?.custom_fields || [];
      return {
        ...state,
        cfCreditNoteDoc: CustomFieldHelpers.mergeCustomFields(props.cfV2DocCreditNote?.data?.document_custom_fields, invoiceCustomFields),
        tenantDepartmentId: props.user?.tenant_info?.default_store_id,
        isUserReadyForCF: true,
      };
    }
    if (props.cfV2DocReturnSlip && !state.isUserReadyForCFReturnSlip && props?.selectedInvoiceForCN) {
      const getBatches = (oldBaches, quantity) => {
        const batches = oldBaches;
        let remainingQty = new Decimal(quantity);
        for (let i = 0; i < batches?.length; i++) {
          batches[i].quantity = 0;
        }

        for (let i = 0; i < batches?.length; i++) {
          const consumedQty = new Decimal(batches?.[i]?.consumed_qty || 0);
          const cnGenQty = new Decimal(batches?.[i]?.cn_gen_qty || 0);
          const diffQty = consumedQty.minus(cnGenQty);

          if (remainingQty.greaterThan(0) && diffQty.greaterThan(0)) {
            const allocatable = Decimal.min(diffQty, remainingQty);
            batches[i].quantity = allocatable.toNumber();
            batches[i].batch_in_use = true;
            remainingQty = remainingQty.minus(allocatable);
          } else {
            batches[i].batch_in_use = false;
          }
        }
        return batches;
      };
      const consumptionCustomFields = props?.selectedInvoiceForCN?.custom_fields || [];
      const invoiceLines = props?.selectedInvoiceForCN?.invoice_lines?.filter((item) => !item?.bundle_products?.length)?.map((orderLine) => {
        const discountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(orderLine?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(orderLine?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = orderLine?.is_discount_in_percent ? (orderLine.quantity * orderLine.unit_price) * (1 - discountValue / 100) : Math.max(orderLine.quantity * orderLine.unit_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(orderLine?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
        return {
          key: uuidv4(),
          asset1: orderLine?.product_sku_info?.assets?.[0]?.url || '',
          product_sku_name: orderLine?.product_sku_info?.product_sku_name,
          product_category_info: orderLine?.product_sku_info?.product_category_info,
          product_sku_info: orderLine?.product_sku_info,
          invoice_line_id: orderLine?.invoice_line_id,
          remarks: orderLine?.remarks?.replace(/<[^>]+>/g, '') || '',
          remarkRequired: !!orderLine?.remarks?.replace(/<[^>]+>/g, ''),
          invoice_qty: QUANTITY(orderLine?.quantity, orderLine?.uom_info[0]?.precision),
          returned_qty: QUANTITY(orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          quantity: QUANTITY(orderLine?.quantity - orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          unitPrice: Number(orderLine?.unit_price),
          uomId: Number(orderLine?.uom_id),
          uom_info: orderLine?.uom_info[0],
          product_batches: getBatches(orderLine?.product_batches, orderLine?.quantity - orderLine?.cn_quantity),
          uomGroup: Number(orderLine?.group_id),
          taxId: Number(orderLine?.tax_id),
          taxInfo: orderLine?.tax_group_info,
          child_taxes: Helpers.computeTaxation(taxableValue, orderLine?.tax_group_info, user?.tenant_info?.state, selectedInvoiceForCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          discount: discountValue,
          unitDiscount: unitDiscountValue,
          lineDiscountType: orderLine?.is_discount_in_percent ? 'Percent' : 'Amount',
          showUnitDiscount: orderLine?.is_discount_in_percent && unitDiscountValue > 0,
          hsn_code: orderLine?.hsn_code,
          order_line_id: orderLine?.order_line_id,
          tenant_product_id: orderLine?.tenant_product_id,
          updateStock: true,
          manufacturingDateFormat: orderLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: orderLine?.product_sku_info?.expiry_date_format,
          lineCustomFields: isReturnSlip ? CustomFieldHelpers.mergeCustomFields(props.cfV2DocReturnSlip?.data?.document_line_custom_fields, orderLine?.custom_fields)?.map((cf) => ({
            ...cf,
            fieldValue: cf?.fieldName === 'Quantity' ? Number(orderLine?.quantity) : cf?.fieldValue,
          })) || [] : [],
        };
      });
      const bundleProductInvoiceLines = [];
      props?.selectedInvoiceForCN?.invoice_lines?.filter((item) => item?.bundle_products?.length)?.map((item) => {
        bundleProductInvoiceLines.push(...(item.bundle_products || []).map((innerItem) => ({
          ...innerItem,
          bundle_name: item?.product_sku_info?.product_sku_name,
        })));
      });
      invoiceLines.push(...bundleProductInvoiceLines.map((orderLine) => {
        const discountValue = orderLine?.is_discount_in_percent ? Number.parseFloat(Number(orderLine?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(orderLine?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = orderLine?.is_discount_in_percent
          ? (orderLine.quantity * orderLine.unit_price) * (1 - discountValue / 100)
          : Math.max(orderLine.quantity * orderLine.unit_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(orderLine?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
        return {
          key: uuidv4(),
          asset1: orderLine?.product_sku_info?.assets?.[0]?.url || '',
          product_sku_name: orderLine?.product_sku_info?.product_sku_name,
          product_sku_info: orderLine?.product_sku_info,
          invoice_line_id: orderLine?.invoice_line_id,
          remarks: orderLine?.remarks?.replace(/<[^>]+>/g, '') || '',
          remarkRequired: !!orderLine?.remarks?.replace(/<[^>]+>/g, ''),
          invoice_qty: QUANTITY(orderLine?.quantity, orderLine?.uom_info[0]?.precision),
          returned_qty: QUANTITY(orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          quantity: QUANTITY(orderLine?.quantity - orderLine?.cn_quantity, orderLine?.uom_info[0]?.precision),
          unitPrice: 1,
          uomId: Number(orderLine?.uom_id),
          uom_info: orderLine?.uom_info[0],
          product_batches: getBatches(orderLine?.product_batches, orderLine?.quantity - orderLine?.cn_quantity),
          uomGroup: Number(orderLine?.group_id),
          taxId: Number(orderLine?.tax_id),
          taxInfo: orderLine?.tax_group_info,
          child_taxes: Helpers.computeTaxation(taxableValue, orderLine?.tax_group_info, user?.tenant_info?.state, selectedInvoiceForCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          hsn_code: orderLine?.hsn_code,
          discount: discountValue,
          unitDiscount: unitDiscountValue,
          lineDiscountType: orderLine?.is_discount_in_percent ? 'Percent' : 'Amount',
          showUnitDiscount: orderLine?.is_discount_in_percent && unitDiscountValue > 0,
          order_line_id: orderLine?.order_line_id,
          tenant_product_id: orderLine?.tenant_product_id,
          updateStock: true,
          isBundled: true,
          bundle_name: orderLine?.bundle_name,
          manufacturingDateFormat: orderLine?.product_sku_info?.manufacturing_date_format,
          expiryDateFormat: orderLine?.product_sku_info?.expiry_date_format,
          lineCustomFields: isReturnSlip ? CustomFieldHelpers.mergeCustomFields(props.cfV2DocReturnSlip?.data?.document_line_custom_fields, orderLine?.custom_fields)?.map((cf) => ({
            ...cf,
            fieldValue: cf?.fieldName === 'Quantity' ? Number(orderLine?.quantity) : cf?.fieldValue,
          })) || [] : [],
        };
      }));
      return {
        selectedCustomer: selectedInvoiceForCN?.customer_id,
        selectedCustomerInfo: selectedInvoiceForCN?.customer_info,
        gstNumber: selectedInvoiceForCN?.customer_info?.gst_number,
        // billingAddress: selectedInvoiceForCN?.billing_address_info,
        // shippingAddress: selectedInvoiceForCN?.shipping_address_info,
        billingAddress: isReturnSlip ? selectedInvoiceForCN?.shipping_address_info : selectedInvoiceForCN?.billing_address_info,
        shippingAddress: isReturnSlip ? selectedInvoiceForCN?.ship_from_address_info : selectedInvoiceForCN?.shipping_address_info,
        data: invoiceLines || [],
        paymentTerms: Number(selectedInvoiceForCN?.payment_terms?.[0]?.due_days),
        selectedInvoice: selectedInvoiceForCN?.invoice_id,
        termsAndConditions: selectedInvoiceForCN?.terms_and_conditions,
        fileList: selectedInvoiceForCN?.attachments,
        selectedInvoiceForCN,
        discountPercentage: selectedInvoiceForCN?.is_discount_in_percent ? selectedInvoiceForCN?.discount_percentage?.toFixed(DEFAULT_CUR_ROUND_OFF) : selectedInvoiceForCN?.discount_amount?.toFixed(DEFAULT_CUR_ROUND_OFF),
        isLineWiseDiscount: selectedInvoiceForCN?.is_line_wise_discount,
        discountType: selectedInvoiceForCN?.is_discount_in_percent ? 'Percent' : 'Amount',
        chargeData: selectedInvoiceForCN?.other_charges?.map((item) => ({
          ...item,
          chargeKey: uuidv4(),
          chargesTaxId: item?.tax_info?.tax_id,
          chargesSacCode: item?.charge_sac_code,
          chargesTaxInfo: item?.tax_info,
          chargesTax: item?.tax_info?.tax_value,
          tallyLedgerName: item?.ledger_name || null,
          chargesTaxData: {
            ...item?.tax_info,
            child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, selectedInvoiceForCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          },
        })) || [],
        taxType: selectedInvoiceForCN?.tcs_id || null,
        taxTypeId: selectedInvoiceForCN?.tcs_id ? selectedInvoiceForCN?.tcs_info?.tax_id : null,
        taxTypeInfo: selectedInvoiceForCN?.tcs_id ? selectedInvoiceForCN?.tcs_info : null,
        selectedCurrencyID: selectedInvoiceForCN?.org_currency_info?.org_currency_id,
        selectedCurrencyName: selectedInvoiceForCN?.org_currency_info,
        taxTypeName: selectedInvoiceForCN?.tcs_id ? selectedInvoiceForCN?.tcs_info?.tax_type_name : 'TCS',
        isAutomaticConversionRate: false,
        currencyConversionRate: selectedInvoiceForCN?.conversion_rate,
        selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedInvoiceForCN?.tenant_id)?.it_id,
        salesAccount: selectedInvoiceForCN?.tally_sales_account_name,
        selectedTenant: selectedInvoiceForCN?.tenant_id,
        cfCreditNoteDoc: CustomFieldHelpers.mergeCustomFields(props.cfV2DocReturnSlip?.data?.document_custom_fields || [], consumptionCustomFields || []),
        isUserReadyForCFReturnSlip: true,
        cfCreditNoteLine: CustomFieldHelpers.getCfStructure(props.cfV2DocReturnSlip?.data?.document_line_custom_fields, false),
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocReturnSlip?.data?.document_line_custom_fields, state?.visibleColumns),
        tenantDepartmentId: props.selectedInvoiceForCN?.tenant_department_id || props.user?.tenant_info?.default_store_id,
        accountManager: props.selectedInvoiceForCN?.account_manager_id || null,
      };
    }
    const hasCreditNotes = props.selectedCN?.data?.credit_notes?.length;
    const hasReturnSlipId = !!props?.match?.params?.returnSlipId;
    const hasCreditNoteId = !!props?.match?.params?.creditNoteId;
    const isReturnSlipReady = props.cfV2DocReturnSlip?.data?.success && hasReturnSlipId;
    const isCreditNoteReady = props.cfV2DocCreditNote?.data?.success && hasCreditNoteId;
    if (
      hasCreditNotes &&
      (isReturnSlip ? isReturnSlipReady : isCreditNoteReady) &&
      !state.isUserReady) {
      const selectedCN = props.selectedCN?.data?.credit_notes[0];
      // const selectedRS = props?.selectedCRS;
      props.getInvoice(user?.tenant_info?.org_id, selectedCN?.tenant_id || props.user?.tenant_info?.tenant_id, '', 1, 10, selectedCN?.customer_info?.customer_id, '', 'CONFIRMED', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, `${isReturnSlip ? 'CONSUMPTION_ORDER' : 'INVOICE'}`);
      // Already Used Custom Fields
      const oldCustomField = selectedCN?.custom_fields || [];

      const getBatches = (oldBaches, quantity) => {
        const batches = oldBaches;
        let remainingQty = new Decimal(quantity);

        for (let i = 0; i < batches?.length; i++) {
          const batchQty = new Decimal(batches?.[i]?.quantity || 0);
          if (remainingQty.greaterThan(0) && batchQty.greaterThan(0)) {
            batches[i].batch_in_use = true;
            remainingQty = remainingQty.minus(batchQty);
          } else {
            batches[i].quantity = 0;
            batches[i].batch_in_use = false;
            remainingQty = remainingQty.minus(batchQty);
          }
        }
        return batches.map((item) => ({
          ...item,
          returned_quantity: new Decimal(item?.quantity || 0).toNumber(),
        }));
      };

      const cnLines = selectedCN?.cn_lines?.map((invoiceLine) => {
        const discountValue = invoiceLine?.is_discount_in_percent ? Number.parseFloat(Number(invoiceLine?.line_discount_percentage).toFixed(DEFAULT_CUR_ROUND_OFF)) : Number.parseFloat(Number(invoiceLine?.line_discount_amount).toFixed(DEFAULT_CUR_ROUND_OFF));
        const taxableValue = invoiceLine?.is_discount_in_percent
          ? (invoiceLine.quantity * invoiceLine.unit_price) * (1 - discountValue / 100)
          : Math.max(invoiceLine.quantity * invoiceLine.unit_price - discountValue, 0);
        const unitDiscountValue = Number(discountValue / Number(invoiceLine?.quantity)).toPrecision(DEFAULT_CUR_ROUND_OFF);
        return {
          key: uuidv4(),
          asset1: invoiceLine?.product_sku_info?.assets?.[0]?.url || '',
          product_sku_info: invoiceLine?.product_sku_info,
          invoice_qty: QUANTITY(invoiceLine?.inv_quantity || invoiceLine?.quantity, invoiceLine?.uom_info[0]?.precision),
          returned_qty: QUANTITY(invoiceLine?.cn_quantity - invoiceLine?.quantity, invoiceLine?.uom_info[0]?.precision),
          product_sku_name: invoiceLine?.product_sku_info?.product_sku_name || invoiceLine?.product_sku_name,
          internal_sku_code: invoiceLine?.product_sku_info?.internal_sku_code,
          product_sku_id: invoiceLine?.product_sku_info?.product_sku_id,
          product_category_info: invoiceLine?.product_sku_info?.product_category_info,
          quantity: Number(invoiceLine?.quantity),
          returned_quantity: Number(invoiceLine?.returned_quantity),
          unitPrice: Number(invoiceLine?.unit_price),
          uomId: Number(invoiceLine?.uom_id),
          uomGroup: Number(invoiceLine?.group_id),
          taxId: Number(invoiceLine?.tax_id),
          taxInfo: invoiceLine?.tax_group_info,
          child_taxes: Helpers.computeTaxation(taxableValue, invoiceLine?.tax_group_info, props.user?.tenant_info?.state, selectedCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          remarks: (invoiceLine?.remarks || '')?.replace(/<[^>]+>/g, '') || '',
          remarkRequired: !!invoiceLine?.remarks?.replace(/<[^>]+>/g, ''),
          product_batches: getBatches(invoiceLine?.product_batches, Number(invoiceLine?.returned_quantity || invoiceLine?.quantity)),
          discount: discountValue,
          unitDiscount: unitDiscountValue,
          lineDiscountType: invoiceLine?.is_discount_in_percent ? 'Percent' : 'Amount',
          showUnitDiscount: !invoiceLine?.is_discount_in_percent && unitDiscountValue > 0,
          order_line_id: invoiceLine?.order_line_id,
          tenant_product_id: invoiceLine?.tenant_product_id,
          hsn_code: invoiceLine?.hsn_code,
          uom_info: invoiceLine?.uom_info[0],
          cnLineId: invoiceLine?.cn_line_id,
          updateStock: invoiceLine?.update_stock_qty,
          lineCustomFields: isReturnSlip ? CustomFieldHelpers.mergeCustomFields(props.cfV2DocReturnSlip?.data?.document_line_custom_fields, invoiceLine?.custom_fields)?.map((cf) => ({
            ...cf,
            fieldValue: cf?.fieldName === 'Quantity' ? Number(invoiceLine?.quantity) : cf?.fieldValue,
          })) || [] : [],
        };
      });
      return {
        ...state,
        isUserReady: true,
        creditNoteWithoutLines: selectedCN?.without_lines,
        tenantDepartmentId: selectedCN?.tenant_department_id,
        isAdhocCnLiens: !selectedCN?.invoice_id,
        creditNoteWithReturnedQyt: selectedCN?.bypass_inventory_change,
        selectedCustomer: selectedCN?.customer_id,
        selectedCustomerInfo: selectedCN?.customer_info,
        gstNumber: selectedCN?.customer_info?.gst_number,
        cnDate: dayjs(selectedCN?.cn_date),
        billingAddress: isReturnSlip ? selectedCN?.ship_from_address_info : selectedCN?.billing_address_info,
        shippingAddress: selectedCN?.shipping_address_info,
        data: cnLines,
        reasonForCancellation: selectedCN?.reason,
        termsAndConditions: selectedCN?.terms_and_conditions,
        cnNumber: selectedCN?.credit_note_number,
        selectedInvoice: selectedCN?.invoice_id,
        fileList: selectedCN?.attachments,
        selectedCN,
        cfCreditNoteDoc: CustomFieldHelpers.mergeCustomFields(isReturnSlip ? props.cfV2DocReturnSlip?.data?.document_custom_fields : props.cfV2DocCreditNote?.data?.document_custom_fields, oldCustomField), //check here
        discountPercentage: selectedCN?.is_discount_in_percent ? selectedCN?.discount_percentage?.toFixed(DEFAULT_CUR_ROUND_OFF) : selectedCN?.discount_amount?.toFixed(DEFAULT_CUR_ROUND_OFF),
        isLineWiseDiscount: selectedCN?.is_line_wise_discount,
        discountType: selectedCN?.is_discount_in_percent ? 'Percent' : 'Amount',
        chargeData: selectedCN?.other_charges?.map((item) => ({
          ...item,
          chargeKey: uuidv4(),
          chargesTaxId: item?.tax_info?.tax_id,
          chargesSacCode: item?.charge_sac_code,
          chargesTaxInfo: item?.tax_info,
          chargesTax: item?.tax_info?.tax_value,
          tallyLedgerName: item?.ledger_name || null,
          chargesTaxData: {
            ...item?.tax_info,
            child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, selectedCN?.billing_address_info?.state)?.tax_info?.child_taxes,
          },
        })) || [],
        taxType: selectedCN?.tcs_id || null,
        taxTypeId: selectedCN?.tcs_id ? selectedCN?.tcs_info?.tax_id : null,
        taxTypeInfo: selectedCN?.tcs_id ? selectedCN?.tcs_info : null,
        selectedCurrencyID: selectedCN?.org_currency_info?.org_currency_id,
        selectedCurrencyName: selectedCN?.org_currency_info,
        taxTypeName: selectedCN?.tcs_id ? selectedCN?.tcs_info?.tax_type_name : 'TCS',
        isAutomaticConversionRate: false,
        currencyConversionRate: selectedCN?.conversion_rate,
        selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === selectedCN?.tenant_id)?.it_id,
        narration: selectedCN?.narration || '',
        selectedTenant: selectedCN?.tenant_id,
        salesAccount: selectedCN?.tally_credit_note_account_name,
        cfCreditNoteLine: CustomFieldHelpers.getCfStructure(props.cfV2DocReturnSlip?.data?.document_line_custom_fields, false),
        visibleColumns: CustomFieldHelpers.updateVisibleColumns(props.cfV2DocReturnSlip?.data?.document_line_custom_fields, state?.visibleColumns),
        accountManager: selectedCN?.account_manager_id || null,
      };
    }
    return state;
  }

  handleChangeTextArea = (html) => {
    if (this.state.termsAndConditions !== html) {
      this.setState({ termsAndConditions: html });
    }
  };

  handleChangeNarration = (value) => {
    if (this.state.narration !== value) {
      this.setState({ narration: value });
    }
  };

  handleDelete = (key) => {
    const { data, isLineWiseDiscount, discountPercentage } = this.state;
    if (data?.length === 1) {
      this.setState({
        data: [],
      });
    } else {
      const copyData = data.filter((item) => item.key !== key);
      this.setState({ data: copyData }, () => {
        if (!isLineWiseDiscount) {
          this.handleDiscountPercentageChange(discountPercentage);
        }
      });
    }
  };

  handleDeleteCharge = (chargeKey) => {
    const { chargeData } = this.state;
    const copyChargeData = chargeData.filter((item) => item.chargeKey !== chargeKey);
    this.setState({ chargeData: copyChargeData });
  };

  getLineTotals() {
    const {
      data, chargeData, taxTypeInfo, taxTypeName,
    } = this.state;
    let totalAmount = 0;
    let totalDiscount = 0;
    let totalBase = 0;
    let totalTcs = 0;
    let totalTax = 0;
    let totalOtherCharge = 0;
    let cnTotal = 0;
    let totalOtherCharges = 0;

    // Taxes Bifurcation
    const totalTaxValue = Helpers.groupAndSumByTaxName(data?.map((item) => item?.child_taxes)?.flat())?.reduce((acc, curr) => acc + curr?.tax_amount, 0);

    for (let i = 0; i < chargeData?.length; i++) {
      let currentOtherCharge = 0;
      let chargesTaxAmountLinesWise = 0;
      if (chargeData[i]?.charge_amount) {
        chargesTaxAmountLinesWise = Helpers.groupAndSumByTaxName([...chargeData[i]?.chargesTaxData?.child_taxes?.flat()])?.reduce((acc, curr) => acc + curr?.tax_amount, 0);
        currentOtherCharge = chargeData?.[i]?.chargesTax ? chargeData?.[i]?.charge_amount + (chargesTaxAmountLinesWise) : chargeData?.[i]?.charge_amount;
        if (chargeData?.[i]?.chargesTax) {
          totalOtherCharges += Number(chargeData?.[i]?.charge_amount);
        }
      }
      totalOtherCharge += currentOtherCharge;
    }

    for (let i = 0; i < data?.length; i++) {
      let currentAmount = 0;
      let currentDiscount = 0;
      let currentBase = 0;
      let currentTax = 0;
      let currentCN = 0;
      if (data[i].quantity) {
        const discountValue = Number(data[i].discount) || 0;
        currentAmount += (data[i].quantity * data[i].unitPrice);
        currentDiscount += data[i].discount ? (data?.[i]?.lineDiscountType == 'Percent' ? ((data[i].quantity * data[i].unitPrice) * (data[i].discount / 100)) : discountValue) : 0;
        currentBase += data?.[i]?.lineDiscountType == 'Percent' ? (data[i].quantity * data[i].unitPrice) * (data[i].discount ? Number(100 - data[i].discount) / 100 : 1) : (data?.[i]?.quantity * data?.[i]?.unitPrice) - discountValue;
      }

      if (data[i].taxId && data[i].taxInfo?.tax_value) currentTax += currentBase * (data?.[i]?.taxId ? data[i].taxInfo?.tax_value / 100 : 0);
      if (currentBase) currentCN = currentBase;
      totalAmount += currentAmount;
      totalDiscount += currentDiscount;
      totalBase += currentBase;
      totalTax += currentTax;
      cnTotal += currentCN + totalTcs;
    }

    // Subtract TCS from currentBase
    if (taxTypeInfo && taxTypeName === 'TCS') {
      const tcsRate = taxTypeInfo?.tax_value / 100;

      const tcsAmount = (totalBase + totalTaxValue + Number(totalOtherCharge)) * tcsRate;
      totalTcs = tcsAmount;
    }

    cnTotal += totalOtherCharge + totalTaxValue + totalTcs;
    totalBase += totalOtherCharges;

    return {
      totalAmount,
      totalDiscount,
      totalBase,
      totalTax,
      totalTcs,
      cnTotal,
    };
  }

  handleProductChange = (tenantSku, key) => {
    const { data, billingAddress, isLineWiseDiscount, discountPercentage, discountType, } = this.state;
    const { getTenantSkuOffer, user } = this.props;
    getTenantSkuOffer(tenantSku?.tenant_product_id, null, (sellerOffer) => {
      const copyData = JSON.parse(JSON.stringify(data));
      for (let i = 0; i < copyData?.length; i++) {
        if (copyData[i].key === key) {
          const copyDataItem = copyData[i];
          copyDataItem.asset1 = tenantSku?.product_info?.assets?.[0]?.url || '';
          copyDataItem.product_sku_name = tenantSku?.product_info.product_sku_name;
          copyDataItem.product_category_info = tenantSku?.product_category_info;
          copyDataItem.hsn_code = tenantSku?.product_info?.hsn_code || '';
          copyDataItem.product_sku_id = tenantSku?.product_info.product_sku_id;
          copyDataItem.internal_sku_code = tenantSku?.product_info.internal_sku_code;
          copyDataItem.product_sku_info = tenantSku?.product_info;
          copyDataItem.tenant_product_id = `${tenantSku?.tenant_product_id}`;
          copyDataItem.quantity = 1;
          copyDataItem.unitPrice = 1;
          copyDataItem.offerPrice = 1;
          copyDataItem.uomId = tenantSku?.uom_id;
          copyDataItem.uom_info = tenantSku?.uom_info;
          // copyDataItem.product_batches = tenantSku?.product_batches; make batch_in_use true in first batch
          copyDataItem.product_batches = tenantSku?.product_batches?.map((batch, index) => ({
            ...batch,
            batch_in_use: index === 0,
            quantity: index === 0 ? 1 : 0,
          }));
          copyDataItem.uomGroup = tenantSku?.group_id;
          copyDataItem.taxId = tenantSku?.tax_info?.tax_id;
          copyDataItem.taxInfo = tenantSku?.tax_info;
          copyDataItem.child_taxes = Helpers.computeTaxation(1, tenantSku?.tax_info, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
          copyDataItem.discount = isLineWiseDiscount ? discountPercentage : 0;
          copyDataItem.manufacturingDateFormat = tenantSku?.product_info?.manufacturing_date_format;
          copyDataItem.expiryDateFormat = tenantSku?.product_info?.xexpiry_date_format;
          copyDataItem.lineDiscountType = !isLineWiseDiscount ? discountType : 'Percent';
          copyData[i] = copyDataItem;
        }
      }
      this.setState({ data: copyData }, () => {
        if (!isLineWiseDiscount) {
          this.handleDiscountPercentageChange(discountPercentage);
        }
      });
    });
  };

  handleProductChangeValue = (value, key) => {
    const { data } = this.state;
    const copyData = JSON.parse(JSON.stringify(data));
    for (let i = 0; i < copyData?.length; i++) {
      if (copyData[i].key === key) {
        const copyDataItem = copyData[i];
        copyDataItem.product_sku_name = value;
        copyData[i] = copyDataItem;
      }
    }
    this.setState({ data: copyData });
  };

  addNewRow() {
    const { data, isAdhocCnLiens, cfCreditNoteLine } = this.state;
    const { isReturnSlip } = this.props;
    const copyData = JSON.parse(JSON.stringify(data));
    copyData.push({
      key: uuidv4(),
      child_taxes: [{
        tax_amount: 0,
        tax_type_name: '',
      }],
      lineCustomFields: isReturnSlip ? cfCreditNoteLine || [] : [],
    });
    this.setState({ data: copyData, isAdhocCnLiens: true });
  }

  addNewChargesRow() {
    const { chargeData } = this.state;
    const copychargeData = JSON.parse(JSON.stringify(chargeData));
    copychargeData.push({
      chargeKey: uuidv4(),
      charge_name: '',
      charge_amount: 0,
    });
    this.setState({ chargeData: copychargeData });
  }

  isDataValid() {
    const { data, isAdhocCnLiens, creditNoteWithReturnedQyt, isReturnSlip } = this.state;
    const { cnWithoutDeductingStock, selectedInvoice } = this.props;
    let isDataValid = true;

    for (let i = 0; i < data?.length; i++) {
      const product = data[i];
      const isStorable = product?.product_sku_info?.product_type === 'STORABLE';
      const batchQuantity = Helpers.getValueTotalInObject(product?.product_batches?.filter((item) => item?.batch_in_use), 'quantity');
      const basicValidation = Number(product.quantity) <= 0
        || !product.product_sku_name
        || Number(product.unitPrice) <= 0
        || !JSON.stringify(product.taxId)
        || (Number(product.quantity) > Number(product.invoice_qty) - Number(product.returned_qty));
      const withReturnedQtyValidation = creditNoteWithReturnedQyt ? (Number(product.quantity) < Number(product.returned_quantity)) : false;
      const withReturnedQtyValidationSecond = creditNoteWithReturnedQyt ? (!['SERVICE', 'NON_STORABLE'].includes(product?.product_sku_info?.product_type) && product?.product_batches?.length === 0 && Number(product.returned_quantity) > 0) : (!['SERVICE', 'NON_STORABLE'].includes(product?.product_sku_info?.product_type) && product?.product_batches?.length === 0);

      if (cnWithoutDeductingStock) {
        if (basicValidation) {
          isDataValid = false;
          break;
        }
      } else if (isStorable && selectedInvoice) {
        const storableValidation = !batchQuantity || Number(product.quantity) > batchQuantity;
        if (basicValidation || storableValidation) {
          isDataValid = false;
          break;
        }
      } else if (basicValidation && !isAdhocCnLiens && !isReturnSlip) {
        isDataValid = false;
        break;
      } else if (withReturnedQtyValidation || withReturnedQtyValidationSecond && !isAdhocCnLiens) {
        isDataValid = false;
        break;
      } else if (isReturnSlip && Number(product.quantity) <= 0) {
        isDataValid = false;
        break;
      }
    }
    return isDataValid;
  }

  isDataValid2() {
    const { chargeData } = this.state;
    let isDataValid = true;
    if (chargeData?.length) {
      for (let i = 0; i < chargeData?.length; i++) {
        if (!chargeData[i].charge_name || !chargeData[i].charge_amount) {
          isDataValid = false;
        }
      }
    }
    return isDataValid;
  }

  isCfValid = (customFields) => !customFields?.filter((customField) => customField.visible && customField.isActive && customField.isRequired && (customField?.fieldName != 'Rate') && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length;

  isLineCfValid = () => {
    const { data } = this.state;
    let isDataValid = true;
    for (let i = 0; i < data?.length; i++) {
      if (!this.isCfValid(data[i]?.lineCustomFields)) {
        isDataValid = false;
        break;
      }
    }
    return isDataValid;
  };

  createCN(withApproval, applyCreditNote) {
    this.setState({ withApproval });
    const {
      createCN, history, user, match, updateCN, callback, selectedInvoiceForCN, cnWithoutDeductingStock, invoiceType, selectedRS,
    } = this.props;
    const {
      cnDate, data, shippingAddress, reasonForCancellation, billingAddress, selectedCustomer, selectedCN, selectedCurrencyName, cfV2DocCreditNote, creditNoteWithoutLines, creditNoteWithReturnedQyt, docSeqId, cnNumber, initialCnNumber,
      selectedInvoice, termsAndConditions, chargeData, selectedCustomerInfo, gstNumber, fileList, cfCreditNoteDoc, discountPercentage, isLineWiseDiscount, taxTypeInfo, taxType, tenantDepartmentId, isAutomaticConversionRate, currencyConversionRate, narration, discountType, salesAccount, isReturnSlip, accountManager,
    } = this.state;
    const cnLines = [];
    this.setState({ formSubmitted: true, data: JSON.parse(JSON.stringify(data)) });
    if (this.isDataValid() && this.isDataValid2() && selectedCustomer && (isReturnSlip ? true : (shippingAddress && billingAddress)) && cnDate && (gstNumber ? String(gstNumber)?.length === 15 : true) && reasonForCancellation && this.isLineCfValid()) {
      if (match?.params?.creditNoteId || match?.params?.returnSlipId) {
        for (let i = 0; i < data?.length; i++) {
          let lineDiscount = data?.[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data?.[i]?.quantity) * parseFloat(data?.[i]?.unitPrice))) * 100);

          const cnLine = {
            cn_line_id: data[i]?.cnLineId || null,
            tenant_product_id: data[i].tenant_product_id || null,
            product_sku_name: data[i].product_sku_name || null,
            remarks: data[i]?.remarks || null,
            quantity: Number(data[i].quantity) || 0,
            returned_quantity: (cnWithoutDeductingStock || creditNoteWithoutLines) ? 0 : (creditNoteWithReturnedQyt ? Number(data[i].returned_quantity) : Number(data[i].quantity)),
            uom_id: data[i]?.uomId || null,
            product_batches: (cnWithoutDeductingStock || creditNoteWithoutLines) ? [] : data[i]?.product_batches?.map((batch) => ({
              ...batch,
              quantity: creditNoteWithReturnedQyt ? Number(batch.returned_quantity) : Number(batch.quantity),
              manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, data?.[i]?.manufacturingDateFormat) : null,
              expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, data?.[i]?.expiryDateFormat) : null,
            })),
            uom_info: data[i]?.uom_info ? [data[i]?.uom_info] : null,
            update_stock_qty: data[i]?.updateStock,
            custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          };
          if (!isReturnSlip) {
            cnLine.unit_price = parseFloat(data[i].unitPrice) || 0; //not needed for rs
            cnLine.offer_price = parseFloat(data[i].unitPrice) || 0; //not needed for rs
            cnLine.tax_id = data[i]?.taxId || null; //not needed for rs
            cnLine.tax_group_info = data[i]?.taxInfo || null; //not needed for rs
            cnLine.hsn_code = data[i]?.hsn_code || null;
            cnLine.line_discount_percentage = lineDiscount; //not needed for rs
            cnLine.is_discount_in_percent = data[i]?.lineDiscountType === 'Percent'; //not needed for rs
            cnLine.invoice_line_id = data[i]?.invoice_line_id || null; // not needed for rs upadte
          }
          cnLines.push(cnLine);
        }
        const payload = [{
          cn_id: selectedCN?.cn_id,
          customer_id: selectedCustomer,
          customer_info: { ...selectedCustomerInfo, gst_number: gstNumber },
          tenant_id: selectedCN?.tenant_id || user?.tenant_info?.tenant_id,
          cn_date: dayjs(cnDate).format('YYYY/MM/DD'),
          reason: reasonForCancellation,
          cn_lines: cnLines,
          tenant_info: selectedCN?.tenant_info,
          invoice_id: selectedInvoice,
          terms_and_conditions: global.isBlankString(termsAndConditions) ? '' : termsAndConditions,
          status: withApproval ? 'CONFIRMED' : 'DRAFT',
          attachments: fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url,
            type: attachment.type,
            name: attachment.name,
            uid: attachment.uid,
          })) || [],
          custom_fields: CustomFieldHelpers.postCfStructure(cfCreditNoteDoc),
          bypass_inventory_change: creditNoteWithReturnedQyt || !!cnWithoutDeductingStock,
          doc_type: isReturnSlip ? 'RETURN_SLIP' : 'CREDIT_NOTE',
          tenant_department_id: !selectedInvoice ? tenantDepartmentId : null,
          credit_note_number: initialCnNumber?.toLowerCase()?.trim() === cnNumber?.toLowerCase()?.trim() ? null : cnNumber,
          account_manager_id: accountManager || null,
        }];

        if (isReturnSlip) {
          Object.assign(payload[0], {
            shipping_address: shippingAddress?.address_id,
            shipping_address_info: shippingAddress,
            ship_from_address_id: billingAddress?.address_id,
            ship_from_address_info: billingAddress,
          });
        }

        if (!isReturnSlip) {
          Object.assign(payload[0], {
            shipping_address: shippingAddress?.address_id,
            shipping_address_info: shippingAddress,
            billing_address: billingAddress?.address_id,
            billing_address_info: billingAddress,
            without_lines: creditNoteWithoutLines || false,
            apply_credit_note: !!applyCreditNote,
            cnWithoutDeductingStock: null,
            apply_round_off: !cnWithoutDeductingStock,
            narration: narration || '',
            tally_credit_note_account_name: salesAccount,
            other_charges: chargeData?.map((charge) => ({
              charge_name: charge?.charge_name,
              charge_amount: charge?.charge_amount,
              charge_type: '',
              charge_sac_code: charge?.chargesSacCode || null,
              tax_info: charge?.chargesTaxInfo || null,
              ledger_name: charge?.tallyLedgerName || null,
            })) || [],
            discount_percentage:
              discountType === 'Percent'
                ? discountPercentage
                : (discountPercentage / this.getLineTotals()?.totalAmount || 0) * 100,
            is_line_wise_discount: isLineWiseDiscount,
            is_discount_in_percent: discountType === 'Percent',
            tcs_id: taxTypeInfo ? taxType : null,
            tcs_info: taxTypeInfo
              ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              }
              : null,
            conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
            org_currency_id: selectedCurrencyName?.org_currency_id,
            shopify_order_id: invoiceType === 'shopify' ? cnWithoutDeductingStock : null,
            unicommerce_shipping_package_code: invoiceType === 'unicommerce' ? cnWithoutDeductingStock : null,
          });
        }
        updateCN(payload, () => {
          isReturnSlip ? history.push(`/sales/return-slip/view/${selectedCN?.cn_id}`) : history.push(`/sales/credit-note/view/${selectedCN?.cn_id}`);
        });
      } else {
        for (let i = 0; i < data?.length; i++) {
          let lineDiscount = data?.[i]?.lineDiscountType === 'Percent' ? Number(data?.[i]?.discount || 0) : ((Number(data?.[i]?.discount || 0) / (Number(data?.[i]?.quantity) * parseFloat(data?.[i]?.unitPrice))) * 100);

          const cnLine = {
            invoice_line_id: data[i]?.invoice_line_id || null,
            tenant_product_id: data[i].tenant_product_id || null,
            product_sku_name: data[i].product_sku_name || null,
            remarks: data[i]?.remarks || null,
            quantity: Number(data[i].quantity) || 0,
            returned_quantity: (cnWithoutDeductingStock || creditNoteWithoutLines) ? 0 : (creditNoteWithReturnedQyt ? Number(data[i].returned_quantity) : Number(data[i].quantity)),
            uom_id: data[i]?.uomId || null,
            product_batches: (cnWithoutDeductingStock || creditNoteWithoutLines) ? [] : data[i]?.product_batches?.map((batch) => ({
              ...batch,
              quantity: creditNoteWithReturnedQyt ? Number(batch.returned_quantity) : Number(batch.quantity),
              manufacturing_date: batch?.manufacturing_date ? FormHelpers.dateFormatter(batch?.manufacturing_date, data?.[i]?.manufacturingDateFormat) : null,
              expiry_date: batch?.expiry_date ? FormHelpers.dateFormatter(batch?.expiry_date, data?.[i]?.expiryDateFormat) : null,
            })),
            update_stock_qty: data[i]?.updateStock,
            uom_info: data[i]?.uom_info ? [data[i]?.uom_info] : null,
            custom_fields: CustomFieldHelpers.postCfStructure(data[i]?.lineCustomFields),
          };
          if (!isReturnSlip) {
            cnLine.unit_price = parseFloat(data[i].unitPrice) || 0; // not needed for RS
            cnLine.offer_price = parseFloat(data[i].unitPrice) || 0; // not needed for RS
            cnLine.tax_id = data[i]?.taxId || null; // not needed for RS
            cnLine.tax_group_info = data[i]?.taxInfo || null; // not needed for RS
            cnLine.hsn_code = data[i]?.hsn_code || null;
            cnLine.line_discount_percentage = lineDiscount; // not needed for RS
            cnLine.is_discount_in_percent = data[i]?.lineDiscountType === 'Percent'; // not needed for RS
          }
          cnLines.push(cnLine);
        }

        const payload = [{
          customer_id: selectedCustomer,
          tenant_department_id: (!selectedInvoice) ? tenantDepartmentId : selectedInvoiceForCN?.tenant_department_id || null,
          customer_info: { ...selectedCustomerInfo, gst_number: gstNumber },
          tenant_id: selectedInvoiceForCN?.tenant_id || user?.tenant_info?.tenant_id,
          cn_date: dayjs(cnDate).format('YYYY/MM/DD'),
          reason: reasonForCancellation,
          cn_lines: cnLines,
          tenant_info: selectedInvoiceForCN?.tenant_id ? selectedInvoiceForCN?.tenant_info
            : {
              tenant_name: user?.tenant_info?.tenant_name,
              legal_name: user?.tenant_info?.legal_name,
              gst_number: user?.tenant_info?.gst_number,
            },
          attachments: fileList?.map((attachment) => ({
            url: attachment?.response?.response?.location || attachment?.url,
            type: attachment.type,
            name: attachment.name,
            uid: attachment.uid,
          })) || [],
          terms_and_conditions: global.isBlankString(termsAndConditions) ? '' : termsAndConditions,
          status: withApproval ? 'CONFIRMED' : 'DRAFT',
          invoice_id: selectedInvoice,
          custom_fields: CustomFieldHelpers.postCfStructure(cfCreditNoteDoc),
          doc_type: isReturnSlip ? 'RETURN_SLIP' : 'CREDIT_NOTE',
          source: isReturnSlip ? 'CONSUMPTION_ORDER' : (selectedInvoice ? 'INVOICE' : 'ADHOC'),
          credit_note_number: initialCnNumber?.toLowerCase()?.trim() === cnNumber?.toLowerCase()?.trim() ? null : cnNumber,
          seq_id: docSeqId || null,
          account_manager_id: accountManager || null,
        }];

        if (isReturnSlip) {
          Object.assign(payload[0], {
            shipping_address: shippingAddress?.address_id,
            shipping_address_info: shippingAddress,
            ship_from_address_id: billingAddress?.address_id,
            ship_from_address_info: billingAddress,
          });
        }

        if (!isReturnSlip) {
          Object.assign(payload[0], {
            shipping_address: shippingAddress?.address_id,
            shipping_address_info: shippingAddress,
            billing_address: billingAddress?.address_id,
            billing_address_info: billingAddress,
            without_lines: creditNoteWithoutLines || false,
            apply_credit_note: !!applyCreditNote,
            cnWithoutDeductingStock: null,
            apply_round_off: !cnWithoutDeductingStock,
            narration: narration || '',
            tally_credit_note_account_name: salesAccount,
            other_charges: chargeData?.map((charge) => ({
              charge_name: charge?.charge_name,
              charge_amount: charge?.charge_amount,
              charge_type: '',
              charge_sac_code: charge?.chargesSacCode || null,
              tax_info: charge?.chargesTaxInfo || null,
              ledger_name: charge?.tallyLedgerName || null,
            })) || [],
            discount_percentage: discountType === 'Percent'
              ? discountPercentage
              : (discountPercentage / this.getLineTotals()?.totalAmount || 0) * 100,
            is_line_wise_discount: isLineWiseDiscount,
            is_discount_in_percent: discountType === 'Percent',
            tcs_id: taxTypeInfo ? taxType : null,
            tcs_info: taxTypeInfo
              ? {
                tax_id: taxTypeInfo?.tax_id,
                tax_name: taxTypeInfo?.tax_name,
                tax_value: taxTypeInfo?.tax_value,
                tax_type_name: taxTypeInfo?.tax_type_name,
              }
              : null,
            conversion_rate: currencyConversionRate || selectedCurrencyName?.conversion_rate,
            org_currency_id: selectedCurrencyName?.org_currency_id,
            bypass_inventory_change: creditNoteWithReturnedQyt || !!cnWithoutDeductingStock,
            shopify_order_id: invoiceType === 'shopify' ? cnWithoutDeductingStock : null,
            unicommerce_shipping_package_code: invoiceType === 'unicommerce' ? cnWithoutDeductingStock : null,
          });
        }

        createCN(payload, (cn) => {
          if (selectedInvoiceForCN) {
            callback();
          } else {
            isReturnSlip ? history.push(`/sales/return-slip/view/${cn?.cn_id}`) : history.push(`/sales/credit-note/view/${cn?.cn_id}`);
          }
        });
      }
    } else {
      notification.error({
        message: 'Please fill all the required fields',
        placement: 'top',
        duration: 3,
      });
    }
  }

  customInputChange(fieldValue, cfId) {
    const { cfCreditNoteDoc } = this.state;
    const newCustomField = cfCreditNoteDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),

          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfCreditNoteDoc: newCustomField,
    });
  }

  customLineInputChange = (fieldValue, key) => {
    const { data } = this.state;
    const copyData = structuredClone(data);
    for (const copyDataItem of copyData) {
      if (copyDataItem?.key === key) {
        copyDataItem.lineCustomFields = fieldValue;
        const qtyCf = fieldValue?.find((j) => j?.fieldName === 'Quantity');
        copyDataItem.quantity = qtyCf?.fieldValue > 0 ? Number.parseFloat(Number(qtyCf?.fieldValue).toFixed(DEFAULT_CUR_ROUND_OFF)) : '';
      }
    }
    this.setState({ data: copyData });
  };

  toggleBatch(record, adjustmentRow) {
    const { data, selectedInvoice } = this.state;

    for (let i = 0; i < data?.length; i++) {
      if (data[i]?.key === adjustmentRow?.key) {
        const rowBatches = data[i]?.product_batches;

        // If selectedInvoice is false or not present, unselect other checkboxes
        if (!selectedInvoice) {
          for (let j = 0; j < rowBatches?.length; j++) {
            if (rowBatches[j]?.batch_id !== record?.batch_id) {
              rowBatches[j].batch_in_use = false; // Unselect other checkboxes
              data[i].quantity -= rowBatches[j].quantity; // Adjust quantity
              data[i].returned_quantity -= rowBatches[j].returned_quantity; // Adjust quantity
              rowBatches[j].quantity = 0; // Reset quantity to 0
              rowBatches[j].returned_quantity = 0; // Reset quantity to 0
            }
          }
        }

        // Toggle the selected batch
        for (let j = 0; j < rowBatches?.length; j++) {
          if (rowBatches[j]?.batch_id === record?.batch_id) {
            rowBatches[j].batch_in_use = !rowBatches[j]?.batch_in_use;
            data[i].quantity -= rowBatches[j].quantity;
            data[i].returned_quantity -= rowBatches[j].returned_quantity;
            rowBatches[j].quantity = 0; // Reset quantity for deselected batch
            rowBatches[j].returned_quantity = 0; // Reset quantity for deselected batch
          }
        }

        // If a batch is selected, set its quantity
        for (let j = 0; j < rowBatches?.length; j++) {
          if (rowBatches[j]?.batch_in_use) {
            rowBatches[j].quantity = record.quantity; // Set the quantity for the selected batch
            rowBatches[j].returned_quantity = record.returned_quantity; // Set the quantity for the selected batch
            data[i].quantity += rowBatches[j].quantity; // Adjust the total quantity accordingly
            data[i].returned_quantity += rowBatches[j].returned_quantity; // Adjust the total quantity accordingly
          }
        }

        break; // Exit the loop once the correct row is found and processed
      }
    }

    this.setState({ data });
  }

  // getBatches(oldBaches, quantity) {
  //   const batches = oldBaches;
  //   let remainingQty = quantity;
  //   for (let i = 0; i < batches?.length; i++) {
  //     batches[i].quantity = 0;
  //   }
  //   for (let i = 0; i < batches?.length; i++) {
  //     if (remainingQty > 0 && (batches?.[i]?.consumed_qty - batches?.[i]?.cn_gen_qty) > 0) {
  //       batches[i].quantity = (batches?.[i]?.consumed_qty - batches?.[i]?.cn_gen_qty) > remainingQty ? remainingQty : (batches?.[i]?.consumed_qty - batches?.[i]?.cn_gen_qty);
  //       batches[i].batch_in_use = true;
  //       remainingQty -= batches[i]?.quantity;
  //     } else {
  //       batches[i].batch_in_use = false;
  //       remainingQty -= batches[i]?.quantity;
  //     }
  //   }
  //   return batches;
  // }
  getBatches = (oldBaches, quantity) => {
    const batches = oldBaches;
    let remainingQty = new Decimal(quantity);
    for (let i = 0; i < batches?.length; i++) {
      batches[i].quantity = 0;
    }

    for (let i = 0; i < batches?.length; i++) {
      const consumedQty = new Decimal(batches?.[i]?.consumed_qty || 0);
      const cnGenQty = new Decimal(batches?.[i]?.cn_gen_qty || 0);
      const diffQty = consumedQty.minus(cnGenQty);

      if (remainingQty.greaterThan(0) && diffQty.greaterThan(0)) {
        const allocatable = Decimal.min(diffQty, remainingQty);
        batches[i].quantity = allocatable.toNumber();
        batches[i].batch_in_use = true;
        remainingQty = remainingQty.minus(allocatable);
      } else {
        batches[i].batch_in_use = false;
      }
    }
    return batches;
  };

  showNewCustomerInSelect = (value) => {
    this.setState({
      selectedCustomer: value?.customer_info?.customer_id,
      billingAddress: value?.customer_info?.billing_address_details,
      shippingAddress: value?.customer_info?.shipping_address_details,
      gstNumber: value?.customer_info?.gst_number,
      selectedCustomerInfo: value?.customer_info,
      data: [
        {
          key: uuidv4(), asset1: '', product_sku_name: '', quantity: '', unitPrice: '', lot: 0, taxId: 0, discount: 0,
        },
      ],
      selectedInvoice: null,
    });
  };

  handleDiscountPercentageChange = (value) => {

    const { discountType, data, billingAddress, } = this.state;
    const { user } = this.props;

    const copyData = JSON.parse(JSON.stringify(data));
    const totalValue = copyData?.reduce((acc, cur) => acc + (cur?.quantity * cur?.unitPrice), 0);

    copyData?.map((item) => {

      const discountValue = discountType === 'Percent' ? parseFloat(value) : ((item?.quantity * item?.unitPrice) / parseFloat(totalValue)) * parseFloat(value);
      const taxableValue = discountType === 'Percent' ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100) : Math.max(item?.quantity * item?.unitPrice - discountValue, 0);

      item.discount = discountValue;
      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
    });

    this.setState({
      data: copyData,
      discountPercentage: value,
    });
  };

  render() {
    const {
      createCNLoading, getCNByIdLoading, updateCNLoading, updateCNStatusLoading, getInvoice, user, isQuickView,
      invoices, getCustomers, MONEY, CurrenciesResults, getCurrenciesLoading, cnWithoutDeductingStock, invoiceType, selectedCN, isAdhocCnLiens, priceMasking, getTallyConnectionsLoading, cfV2DocCreditNote, cfV2DocReturnSlip, selectedInvoiceForCN,
    } = this.props;
    const {
      data, formSubmitted, shippingAddress, termsAndConditions, creditNoteWithoutLines, creditNoteWithReturnedQyt,
      withApproval, reasonForCancellation, cnDate, showAddressDrawer, selectedAddressType, billingAddress, docSeqId, initialCnNumber,
      selectedCustomer, selectedInvoice, fileList, gstNumber, cnNumber, cfCreditNoteDoc, chargeData, isLineWiseDiscount, discountPercentage, showAddCustomer,
      showNewCustomerModal, taxType, taxTypeId, taxTypeName, selectedCurrencyID, selectedCurrencyName, tenantDepartmentId, isAutomaticConversionRate, currencyConversionRate, narration, selectedTenantTallyIntegrationId, discountType, selectedTenant, salesAccount, isReturnSlip, visibleColumns, cfCreditNoteLine, accountManager,
    } = this.state;
    const creditNote = user?.tenant_info?.sales_config?.sub_modules?.credit_note?.is_active;
    const cnUpdateCase = selectedCN?.data?.credit_notes?.length;
    const accountingGSTTransactionAsPerMaster = user?.tenant_info?.sales_config?.sub_modules?.customer?.settings?.gst_details_in_transaction === 'AS_PER_MASTER';

    const { isDataMaskingPolicyEnable, isHideCostPrice, isHideSellingPrice } = priceMasking;

    const splitChargesData = (charge) => {
      const chargeWithTaxName = charge?.filter((line) => line?.chargesTaxInfo?.tax_id) || [];
      const chargeWithoutTaxName = charge?.filter((line) => !line?.chargesTaxInfo?.tax_id) || [];
      return { chargeWithTaxName, chargeWithoutTaxName };
    };

    const amountTypeOptions = [
      {
        label: `${selectedCurrencyName?.currency_symbol} `,
        value: 'Amount',
      },
      {
        label: '%',
        value: 'Percent',
      },
    ];

    const renderCharges = (charge) => charge?.map((line) => (
      <div
        key={line?.chargeKey}
        className="form-calculator__field"
      >
        <div className="form-calculator__field-name">
          {!user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings?.use_custom_charges ? (
            <div className="select_extra_charge_wrapper">
              <SelectExtraCharge
                containerClassName="orgInputContainer"
                selectedChargeName={line.charge_name}
                disabled={createCNLoading || updateCNLoading}
                onChange={(value) => {
                  const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                  copyChargeData.map((item) => {
                    if (item?.chargeKey === line?.chargeKey) {
                      item.charge_name = value?.ledger_name;
                      item.chargesSacCode = (value?.charge_sac_code) || null;
                    }
                  });
                  this.setState({ chargeData: copyChargeData });
                }}
                customStyle={{
                  width: '220px',
                  backgroundColor: 'white',
                }}
                excludeCharges={chargeData?.map((item) => item?.charge_name)}
                entityName="SALES"
              />
              {line?.chargesTax && (
                <div style={{
                  color: '#2d7df7',
                  fontWeight: '400',
                  fontSize: '12px',
                }}
                >
                  {`tax@${line?.chargesTax}%`}
                </div>
              )}
            </div>
          ) : (
            <H3FormInput
              value={line?.charge_name}
              type="text"
              containerClassName={`${formSubmitted && Number(line?.charge_name) <= 0 ? 'form-error__input' : ''}`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                copyChargeData.map((item) => {
                  if (item?.chargeKey === line?.chargeKey) {
                    item.charge_name = e?.target?.value;
                  }
                });
                this.setState({ chargeData: copyChargeData });
              }}
            />
          )}
        </div>
        <div
          className="form-calculator__field-value"
          style={{ display: 'flex', gap: '0px' }}
        >
          <div style={{ width: '140px', marginRight: '-27px' }}>
            <H3FormInput
              value={line?.charge_amount}
              type="number"
              containerClassName={`orgInputContainer ${formSubmitted && Number(line?.charge_amount) <= 0 ? 'form-error__input' : ''}`}
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                copyChargeData.map((item) => {
                  if (item?.chargeKey === line?.chargeKey) {
                    const updatedChargeAmount = parseFloat(e?.target?.value) || 0;

                    // Update charge_amount
                    item.charge_amount = updatedChargeAmount;
                    // Compute chargesTaxData with updated values
                    const computedTaxData = Helpers.computeTaxation(
                      updatedChargeAmount,
                      line?.chargesTaxInfo,
                      user?.tenant_info?.state,
                      billingAddress?.state
                    );
                    // Update chargesTaxData
                    item.chargesTaxData = {
                      ...line?.chargesTaxInfo,
                      child_taxes: computedTaxData?.tax_info?.child_taxes || [],
                    };
                  }
                  return data;
                });
                this.setState({ chargeData: copyChargeData });
              }}
            />
          </div>
          <ChargesTaxInput
            openChargesTax={line?.openChargesTax}
            setOpenChargesTax={(value) => {
              const copyChargeData = JSON.parse(JSON.stringify(chargeData));
              copyChargeData.map((item) => {
                if (item?.chargeKey === line?.chargeKey) {
                  item.openChargesTax = value;
                }
              });
              this.setState({ chargeData: copyChargeData });
            }}
            sacCode={line?.chargesSacCode}
            setSacCode={(value) => {
              const copyChargeData = JSON.parse(JSON.stringify(chargeData));
              copyChargeData.map((item) => {
                if (item?.chargeKey === line?.chargeKey) {
                  item.chargesSacCode = value;
                }
              });
              this.setState({ chargeData: copyChargeData });
            }}
            chargesTaxId={line?.chargesTaxId}
            setChargesTaxData={(value) => {
              const updatedChargeData = chargeData?.map((item) => {
                if (item?.chargeKey === line?.chargeKey) {
                  return {
                    ...item,
                    chargesTaxId: !value ? 'Not Applicable' : value?.tax_id,
                    chargesTax: !value ? null : value?.tax_value,
                    chargesTaxInfo: !value ? null : value,
                    chargesTaxData: !value ? {
                      child_taxes: [
                        {
                          tax_amount: 0,
                          tax_type_name: '',
                        },
                      ],
                    } : {
                      ...value,
                      child_taxes: Helpers.computeTaxation(line?.charge_amount, value, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes,
                    },
                  };
                }
                return item;
              });
              this.setState({ chargeData: updatedChargeData });
            }}
            showTallyLedgerSales
            tallyLedgerName={line?.tallyLedgerName}
            setTallyLedgerName={(value) => {
              const updatedChargeData = chargeData?.map((item) => {
                if (item?.chargeKey === line?.chargeKey) {
                  item.tallyLedgerName = value;
                }
                return item;
              });
              this.setState({ chargeData: updatedChargeData });
            }}
            selectedTenant={selectedTenant || user?.tenant_info?.tenant_id}
          />
          <div
            className="form-calculator__delete-line-button"
            onClick={() => this.handleDeleteCharge(line?.chargeKey)}
          >
            <FontAwesomeIcon
              icon={faCircleXmark}
              size="sm"
              style={{ color: '#6f7276' }}
            />
          </div>
        </div>
      </div>
    ));

    const salesAccountList = user?.user_tenants?.find((item) => item?.tenant_id === selectedTenant || user?.tenant_info?.tenant_id)?.tally_configuration?.sales_account_list;

    const salesAccountOptions = salesAccountList?.map((item) => ({
      label: item?.sales_account_name,
      value: item?.sales_account_name,
    }));

    const creditNoteReasonsOptions = [
      { label: 'Sales Return', value: 'Sales Return' },
      { label: 'Opening Credits', value: 'Opening Credits' },
      { label: 'Post Sales Discount', value: 'Post Sales Discount ' },
      { label: 'Deficiency in Service', value: 'Deficiency in Service' },
      { label: 'Correction in Invoice', value: 'Correction in Invoice' },
      { label: 'Change in POS', value: 'Change in POS' },
      { label: 'Finalization of Provisional assessment', value: 'Finalization of Provisional assessment' },
      { label: 'Others', value: 'Others' },
    ];

    const getVisibleColumnsForReturnSlip = () => {
      const keysToRemove = new Set([
        'QUANTITY_TO_BE_RETURNED',
        'UNIT_PRICE',
        'TAX',
        'DISCOUNT',
        'LINE_TOTAL',
      ]);

      const filteredColumns = Object.fromEntries(
        Object.entries(visibleColumns).filter(([key]) => !keysToRemove.has(key))
      );

      return filteredColumns;
    };

    const addressSelectorTitle = (selectedAddressType, isReturnSlip) => {
      if (selectedAddressType === 'TENANT_SHIPPING') {
        return isReturnSlip ? 'Ship To Address' : 'Shipping Address';
      }
      if (selectedAddressType === 'TENANT_BILLING') {
        return isReturnSlip ? 'Ship From Address' : 'Billing Address';
      }
      return 'Address';
    };

    return (
      <Fragment>
        <Helmet>
          <meta charSet="utf-8" />
          <title>{H3_TITLE}</title>
        </Helmet>
        <Drawer
          open={showNewCustomerModal}
          onClose={() => this.setState({ showNewCustomerModal: false })}
          width="720px"
          destroyOnClose
        >
          <div className="custom-drawer__header-wrapper">
            <div className="custom-drawer__header" style={{ width: '680px' }}>
              <H3Text
                text="Add New Customer"
                className="custom-drawer__title"
              />
              <H3Image
                src={cdnUrl("icon-close-blue.png", "icons")}
                className="custom-drawer__close-icon"
                onClick={() => this.setState({ showNewCustomerModal: false })}
              />
            </div>
          </div>
          <CustomerForm
            callback={(createdCustomer) => {
              getCustomers(
                '',
                user?.tenant_info?.tenant_id,
                1,
                1000,
                '',
                (newCustomers) => {
                  this.showNewCustomerInSelect(
                    newCustomers?.customers.filter(
                      (customer) => customer?.customer_id === createdCustomer.customer_id,
                    )[0],
                  );
                  this.setState({ showNewCustomerModal: false });
                },
              );
            }}
          />
        </Drawer>
        <Drawer
          open={showAddressDrawer}
          onClose={() => this.setState({ showAddressDrawer: false, selectedAddressType: '' })}
          width="360"
          destroyOnClose
        >
          <AddressSelector
            title={addressSelectorTitle(selectedAddressType, isReturnSlip)}
            selectedAddressId={
              selectedAddressType === 'TENANT_SHIPPING'
                ? shippingAddress?.address_id
                : billingAddress?.address_id
            }
            onAddressChange={(address) => {
              if (selectedAddressType === 'TENANT_SHIPPING') {
                this.setState({ shippingAddress: address, showAddressDrawer: false });
              } if (selectedAddressType === 'TENANT_BILLING') {
                this.setState(() => {
                  const updateSate = {
                    billingAddress: address,
                    showAddressDrawer: false,
                  };

                  if (!isReturnSlip) {
                    updateSate.data = data?.map((record) => ({
                      ...record,
                      child_taxes: Helpers.computeTaxation((record?.quantity * record?.unitPrice) * (record.discount ? Number(100 - record.discount) / 100 : 1), record.taxInfo, user?.tenant_info?.state, address?.state)?.tax_info?.child_taxes,
                    }));
                  }
                  return updateSate;
                });
              }
            }}
            entityId={isReturnSlip && selectedAddressType === 'TENANT_SHIPPING' ? selectedTenant : selectedCustomer}
            entityType={isReturnSlip && selectedAddressType === 'TENANT_SHIPPING' ? 'TENANT' : 'CUSTOMER'}
            tenantId={selectedTenant || selectedCN?.tenant_id || selectedInvoice?.tenant_id || selectedInvoiceForCN?.tenant_id || user?.tenant_info?.tenant_id}
          />
        </Drawer>
        {getCNByIdLoading ? (
          <div className="create-cn__loading__wrapper">
            {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
              <div key={item} className="create-cn__loading__wrapper-row">
                <div className="create-cn__loading__wrapper-row__label loadingBlock" />
                <div className="create-cn__loading__wrapper-row__value loadingBlock" />
              </div>
            ))}
            <div className="create-cn__loading__wrapper-table loadingBlock" />
            <div className="create-cn__loading__wrapper-details loadingBlock" />
          </div>
        )
          : (
            <div className="form__wrapper" style={{ paddingTop: window.location.href.includes('/sales/credit-notes/') || window.location.href.includes('/sales/return-slip/') ? '90px' : '0px' }}>
              <div className="ant-row">
                <div className="ant-col-md-24">
                  {cnWithoutDeductingStock && (
                    <Alert
                      message={`Creating a credit note for ${invoiceType} orders will not restock the inventory.`}
                      type="warning"
                      showIcon
                      style={{
                        margin: '-8px 0px 2px -1px',
                        borderRadius: '4px',
                        padding: '4px',
                      }}
                    />
                  )}
                  <div className="form__section">
                    <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                      <H3Text text="PART A" className="form__section-title" />
                      <div className="form__section-line" />
                      <div style={{ display: 'flex', justifyContent: 'end', alignItems: 'center' }}>
                        <CurrencyConversionV2
                          selectedCurrencyName={selectedCurrencyName}
                          selectedCurrencyID={selectedCurrencyID}
                          isAutomaticConversionRate={isAutomaticConversionRate}
                          currencyConversionRate={currencyConversionRate}
                          setCurrencyConversionRate={(val) => this.setState({ currencyConversionRate: val })}
                          setIsAutomaticConversionRate={(val) => this.setState({ isAutomaticConversionRate: val })}
                          setSelectedCurrencyName={(val) => this.setState({ selectedCurrencyName: val })}
                          setSelectedCurrencyID={(val) => this.setState({ selectedCurrencyID: val })}
                        />
                      </div>
                    </div>
                    <div className="form__section-inputs mg-bottom-20">
                      <div className="ant-row">
                        <DocumentNumberSeqInput
                          valueFromProps={cnNumber}
                          updateCase={cnUpdateCase}
                          setInitialDocSeqNumber={(value) => this.setState({ initialCnNumber: value })}
                          entityName={isReturnSlip ? 'RETURN_SLIP' : 'CREDIT_NOTE'}
                          docSeqId={docSeqId}
                          tenantId={selectedInvoiceForCN?.tenant_id || selectedInvoice?.tenant_id || user?.tenant_info?.tenant_id}
                          onChangeFromProps={(event, newValue, seqId) => {
                            this.setState({
                              cnNumber: newValue ? (newValue || '') : (event?.target?.value || ''),
                              docSeqId: seqId,
                            });
                          }}
                          docTitle={isReturnSlip ? 'Return Slip #' : 'Credit Note #'}
                          formSubmitted={formSubmitted}
                        />
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              text="Customer"
                              className="form__input-row__label"
                              required
                            />
                            <div className={`form__input-row__input ${(formSubmitted && !selectedCustomer) ? 'ant_selector_with_error' : ''}`}>
                              <SelectCustomer
                                showAddCustomer={showAddCustomer}
                                addCustomer={() => this.setState({ showNewCustomerModal: true })}
                                disabled={createCNLoading || updateCNStatusLoading || selectedInvoiceForCN || cnUpdateCase}
                                selectedCustomer={selectedCustomer}
                                onChange={(value) => {
                                  getInvoice(user?.tenant_info?.org_id, selectedInvoiceForCN?.tenant_id || selectedInvoice?.tenant_id || user?.tenant_info?.tenant_id, '', 1, 30, value?.customer_info?.customer_id, '', 'CONFIRMED', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'INVOICE');

                                  const docCf = CustomFieldHelpers.postCfStructure(cfCreditNoteDoc?.filter((item) => (item?.isActive && item?.visible))) || [];
                                  const customerCf = value?.custom_field_values?.filter((item) => (item?.is_active)) || [];
                                  const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, customerCf) || [];

                                  this.setState({
                                    selectedCustomer: value?.customer_info?.customer_id,
                                    billingAddress: value?.customer_info?.billing_address_details,
                                    shippingAddress: value?.customer_info?.shipping_address_details,
                                    gstNumber: value?.customer_info?.gst_number,
                                    selectedCustomerInfo: value?.customer_info,
                                    data: [],
                                    selectedInvoice: null,
                                    selectedCurrencyID: value?.org_currency_id,
                                    selectedCurrencyName: value?.currency_info,
                                    currencyConversionRate: isAutomaticConversionRate ? value?.currency_info?.automatic_conversion_rate : value?.currency_info?.conversion_rate,
                                    cfCreditNoteDoc: mergedCf,
                                  });
                                }}
                                containerClass="create-cn__input-row orgInputContainer"
                                inputClassName="create-cn__input-row__input"
                                labelClassName="create-cn__input-row__label"
                                tenantId={selectedInvoiceForCN?.tenant_id || selectedInvoice?.tenant_id || user?.tenant_info?.tenant_id}
                                hideTitle
                              />
                            </div>
                          </div>
                        </div>
                        {(!selectedInvoiceForCN && !cnUpdateCase) ? (
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text text={isReturnSlip ? 'Consumption Order' : 'Invoice'} className="form__input-row__label" />
                              <div className="form__input-row__input">
                                <PRZSelect
                                  containerClass="form__input-row"
                                  filterOption={(input, option) => {
                                    return (option?.label ?? '').toLowerCase().includes(input?.toLowerCase());
                                  }}
                                  value={selectedInvoice}
                                  disabled={createCNLoading || updateCNStatusLoading || selectedInvoiceForCN || creditNoteWithoutLines || cnUpdateCase}
                                  allowClear
                                  showSearch
                                  onChange={(value) => {
                                    let invoiceLines = [];

                                    if (value !== undefined && value !== null) {
                                      const invoice = invoices?.data?.invoices?.find((item) => item?.invoice_id === value);
                                      invoiceLines = invoice?.invoice_lines?.map((invoiceLine) => {
                                        const discountValue = invoiceLine?.is_discount_in_percent ? invoiceLine?.line_discount_percentage : invoiceLine?.line_discount_amount;
                                        const taxableValue = invoiceLine?.is_discount_in_percent ? (invoiceLine?.quantity * invoiceLine?.unit_price * (1 - discountValue / 100)) : Math.max(invoiceLine?.quantity * invoiceLine?.unit_price - discountValue);
                                        return {
                                          key: uuidv4(),
                                          asset1: invoiceLine?.product_sku_info?.assets?.[0]?.url || '',
                                          product_sku_name: invoiceLine?.product_sku_info?.product_sku_name,
                                          product_category_info: invoiceLine?.product_sku_info?.product_category_info,
                                          product_sku_info: invoiceLine?.product_sku_info,
                                          invoice_qty: QUANTITY(invoiceLine?.quantity, invoiceLine?.uom_info[0]?.precision),
                                          returned_qty: QUANTITY(invoiceLine?.cn_quantity, invoiceLine?.uom_info[0]?.precision),
                                          quantity: QUANTITY(invoiceLine?.quantity - invoiceLine?.cn_quantity, invoiceLine?.uom_info[0]?.precision),
                                          unitPrice: Number(invoiceLine?.unit_price),
                                          uomId: Number(invoiceLine?.uom_id),
                                          uom_info: invoiceLine?.uom_info[0],
                                          taxId: Number(invoiceLine?.tax_id),
                                          taxInfo: invoiceLine?.tax_group_info,
                                          hsn_code: invoiceLine?.hsn_code,
                                          child_taxes: Helpers.computeTaxation(
                                            taxableValue,
                                            invoiceLine.tax_group_info,
                                            user?.tenant_info?.state,
                                            invoice?.billing_address_info?.state,
                                          )?.tax_info?.child_taxes,
                                          product_batches: this.getBatches(
                                            invoiceLine?.product_batches,
                                            QUANTITY(invoiceLine?.quantity - invoiceLine?.cn_quantity, invoiceLine?.uom_info[0]?.precision),
                                          ),
                                          discount: discountValue,
                                          invoice_line_id: invoiceLine?.invoice_line_id,
                                          tenant_product_id: invoiceLine?.tenant_product_id,
                                          remarks: invoiceLine?.remarks?.replace(/<[^>]+>/g, ''),
                                          remarkRequired: !!invoiceLine?.remarks?.replace(/<[^>]+>/g, ''),
                                          updateStock: true,
                                          lineDiscountType: invoiceLine?.is_discount_in_percent ? 'Percent' : 'Amount',
                                        };
                                      }) || [];

                                      const docCf = isReturnSlip ? cfV2DocReturnSlip?.data?.document_custom_fields || [] : cfV2DocCreditNote?.data?.document_custom_fields || [];
                                      const invoiceCf = invoice?.custom_fields?.filter((item) => (item?.is_active)) || [];
                                      const mergedCf = CustomFieldHelpers.mergeCustomFields(docCf, invoiceCf) || [];

                                      this.setState({
                                        selectedInvoice: value,
                                        data: invoiceLines,
                                        selectedCurrencyID: invoice?.org_currency_info?.org_currency_id,
                                        selectedCurrencyName: invoice?.org_currency_info,
                                        isAutomaticConversionRate: false,
                                        currencyConversionRate: invoice?.conversion_rate,
                                        selectedTenantTallyIntegrationId: user?.user_tenants?.find((item) => item?.tenant_id === invoice?.tenant_id)?.it_id,
                                        isLineWiseDiscount: invoice?.is_line_wise_discount,
                                        discountType: invoice?.is_discount_in_percent ? 'Percent' : 'Amount',
                                        discountPercentage: invoice?.is_discount_in_percent ? invoice?.discount_percentage : invoice?.discount_amount,
                                        chargeData: invoice?.other_charges?.map((item) => ({
                                          ...item,
                                          chargeKey: uuidv4(),
                                          chargesTaxId: item?.tax_info?.tax_id,
                                          chargesSacCode: item?.charge_sac_code,
                                          chargesTaxInfo: item?.tax_info,
                                          chargesTax: item?.tax_info?.tax_value,
                                          chargesTaxData: {
                                            ...item?.tax_info,
                                            child_taxes: Helpers.computeTaxation(item?.charge_amount, item?.tax_info, user?.tenant_info?.state, invoice?.billing_address_info?.state)?.tax_info?.child_taxes,
                                          },
                                        })),
                                        selectedTenant: invoice?.tenant_id,
                                        cfCreditNoteDoc: mergedCf,
                                        accountManager: invoice?.account_manager_id || null,
                                      });
                                    } else {
                                      invoiceLines = [{
                                        key: uuidv4(),
                                        asset1: '',
                                        product_sku_name: '',
                                        quantity: '',
                                        unitPrice: '',
                                        lot: 0,
                                        taxId: 0,
                                        discount: 0,
                                        child_taxes: [{
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        }],
                                        lineCustomFields: isReturnSlip ? cfCreditNoteLine || [] : [],
                                      }];

                                      this.setState({
                                        selectedInvoice: null,
                                        data: invoiceLines,
                                        selectedCurrencyID: null,
                                        selectedCurrencyName: null,
                                        selectedTenantTallyIntegrationId: user?.tenant_info?.it_id,
                                        narration: '',
                                      });
                                    }
                                  }}
                                  onSearch={(value) => {
                                    getInvoice(user?.tenant_info?.org_id, selectedInvoiceForCN?.tenant_id || selectedInvoice?.tenant_id || user?.tenant_info?.tenant_id, '', 1, 10, selectedCustomer, value, 'CONFIRMED', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 'INVOICE');
                                  }}
                                >
                                  {invoices?.data?.invoices?.map((item) => (
                                    <Option
                                      key={item?.invoice_id}
                                      value={item?.invoice_id}
                                      label={`${item?.invoice_number} ${item.invoice_lines?.length} ${MONEY((item.invoice_grand_total), item?.org_currency_info?.currency_code)}`}
                                    >
                                      <Popover
                                        placement='right'
                                        overlayStyle={{ lineHeight: '0px', }}
                                        title={
                                          <div style={{ marginTop: '3px', marginBottom: '-3px', }}>
                                            {`#${item.invoice_number} -- `}
                                            <b>{ISTDateFormat(item.created_at, 'MMM DD, YY')}</b>
                                            {' -- '}
                                            <b>
                                              {item.invoice_lines?.length}
                                              {' '}
                                              Items
                                            </b>
                                            {' -- '}
                                            <b>{MONEY((item.invoice_grand_total), item?.org_currency_info?.currency_code)}</b>
                                          </div>
                                        }
                                      >
                                        {`#${item.invoice_number} -- `}
                                        <b>{ISTDateFormat(item.created_at, 'MMM DD, YY')}</b>
                                        {' -- '}
                                        <b>
                                          {item.invoice_lines?.length}
                                          {' '}
                                          Items
                                        </b>
                                        {' -- '}
                                        <b>{MONEY((item.invoice_grand_total), item?.org_currency_info?.currency_code)}</b>
                                      </Popover>
                                    </Option>
                                  ))}
                                </PRZSelect>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div className="ant-col-md-6">
                            <div
                              className="form__input-row"
                              style={{ marginBottom: '10px' }}
                            >
                              <H3Text
                                text={isReturnSlip ? 'Consumption Order' : 'Invoice'}
                                className="form__input-row__label"
                              />
                              <H3Text
                                text={selectedCN?.data?.credit_notes?.[0]?.invoice_number || selectedInvoiceForCN?.invoice_number}
                                className="form__input-row__text"
                              />
                            </div>
                          </div>
                        )}
                        {(!selectedInvoice || selectedInvoiceForCN?.invoice_type === 'CONSUMPTION_ORDER' || selectedCN?.data?.credit_notes?.[0]?.doc_type === 'RETURN_SLIP') && (
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text
                                text="Department"
                                className="form__input-row__label"
                              />
                              <div className="form__input-row__input">
                                <SelectDepartment
                                  hideTitle
                                  selectedDepartment={tenantDepartmentId}
                                  onChange={(value) => {
                                    this.setState({
                                      tenantDepartmentId: value?.tenant_department_id,
                                      data: [
                                        {
                                          key: uuidv4(),
                                          asset1: '',
                                          product_sku_name: '',
                                          quantity: '',
                                          unitPrice: '',
                                          lot: 0,
                                          taxId: 0,
                                          discount: 0,
                                          child_taxes: [{
                                            tax_amount: 0,
                                            tax_type_name: '',
                                          }],
                                          lineCustomFields: isReturnSlip ? cfCreditNoteLine || [] : [],
                                        },
                                      ],
                                    });
                                  }}
                                  emptyNotAllowed
                                  customStyle={{
                                    border: '1px solid rgba(68, 130, 218, 0.25)',
                                    borderRadius: '4px',
                                    height: '28px',
                                    padding: '0px 3px',
                                  }}
                                  loading={createCNLoading || updateCNStatusLoading}
                                  disabled={createCNLoading || updateCNStatusLoading || cnUpdateCase || selectedInvoiceForCN?.invoice_type === 'CONSUMPTION_ORDER'}
                                  labelClassName="form__input-row__label"
                                  inputClassName="orgFormInput input"
                                  tenentLevelDepartment
                                  tenantId={user?.tenant_info?.tenant_id}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              text="GST Number"
                              className="form__input-row__label"
                            />
                            <H3FormInput
                              name="a valid GST Number"
                              type="text"
                              containerClassName={`orgInputContainer form__input-row__input ${(formSubmitted && gstNumber ? String(gstNumber)?.length !== 15 : false) ? 'form__input-row__input-error' : ''}`}
                              labelClassName="orgFormLabel"
                              inputClassName="orgFormInput input"
                              placeholder=""
                              onChange={(e) => this.setState({ gstNumber: e.target.value?.trim() })}
                              value={gstNumber}
                              disabled={createCNLoading || updateCNLoading || accountingGSTTransactionAsPerMaster}
                            />
                          </div>
                        </div>

                        <div className="ant-col-md-6">
                          <div className="form__input-row orgInputContainer">
                            <H3Text
                              required
                              text={isReturnSlip ? 'Return Slip Date' : 'Credit Note Date'}
                              className="form__input-row__label"
                            />
                            <div className={`form__input-row__input ${formSubmitted && !cnDate ? 'form__input-row__input-error' : ''}`}>
                              <DatePicker
                                value={cnDate}
                                onChange={(value) => {
                                  this.setState({ cnDate: value });
                                }}
                                style={{
                                  border: '1px solid rgba(68, 130, 218, 0.2)',
                                  borderRadius: '2px',
                                  height: '28px',
                                  padding: '1px 3px',
                                  width: '100%',
                                  background: 'white',
                                }}
                                disabled={createCNLoading || updateCNLoading}
                              />
                              {(formSubmitted && !cnDate) && (
                                <div className="input-error">
                                  *Please enter order date
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text
                              text="Reason"
                              className="form__input-row__label"
                              required
                            />
                            <div className={`form__input-row__input ${formSubmitted && !reasonForCancellation ? 'ant_selector_with_error' : ''}`}>
                              <PRZSelect
                                containerClass="form__input-row"
                                dropdownAlign={{ offset: [-6, 4] }}
                                filterOption={false}
                                value={reasonForCancellation}
                                disabled={createCNLoading || updateCNLoading}
                                onChange={(value) => {
                                  this.setState({
                                    reasonForCancellation: value,
                                  });
                                }}
                                placeholder="Select Reason"
                                options={creditNoteReasonsOptions}
                              />
                            </div>
                          </div>
                        </div>
                        {(selectedInvoiceForCN?.invoice_type === 'CONSUMPTION_ORDER' || isReturnSlip) && <div className="ant-col-md-6">
                          <div className="form__input-row">
                            <H3Text text="Sales Manager" className="form__input-row__label" />
                            <div className="form__input-row__input">
                              <SelectAppUser
                                hideTitle
                                containerClassName="orgInputContainer"
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                selectedUser={accountManager}
                                onChange={(value) => this.setState({ accountManager: value })}
                                loading={createCNLoading || updateCNLoading}
                                disabled
                              />
                            </div>
                          </div>
                        </div>}
                        {user?.user_tenants?.find((tenant) => selectedTenant || user?.tenant_info?.tenant_id === tenant?.tenant_id)?.tally_configuration?.credit_note_account_selection === 'DOC_LEVEL' && (
                          <div className="ant-col-md-6">
                            <div className="form__input-row">
                              <H3Text
                                required
                                text="Credit Note Account"
                                className="form__input-row__label"
                              />
                              <div
                                className="form__input-row__input"
                              >
                                <div className={`form__input-row sales_account__selector__wrapper ${formSubmitted &&
                                  !salesAccount &&
                                  user?.tenant_info?.tally_configuration?.sales_account_selection === 'DOC_LEVEL'
                                  ? 'sales_account__selector__wrapper_with_error'
                                  : ''
                                  }`}
                                >
                                  <PRZSelect
                                    value={salesAccount}
                                    onChange={(value) => {
                                      this.setState({ salesAccount: value });
                                    }}
                                    disabled={
                                      getTallyConnectionsLoading ||
                                      createCNLoading ||
                                      updateCNLoading
                                    }
                                    options={salesAccountOptions}
                                  />
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        <CustomFieldV3
                          customFields={cfCreditNoteDoc}
                          formSubmitted={formSubmitted}
                          customInputChange={(value, cfId) => this.customInputChange(value, cfId)}
                          wrapperClassName="ant-col-md-6"
                          containerClassName="form__input-row"
                          labelClassName="form__input-row__label"
                          inputClassName="form__input-row__input"
                          errorClassName="form__input-row__input-error"
                          disabled={createCNLoading || updateCNLoading}
                          hideTitle
                          isCarryForward={true}
                        />
                      </div>
                    </div>
                  </div>
                  <div className="form__section">
                    <div className="flex-display flex-align-c mg-bottom-5 pd-right-15">
                      <H3Text text="PART B" className="form__section-title" />
                      <div className="form__section-line" />
                    </div>
                    <div className="ant-row">
                      <div className="ant-col-md-8">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text={isReturnSlip ? 'Ship To' : 'Shipping Address'}
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__address__wrapper ${(formSubmitted && !shippingAddress) ? 'form__input-row__address-error' : ''} ${(!selectedCustomer) ? 'form__input-row__address-disabled' : ''}`}>
                            <div className="form__input-row__address">
                              {shippingAddress && (
                                <div className="form__input-row__address-info">
                                  <div className="form__input-row__address-l1">
                                    {shippingAddress?.address1}
                                  </div>
                                  <div className="form__input-row__address-l2">
                                    {`${shippingAddress?.city}, ${shippingAddress?.state}, ${shippingAddress?.postal_code}, ${shippingAddress?.country}`}
                                  </div>
                                </div>
                              )}
                              {!shippingAddress && (
                                <H3Text
                                  text="Select address.."
                                  className="form__input-row__address-placeholder"
                                />
                              )}
                              <div
                                className="form__input-row__address-icon"
                                onClick={() => this.setState({
                                  selectedAddressType: 'TENANT_SHIPPING',
                                  showAddressDrawer: true,
                                })}
                              >
                                <EditFilled />
                                {' '}
                                Update
                              </div>
                            </div>
                            {formSubmitted && !shippingAddress && (
                              <div className="input-error">
                                *Please select shipping address
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-8">
                        <div className="form__input-row">
                          <H3Text
                            required
                            text={isReturnSlip ? 'Ship From' : 'Billing Address'}
                            className="form__input-row__label"
                          />
                          <div className={`form__input-row__address__wrapper ${(formSubmitted && !billingAddress) ? 'form__input-row__address-error' : ''} ${(!selectedCustomer) ? 'form__input-row__address-disabled' : ''}`}>
                            {' '}
                            <div className="form__input-row__address">
                              {billingAddress && (
                                <div className="form__input-row__address-info">
                                  <div className="form__input-row__address-l1">
                                    {billingAddress?.address1}
                                  </div>
                                  <div className="form__input-row__address-l2">
                                    {`${billingAddress?.city}, ${billingAddress?.state} ${billingAddress?.postal_code}, ${billingAddress?.country}`}
                                  </div>
                                </div>
                              )}
                              {!billingAddress && (
                                <H3Text
                                  text="Select address.."
                                  className="form__input-row__address-placeholder"
                                />
                              )}
                              <div
                                className="form__input-row__address-icon"
                                onClick={() => this.setState({
                                  selectedAddressType: 'TENANT_BILLING',
                                  showAddressDrawer: true,
                                })}
                              >
                                <EditFilled />
                                {' '}
                                Update
                              </div>
                            </div>
                            {formSubmitted && !billingAddress && (
                              <div className="input-error">
                                *Please select billing address
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      {!isReturnSlip && (
                        <React.Fragment>
                          <div className="ant-col-md-8" />
                          <div className="ant-col-md-8 form__checkboxes">
                            <div className="form__input-row">
                              <Checkbox
                                disabled={createCNLoading || updateCNStatusLoading || !selectedCustomer || creditNoteWithoutLines || cnWithoutDeductingStock}
                                checked={creditNoteWithReturnedQyt}
                                onChange={(e) => {
                                  // Step 1: Deep copy the data
                                  const copyData = JSON.parse(JSON.stringify(data));

                                  // Step 2: Iterate and update the copyData
                                  const updatedData = copyData?.map((item) => {
                                    // Update product_batches
                                    item.product_batches = item?.product_batches?.map((batch) => {
                                      batch.returned_quantity = batch.quantity;
                                      return batch;
                                    });

                                    // Update top-level returned_quantity
                                    return {
                                      ...item,
                                      returned_quantity: item.quantity,
                                    };
                                  });

                                  // Step 3: Set the state with updated data
                                  this.setState({
                                    creditNoteWithReturnedQyt: e.target.checked,
                                    data: updatedData,
                                  });
                                }}
                              />
                              <span
                                style={{
                                  fontWeight: '500',
                                  fontSize: '12px',
                                  marginLeft: '5px',
                                }}
                              >
                                Credit Only Goods
                                <Tooltip
                                  title={(
                                    <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
                                      <div>
                                        The quantity specified in the return may differ from the actual quantity on the credit note. It is also possible to create a credit note for the customer without taking return of any goods or with zero return quantity.
                                      </div>
                                      <br />
                                      <div>
                                        <b>Examples:</b>
                                      </div>
                                      <br />
                                      <div>
                                        <i>Example 1:</i>
                                        {' '}
                                        A customer receives a shipment of 15 units of a product but discovers that 3 units are defective. The customer contacts you to report the discrepancy. After inspection, you agree that the 3 units are indeed defective. You issue a credit note for 2 units, allowing the customer to keep the 1 unit as a replacement.

                                      </div>
                                      <br />
                                      <div>
                                        <i>Example 2:</i>
                                        {' '}
                                        A customer mistakenly overpays for a shipment of goods. The customer notices the error and contacts you to request a credit. You verify the error and issue a credit note for the overpaid amount, without requiring the customer to return any goods.
                                      </div>
                                    </div>
                                  )}
                                >
                                  <InfoCircleOutlined
                                    style={{ marginLeft: '5px', color: 'rgb(45, 124, 247)', cursor: 'pointer' }}
                                  />
                                </Tooltip>
                              </span>
                            </div>
                            <div className="form__input-row">
                              <Checkbox
                                disabled={createCNLoading || updateCNStatusLoading || !selectedCustomer || selectedInvoice}
                                checked={creditNoteWithoutLines}
                                onChange={(e) => {
                                  this.setState({
                                    creditNoteWithoutLines: e.target.checked,
                                    data: [
                                      {
                                        key: uuidv4(),
                                        asset1: '',
                                        product_sku_name: '',
                                        quantity: 1,
                                        unitPrice: '',
                                        lot: 0,
                                        taxId: 0,
                                        discount: 0,
                                        child_taxes: [{
                                          tax_amount: 0,
                                          tax_type_name: '',
                                        }],
                                        lineCustomFields: isReturnSlip ? cfCreditNoteLine || [] : [],
                                      },
                                    ],
                                  });
                                }}
                              />
                              <span
                                style={{
                                  fontWeight: '500',
                                  fontSize: '12px',
                                  marginLeft: '5px',
                                }}
                              >
                                Document Contains No Items
                              </span>
                            </div>
                          </div>
                        </React.Fragment>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="form__lines-wrapper">
                <div className={isQuickView ? 'create-cn__lines-wrapper-drawer' : 'create-cn__lines-wrapper'}>
                  <CNLines
                    title={() => (
                      <div className="create-cn-title__wrapper">
                        <H3Text text={`Products (${data.filter((item) => item.product_sku_name?.length > 0)?.length})`} className="create-po__input-row__label" />
                        {!isReturnSlip && (<div className="create-cn-title-left">
                          <Checkbox
                            checked={!isLineWiseDiscount}
                            onChange={() => {
                              const copyData = JSON.parse(JSON.stringify(data));
                              copyData.map((i) => {
                                i.discount = 0;
                                i.unitDiscount = 0;
                                i.showUnitDiscount = false;
                                i.lineDiscountType = 'Percent';
                                i.child_taxes = Helpers.computeTaxation(i?.quantity * i?.unitPrice, i?.taxInfo, user?.tenant_info?.state, billingAddress?.state)?.tax_info?.child_taxes;
                              });
                              this.setState({
                                data: copyData,
                                isLineWiseDiscount: !isLineWiseDiscount,
                                discountPercentage: 0,
                                discountType: 'Percent',
                              });
                            }}
                            disabled={createCNLoading || updateCNLoading}
                          />
                          <div style={{ marginLeft: '5px' }}>Credit Note Level Discount</div>
                        </div>)}
                        <div style={{ marginLeft: '5px', display: isReturnSlip ? 'block' : 'none' }}>
                          <CustomDocumentColumns
                            visibleColumns={getVisibleColumnsForReturnSlip() || []}
                            setVisibleColumns={(dt) => this.setState({ visibleColumns: dt })}
                            customColumns={cfCreditNoteLine || []}
                            data={data}
                            updateData={(updatedData) => this.setState({ data: updatedData })}
                          />
                        </div>
                      </div>
                    )}
                    cnWithoutDeductingStock={cnWithoutDeductingStock}
                    handleDelete={this.handleDelete}
                    handleProductChange={this.handleProductChange}
                    handleProductChangeValue={this.handleProductChangeValue}
                    data={data}
                    toggleBatch={(batch, adjustmentRow) => this.toggleBatch(batch, adjustmentRow)}
                    addNewRow={() => this.addNewRow()}
                    updateData={(updatedData) => this.setState({ data: updatedData })}
                    formSubmitted={formSubmitted}
                    loading={getCNByIdLoading || createCNLoading || updateCNLoading}
                    isLineWiseDiscount={isLineWiseDiscount}
                    discountPercentage={discountPercentage}
                    discountType={discountType}
                    selectedCurrencyName={selectedCurrencyName}
                    billFromState={user?.tenant_info?.state}
                    billToState={billingAddress?.state}
                    creditNoteWithReturnedQyt={creditNoteWithReturnedQyt}
                    selectedInvoice={selectedInvoice}
                    tenantDepartmentId={tenantDepartmentId}
                    creditNoteWithoutLines={creditNoteWithoutLines}
                    isAdhocCnLiens={isAdhocCnLiens}
                    isCarryForward={!!selectedInvoiceForCN}
                    isReturnSlip={isReturnSlip}
                    visibleColumns={visibleColumns}
                    customLineInputChange={this.customLineInputChange}
                    cfCreditNoteLine={cfCreditNoteLine}
                  />
                </div>
              </div>

              <div className="form__data-wrapper">
                <div className="ant-row">
                  {!selectedInvoice && !creditNoteWithoutLines && !cnUpdateCase && (
                    <div className="ant-col-md-24">
                      <div className="new-row-button" onClick={() => this.addNewRow()}>
                        <span className="new-row-button__icon"><PlusCircleFilled /></span>
                        <div>New Item</div>
                      </div>
                    </div>
                  )}
                  <div className="ant-col-md-12">
                    <div className="form__data-tc">
                      <label className="orgFormLabel">Terms and Conditions</label>
                      <RichTextEditor
                        onChange={(value) => this.handleChangeTextArea(value)}
                        value={termsAndConditions}
                        disabled={createCNLoading || updateCNLoading}
                      />
                    </div>
                    {!!selectedTenantTallyIntegrationId && !isReturnSlip && (
                      <div className="form__data-tc">
                        <label className="orgFormLabel">Narration</label>
                        <textarea
                          className="orgFormInput"
                          rows="4"
                          onChange={(event) => this.handleChangeNarration(event.target.value)}
                          disabled={createCNLoading || updateCNLoading}
                          value={narration}
                          style={{
                            minHeight: '40px',
                            height: '60px',
                          }}
                        />
                      </div>
                    )}
                    <div
                      className="form__data-attachment"
                    >
                      <label className="orgFormLabel">Attachment(s)</label>
                      <Upload
                        action={Constants.UPLOAD_FILE}
                        listType="picture-card"
                        fileList={fileList}
                        disabled={createCNLoading || updateCNLoading}
                        multiple
                        onChange={(fileListData) => {
                          this.setState({
                            fileList: fileListData?.fileList?.map((item) => ({
                              ...item,
                              url: item?.response?.response?.location || item?.url,
                            })),
                          });
                        }}
                      >
                        {fileList?.length >= 20 ? null : uploadButton}
                      </Upload>
                    </div>
                  </div>
                  <div className="ant-col-md-12">
                    {!isReturnSlip && (<div className="form-calculator__wrapper">
                      <div className="form-calculator">
                        <div className="form-calculator__field">
                          <H3Text
                            text="Total Amount"
                            className="form-calculator__field-name"
                          />
                          <H3Text
                            text={MONEY((this.getLineTotals().totalAmount), selectedCurrencyName?.currency_code)}
                            className="form-calculator__field-value"
                            hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                            popOverMessage={'You don\'t have access to view total amount'}
                          />
                        </div>
                        {isLineWiseDiscount ? (
                          <div className="form-calculator__field">
                            <H3Text
                              text="Discount"
                              className="form-calculator__field-name"
                            />
                            <H3Text
                              text={MONEY((this.getLineTotals().totalDiscount), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                            />
                          </div>
                        ) : (
                          <div className="form-calculator__field">
                            <H3Text
                              text="Discount"
                              className="form-calculator__field-name"
                            />
                            <div
                              className="form-calculator__field-value"
                              style={{ display: 'flex' }}
                            >
                              <div style={{ width: '112px' }}>
                                <H3FormInput
                                  value={discountPercentage}
                                  type="number"
                                  containerClassName={`${formSubmitted && Number(discountPercentage) <= 0
                                    ? 'form-error__input'
                                    : ''
                                    }`}
                                  labelClassName="orgFormLabel"
                                  inputClassName="orgFormInput"
                                  onChange={(e) => {

                                    const copyData = JSON.parse(JSON.stringify(data));
                                    const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.unitPrice), 0);

                                    copyData.map((item) => {

                                      const discountValue = discountType === 'Percent' ? parseFloat(e.target.value) : ((item.quantity * item.unitPrice) / parseFloat(totalValue)) * parseFloat(e.target.value);

                                      const taxableValue = discountType === 'Percent'
                                        ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100)
                                        : Math.max(item.quantity * item?.unitPrice - discountValue, 0);

                                      item.discount = discountValue;
                                      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billingAddress?.state, shippingAddress?.state)?.tax_info?.child_taxes;
                                    });
                                    this.setState({
                                      data: copyData,
                                      discountPercentage: parseFloat(e.target.value),
                                    });
                                  }}
                                />
                              </div>
                              <div className="form-calculator__discount-type">
                                <PRZSelect
                                  value={discountType}
                                  onChange={(value) => {

                                    const copyData = JSON.parse(JSON.stringify(data));
                                    const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.unitPrice), 0);

                                    copyData.map((item) => {

                                      const discountValue = value === 'Percent' ? Number(discountPercentage) : ((item.quantity * item.unitPrice) / parseFloat(totalValue)) * Number(discountPercentage);

                                      const taxableValue = value === 'Percent'
                                        ? (item?.quantity * item?.unitPrice) * (1 - discountValue / 100)
                                        : Math.max(item.quantity * item?.unitPrice - discountValue, 0);

                                      item.discount = discountValue;
                                      item.lineDiscountType = value;
                                      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billingAddress?.state, shippingAddress?.state)?.tax_info?.child_taxes;
                                    });
                                    this.setState({
                                      data: copyData,
                                      discountType: value,
                                    });
                                  }}
                                  options={amountTypeOptions}
                                />
                              </div>
                            </div>
                          </div>
                        )}
                        {renderCharges(splitChargesData(chargeData)?.chargeWithTaxName)}
                        <div className="form-calculator__field">
                          <H3Text
                            text="Taxable Amount"
                            className="form-calculator__field-name"
                          />
                          <H3Text
                            text={MONEY((this.getLineTotals().totalBase), selectedCurrencyName?.currency_code)}
                            className="form-calculator__field-value"
                            hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                            popOverMessage={'You don\'t have access to view taxable amount'}
                          />
                        </div>
                        {data?.[0]?.child_taxes?.[0]?.tax_type_name && Helpers.groupAndSumByTaxName(FormHelpers.childTaxesData([...data, ...chargeData?.flatMap((charge) => (charge?.chargesTaxData))]))?.map((tax, i) => (
                          <Fragment key={i}>
                            <div className="form-calculator__field">
                              <H3Text
                                text={tax?.tax_type_name}
                                className="form-calculator__field-name"
                              />
                              <H3Text
                                text={MONEY((tax?.tax_amount || '0'), selectedCurrencyName?.currency_code)}
                                className="form-calculator__field-value"
                                hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                                popOverMessage={`You don't have access to view ${tax?.tax_type_name?.toLowerCase()}`}
                              />
                            </div>
                          </Fragment>
                        ))}
                        {user?.tenant_info?.global_config?.settings?.enable_tds_tcs && user?.tenant_info?.country_code === 'IN' && (
                          <div className="form-calculator__field">
                            <div
                              className="form-calculator__field-name"
                              style={{ marginBottom: '12px' }}
                            >
                              <H3Text text="TCS" className="form-calculator__field-name" />
                              <SelectTaxType
                                selectedTaxType={taxTypeId}
                                disabled={createCNLoading || updateCNLoading}
                                onChange={(value) => {
                                  this.setState({
                                    taxTypeId: value?.tax_id,
                                    taxTypeInfo: value,
                                    taxTypeName: value?.tax_type_name,
                                    taxType: value?.tax_type,
                                  });
                                }}
                                taxTypeName="TCS"
                                customStyle={{
                                  width: '220px',
                                  backgroundColor: 'white',
                                }}
                              />
                            </div>
                            <H3Text
                              text={MONEY((this.getLineTotals().totalTcs || '0'), selectedCurrencyName?.currency_code)}
                              className="form-calculator__field-value"
                              hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                              popOverMessage={'You don\'t have access to view tcs amount'}
                            />
                          </div>
                        )}

                        {/* {chargeData?.map((line) => (
                          <div key={line?.chargeKey} className="form-calculator__field">
                            <div className="form-calculator__field-name">
                              {!user?.tenant_info?.sales_config?.sub_modules?.sales_order?.settings?.use_custom_charges ? (
                                <SelectExtraCharge
                                  containerClassName="orgInputContainer"
                                  selectedChargeName={line.charge_name}
                                  disabled={createCNLoading || updateCNLoading}
                                  onChange={(value) => {
                                    const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                                    copyChargeData.map((item) => {
                                      if (item.chargeKey === line.chargeKey) {
                                        // eslint-disable-next-line no-param-reassign
                                        item.charge_name = value?.ledger_name;
                                      }
                                    });
                                    this.setState({ chargeData: copyChargeData });
                                  }}
                                  customStyle={{
                                    width: '220px',
                                    backgroundColor: 'white',
                                  }}
                                  excludeCharges={chargeData?.map((item) => item?.charge_name)}
                                  entityName="SALES"
                                />
                              ) : (
                                <H3FormInput
                                  value={line?.charge_name}
                                  type="text"
                                  containerClassName={`${formSubmitted && Number(line?.charge_name) <= 0
                                    ? 'form-error__input'
                                    : ''
                                    }`}
                                  labelClassName="orgFormLabel"
                                  inputClassName="orgFormInput"
                                  onChange={(e) => {
                                    const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                                    copyChargeData.map((item) => {
                                      if (item.chargeKey === line.chargeKey) {
                                        // eslint-disable-next-line no-param-reassign
                                        item.charge_name = e.target.value;
                                      }
                                      return data;
                                    });
                                    this.setState({ chargeData: copyChargeData });
                                  }}
                                />
                              )}

                            </div>
                            <div className="form-calculator__field-value" style={{ display: 'flex' }}>
                              <H3FormInput
                                value={line?.charge_amount}
                                type="number"
                                containerClassName={`orgInputContainer ${formSubmitted && Number(line?.charge_amount) <= 0
                                  ? 'form-error__input'
                                  : ''
                                  }`}
                                labelClassName="orgFormLabel"
                                inputClassName="orgFormInput"
                                onChange={(e) => {
                                  const copyChargeData = JSON.parse(JSON.stringify(chargeData));
                                  copyChargeData.map((item) => {
                                    if (item.chargeKey === line.chargeKey) {
                                      // eslint-disable-next-line no-param-reassign
                                      item.charge_amount = parseFloat(e.target.value);
                                    }
                                    return data;
                                  });
                                  this.setState({ chargeData: copyChargeData });
                                }}
                                allowNegative
                              />
                              <div
                                className="form-calculator__delete-line-button"
                                onClick={() => this.handleDeleteCharge(line?.chargeKey)}
                              >
                                <FontAwesomeIcon
                                  icon={faCircleXmark}
                                  size="sm"
                                  style={{ color: '#6f7276' }}
                                />
                              </div>
                            </div>
                          </div>
                        ))} */}
                        {renderCharges(splitChargesData(chargeData)?.chargeWithoutTaxName)}
                        <div className="new-charge-row-button" onClick={() => this.addNewChargesRow()}>
                          <span className="new-charge-row-button__icon">
                            <PlusCircleFilled />
                          </span>
                          <div>Add Charges</div>
                        </div>
                        {user?.tenant_info?.sales_config?.sub_modules?.credit_note?.settings?.round_off_method !== 'NO_ROUND_OFF' && (
                          <div className="form-calculator__field">
                            <H3Text
                              text="Round Off"
                              className="form-calculator__field-name"
                            />
                            <Tooltip
                              title={`Round Off method for Credit Note is set to ${user?.tenant_info?.sales_config?.sub_modules?.credit_note?.settings?.round_off_method?.replace(/_/g, ' ')?.toProperCase()}`}
                            >
                              <div style={{ cursor: 'pointer', }}>
                                <FontAwesomeIcon icon={faCircleInfo} size='lg' style={{ color: '#2D7DF7', }} />
                              </div>
                            </Tooltip>
                            <H3Text
                              text={`${Helpers.configuredRoundOff(this.getLineTotals()?.cnTotal, user?.tenant_info?.sales_config?.sub_modules?.credit_note?.settings?.round_off_method)?.roundOff < 0 ? '(-) ' : ''}${MONEY(
                                Math.abs(Helpers.configuredRoundOff(this.getLineTotals()?.cnTotal, user?.tenant_info?.sales_config?.sub_modules?.credit_note?.settings?.round_off_method)?.roundOff),
                                selectedCurrencyName?.currency_code
                              )}`}
                              className="form-calculator__field-value"
                              hideText={
                                isDataMaskingPolicyEnable && isHideSellingPrice
                              }
                              popOverMessage={
                                'You don\'t have access to view sub total'
                              }
                            />
                          </div>
                        )}
                        <div className="form-calculator__field form-calculator__field-total">
                          <H3Text
                            text="Grand Total"
                            className="form-calculator__field-name"
                          />
                          <H3Text
                            text={MONEY(Helpers.configuredRoundOff(this.getLineTotals().cnTotal, user?.tenant_info?.sales_config?.sub_modules?.credit_note?.settings?.round_off_method)?.value, selectedCurrencyName?.currency_code)}
                            className="form-calculator__field-value"
                            hideText={isDataMaskingPolicyEnable && isHideSellingPrice}
                            popOverMessage={'You don\'t have access to view grand total amount'}
                          />
                        </div>
                      </div>
                    </div>)}
                  </div>
                </div>
              </div>
            </div>
          )}
        <div className="form__footer">
          {!cnWithoutDeductingStock && (
            <H3Button
              text="Save as Draft"
              buttonType={defaultButtonTypes.BLUE_ROUNDED}
              customClass="form__footer-draft"
              onClick={() => {
                if (!createCNLoading) {
                  this.createCN(false);
                }
              }}
              isLoading={(!withApproval && createCNLoading) || (!withApproval && updateCNLoading)}
              disabled={createCNLoading || updateCNLoading || !creditNote}
            />
          )}
          <H3Button
            text="Save and Issue"
            buttonType={defaultButtonTypes.BLUE_ROUNDED}
            customClass="form__footer-submit"
            onClick={() => {
              if (!createCNLoading) {
                this.createCN(true);
              }
            }}
            isLoading={(withApproval && createCNLoading) || (withApproval && updateCNLoading) || (withApproval && updateCNStatusLoading)}
            disabled={createCNLoading || updateCNStatusLoading || !creditNote}
          />
          &nbsp; &nbsp;
          {selectedInvoice && (selectedInvoiceForCN?.invoice_grand_total - (selectedInvoiceForCN?.total_payment_made + selectedInvoiceForCN?.credits_applied) > 0) && (
            <H3Button
              text="Issue and Apply Credit"
              buttonType={defaultButtonTypes.BLUE_ROUNDED}
              customClass="form__footer-submit"
              onClick={() => {
                if (!createCNLoading) {
                  this.createCN(true, true);
                }
              }}
              isLoading={(withApproval && createCNLoading) || updateCNStatusLoading}
              disabled={createCNLoading || updateCNStatusLoading || !creditNote}
            />
          )}

          <div className="form-barcode__wrapper" style={{ display: 'flex' }}>
            {!creditNote && (
              <Popconfirm
                placement="topRight"
                title="This feature is not accessible within your current plan to use this feature contact us."
                onConfirm={() => window.location.href = 'mailto:<EMAIL>'}
                okText="Contact Us"
                cancelText="Cancel"
              >
                <img
                  className="barcode-restrict"
                  src={cdnUrl("crown2.png", "images")}
                  alt="premium"
                  style={{
                    marginLeft: '8px',
                    marginTop: '5px',
                  }}
                />
              </Popconfirm>
            )}
          </div>
        </div>
      </Fragment>
    );
  }
}

const mapStateToProps = ({
  UserReducers, CNReducers, InvoiceReducers, PurchaseOrderReducers, CFV2Reducers, CurrenciesReducers, TallyIntegrationReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  createCNLoading: CNReducers.createCNLoading,
  updateCNLoading: CNReducers.updateCNLoading,
  getCNByIdLoading: CNReducers.getCNByIdLoading,
  selectedCN: CNReducers.selectedCN,
  invoices: InvoiceReducers.invoices,
  getDocCFV2Loading: CFV2Reducers.getDocCFV2Loading,
  cfV2DocCreditNote: CFV2Reducers.cfV2DocCreditNote,
  cfV2DocReturnSlip: CFV2Reducers.cfV2DocReturnSlip,
  CurrenciesResults: CurrenciesReducers.CurrenciesResults,
  getCurrenciesLoading: CurrenciesReducers.getCurrenciesLoading,
  priceMasking: UserReducers.priceMasking,
  getTallyConnectionsLoading: TallyIntegrationReducers.getTallyConnectionsLoading,
});

const mapDispatchToProps = (dispatch) => ({
  getCustomers: (keyword, tenantId, page, limit, customerId, callback) => dispatch(CustomerActions.getCustomers(keyword, tenantId, page, limit, customerId, callback)),
  createCN: (payload, callback) => dispatch(CNActions.createCN(payload, callback)),
  updateCN: (payload, callback) => dispatch(CNActions.updateCN(payload, callback)),
  getCNById: (tenantId, invoiceId, docType) => dispatch(CNActions.getCNById(tenantId, invoiceId, docType)),
  getTenantSkuOffer: (tenantProductId, tenantSellerId, callback) => dispatch(OfferActions.getOfferByTenantSku(tenantProductId, tenantSellerId, callback)),
  getInvoice: (orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status, startDate, endDate, departmentId, listScreen, paymentStatus, isEwayBilled, secondaryStatus, shopifySearchKeyword, tags, callback, isMarketplaceInvoice, accountManagerIds, source, stockStatus, excludeInvoiceIds, excludeGRNCustomerIds, invoiceType) => dispatch(InvoiceActions.getInvoice(orgId, tenantId, invoiceId, page, limit, customerId, searchKeyword, status, startDate, endDate, departmentId, listScreen, paymentStatus, isEwayBilled, secondaryStatus, shopifySearchKeyword, tags, callback, isMarketplaceInvoice, accountManagerIds, source, stockStatus, excludeInvoiceIds, excludeGRNCustomerIds, invoiceType)),
  getCNByIdSuccess: (selectedCN) => dispatch(CNActions.getCNByIdSuccess(selectedCN)),
  updateCNStatus: (payload, callback) => dispatch(CNActions.updateCNStatus(payload, callback)),
  getInvoiceSuccess: (invoice) => dispatch(InvoiceActions.getInvoiceSuccess(invoice)),
  getDocCFV2Success: (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getDocCFV2: (paylaod, callback) => dispatch(CFV2Actions.getDocCFV2(paylaod, callback)),
  getCurrencies: (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CreditNoteForm));
