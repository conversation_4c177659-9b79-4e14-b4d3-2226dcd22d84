import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  Radio, Drawer, Checkbox, Select, Popconfirm,
} from 'antd';
import H3FormInput from '@Uilib/h3FormInput';
import SellerActions from '@Actions/sellerActions';
import CustomerActions from '@Actions/customerActions';
import CFV2Actions from '@Actions/configurations/cfV2Actions';
import H3Button, { defaultButtonTypes } from '@Uilib/h3Button';
import H3Text from '@Uilib/h3Text';
import Helpers from '@Apis/helpers';
import AddressSelector from '@Components/Common/FormUtils/AddressSelecter';
import CustomFieldV2 from '@Components/Common/CustomFieldV2';
import './style.scss';
import { Countries, CountryCodes } from '@Apis/constants';
import { cdnUrl } from "@Utils/cdnHelper";
import SelectAppUser from '@Components/Common/SelectAppUser';
import CustomFieldHelpers from '@Helpers/CustomFieldHelpers';
import CurrenciesActions from '@Actions/configurations/currenciesAction';
import SelectPaymentTerm from '@Components/Common/SelectPaymentTerm';
import { GetTallyConfigurations } from '../../../../../../modules/getTallyConfigurations';
import PRZSelect from '../../../../../Common/UI/PRZSelect';
import SelectPaymentRemark from '@Components/Common/SelectPaymentRemark';

const { Option } = Select;

class CustomerForm extends Component {
  constructor(props) {
    super(props);
    this.state = {
      customerTypes: {
        1: 'BUSINESS',
        2: 'INDIVIDUAL',
      },
      gstTreatmentTypes: [
        { id: 1, TreatmentType: 'REGISTERED' },
        { id: 2, TreatmentType: 'OVERSEAS' },
        { id: 3, TreatmentType: 'UNREGISTERED' },
      ],
      customerType: 'BUSINESS',
      selectedGstAdjustmentType: 'UNREGISTERED',
      customerTypeValue: 1,
      customerName: '',
      gstin: '',
      pinCodeShipping: '',
      pinCodeBilling: '',
      districtShipping: '',
      districtBilling: '',
      addressShipping: '',
      addressBilling: '',
      stateShipping: '',
      stateBilling: '',
      phone: '',
      email: '',
      showSelectedTenants: false,
      allTenantsSelected: true,
      page: 1,
      limit: 10,
      checkedAddress: false,
      customer: null,
      paymentTerms: 0,
      billingAddressTallyType: '',
      shippingAddressTallyType: '',
    };
  }
  static getDerivedStateFromProps(props, state) {
    if (props.cfV2DocCustomers && !state.isUserReadyForCF) {
      return {
        ...state,
        cfCustomersDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocCustomers?.data?.document_custom_fields, true) || [],
        isUserReadyForCF: true,
      };
    }
    if (props.user?.tenant_info?.org_default_currency_info?.org_currency_id && !state.isCurrencySelected && !props?.selectedCustomer) {
      return {
        ...state,
        selectedCurrencyID: props.user?.tenant_info?.org_default_currency_info?.org_currency_id,
        isCurrencySelected: true,
      };
    }
    if (props.pincodeCity && !props.selectedCustomer) {
      const city = props.pincodeCity;
      const cityState = props.pincodeState;
      props.setCityState(null, null);
      if (state.pinCodeShippingChange) {
        return {
          ...state, districtShipping: city, stateShipping: cityState, pinCodeShippingChange: false,
        };
      }
      if (state.checkedAddress) {
        return {
          ...state,
          districtBilling: city,
          stateBilling: cityState,
          pinCodeShippingChange: false,
          districtShipping: city,
          stateShipping: cityState,
        };
      }
      return {
        ...state, districtBilling: city, stateBilling: cityState, pinCodeShippingChange: false,
      };
    }

    if (props.cfV2DocCustomers && !state.isUserReady) {
      return {
        ...state,
        cfCustomersDoc: CustomFieldHelpers.getCfStructure(props.cfV2DocCustomers?.data?.document_custom_fields, true) || [],
        isUserReady: true,
      };
    }
    if (props?.selectedCustomer && props.cfV2DocCustomers?.data?.success && !state.isUserReadyForCFInUpdate) {
      let oldCustomField = props?.selectedCustomer?.custom_fields || [];

      return {
        ...state,
        cfCustomersDoc: CustomFieldHelpers.getDocumentCf(props.cfV2DocCustomers?.data?.document_custom_fields, oldCustomField),
        isUserReadyForCFInUpdate: true,
      };
    }
    if (!props.selectedCustomer && !state.countryBilling && !state.countryShipping) {
      return {
        ...state,
        countryBilling: Countries.find((item) => item?.code === props.user?.tenant_info?.country_code)?.name,
        countryShipping: Countries.find((item) => item?.code === props.user?.tenant_info?.country_code)?.name,
      };
    }
    return state;
  }

  componentDidMount() {
    const { selectedCustomer, getDocCFV2, user, getCurrencies, getTallyConfigurations } = this.props;

    const payload = {
      orgId: user?.tenant_info?.org_id,
      entityName: 'CUSTOMERS',
    };
    getCurrencies();
    getDocCFV2(payload);
    if (this.isTallyConnected()) {
      getTallyConfigurations({
        query: {
          org_id: user?.org_id,
          column_type: 'ADDRESS',
        },
      }, () => { });
    }

    if (selectedCustomer) {
      this.setState({
        selectedCurrencyID: selectedCustomer?.currency_info?.org_currency_id,
        customer: selectedCustomer?.customer_info,
        gst_treatment: selectedCustomer?.gst_treatment,
        customerName: selectedCustomer?.customer_info?.customer_name,
        legalName: selectedCustomer?.customer_info?.legal_name,
        contactPerson: selectedCustomer?.customer_info?.contact_person_name,
        customerTypeValue: (selectedCustomer?.customer_info?.customer_type === 'BUSINESS' ? 1 : 2),
        selectedGstAdjustmentType: selectedCustomer?.customer_info?.gst_treatment,
        initialSelectedGstAdjustmentType: selectedCustomer?.customer_info?.gst_treatment,
        customerType: selectedCustomer?.customer_info?.customer_type,
        gstin: selectedCustomer?.customer_info?.gst_number,
        billingAddress: selectedCustomer?.customer_info?.billing_address_details,
        shippingAddress: selectedCustomer?.customer_info?.shipping_address_details,
        pinCodeBilling: selectedCustomer?.customer_info?.billing_address_details?.postal_code,
        districtBilling: selectedCustomer?.customer_info?.billing_address_details?.city,
        addressBilling: selectedCustomer?.customer_info?.billing_address_details?.address1,
        stateBilling: selectedCustomer?.customer_info?.billing_address_details?.state,
        countryBilling: selectedCustomer?.customer_info?.billing_address_details?.country,
        pinCodeShipping: selectedCustomer?.customer_info?.shipping_address_details?.postal_code,
        districtShipping: selectedCustomer?.customer_info?.shipping_address_details?.city,
        addressShipping: selectedCustomer?.customer_info?.shipping_address_details?.address1,
        stateShipping: selectedCustomer?.customer_info?.shipping_address_details?.state,
        countryShipping: selectedCustomer?.customer_info?.shipping_address_details?.country,
        countryCode: String(selectedCustomer?.customer_info?.dial_code) || Helpers.getDialCode(user?.tenant_info?.country_code),
        phone: selectedCustomer?.customer_info?.phone_number,
        email: selectedCustomer?.customer_info?.email_address,
        customerId: selectedCustomer?.customer_info?.customer_id,
        billingAddressId: selectedCustomer?.customer_info?.billing_address_details?.address_id,
        shippingAddressId: selectedCustomer?.customer_info?.shipping_address_details?.address_id,
        accountManager: selectedCustomer?.customer_info?.account_manager_id,
        paymentTerms: selectedCustomer?.customer_info?.default_payment_terms?.due_days || 0,
        paymentRemarks: selectedCustomer?.customer_info?.default_payment_terms?.remark || '',
        billingAddressTallyType: selectedCustomer?.customer_info?.billing_address_details?.tally_address_type,
        shippingAddressTallyType: selectedCustomer?.customer_info?.shipping_address_details?.tally_address_type,
      });
    } else {
      this.setState({ countryCode: Helpers.getDialCode(user?.tenant_info?.country_code) });
    }
  }

  addCustomer = () => {
    this.setState({ formSubmitted: true });
    const {
      customerType, customerName, legalName, gstin, pinCodeBilling, districtBilling, addressBilling, addressShipping, districtShipping, stateShipping, pinCodeShipping, checkedAddress, shippingAddress, billingAddress, accountManager,
      stateBilling, phone, email, panNumber, contactPerson, customerId, customerTypeValue, cfCustomersDoc, selectedGstAdjustmentType, countryBilling, countryShipping, countryCode, selectedCurrencyID, paymentTerms, billingAddressTallyType, shippingAddressTallyType, paymentRemarks,
    } = this.state;
    const {
      user, callback, history, createCustomer, selectedCustomer, updateCustomer,
    } = this.props;

    if (customerName && (phone ? Helpers.validatePhoneNumber(phone) : true) && (email ? Helpers.validateEmail(email) : true) && legalName && ((customerTypeValue === 1 && (selectedGstAdjustmentType === 'REGISTERED' || selectedGstAdjustmentType === 'OVERSEAS')) ? Helpers.validateGstNumber(gstin) : true) && !cfCustomersDoc?.filter((customField) => customField.isActive && customField.isRequired && (customField?.fieldType === 'ATTACHMENT' ? !customField?.fieldValue?.length : !customField?.fieldValue))?.length
     && addressBilling && addressShipping) {
      if (!selectedCustomer && pinCodeBilling && districtBilling && addressBilling
        && stateBilling && pinCodeShipping && districtShipping && addressShipping && stateShipping) {
        // create
        const payload = {
          customer_name: customerName,
          customer_type: customerType,
          legal_name: legalName,
          alias_name: customerName,
          description: '',
          gst_number: customerType === 'BUSINESS' ? gstin : null,
          email_address: email,
          contact_person_name: contactPerson,
          phone_number: phone,
          dial_code: countryCode,
          pan_number: panNumber,
          tenant_id: user?.tenant_info?.tenant_id,
          is_verified: 'true',
          gst_treatment: selectedGstAdjustmentType,
          shipping_address_same_as_billing: !!checkedAddress,
          billing_address: {
            address1: addressBilling,
            city: districtBilling,
            state: stateBilling,
            country: countryBilling,
            postal_code: pinCodeBilling,
            tally_address_type: billingAddressTallyType,
          },
          shipping_address: {
            address1: addressShipping,
            city: districtShipping,
            state: stateShipping,
            country: countryShipping,
            postal_code: pinCodeShipping,
            tally_address_type: shippingAddressTallyType,
          },
          custom_fields: CustomFieldHelpers.postCfStructure(cfCustomersDoc),
          account_manager_id: accountManager || null,
          org_currency_id: selectedCurrencyID,
          default_payment_terms: {
            advance_amount: 0,
            due_days: paymentTerms,
            remark: paymentRemarks,
          },
        };
        createCustomer(payload, (createdCustomer) => {
          if (callback) callback(createdCustomer);
          else history.push('/sales/customer');
        });
      } else {
        // update
        const payload = {
          customer_id: customerId,
          customer_type: customerType,
          customer_name: customerName,
          legal_name: legalName,
          alias_name: customerName,
          gst_number: customerType === 'BUSINESS' ? gstin : null,
          gst_treatment: selectedGstAdjustmentType,
          email_address: email,
          contact_person_name: contactPerson,
          phone_number: phone,
          dial_code: countryCode,
          tenant_id: user?.tenant_info?.tenant_id,
          customer_status: 'ACTIVE',
          billing_address_id: billingAddress?.address_id,
          shipping_address_id: shippingAddress?.address_id,
          custom_fields: CustomFieldHelpers.postCfStructure(cfCustomersDoc),
          account_manager_id: accountManager || null,
          org_currency_id: selectedCurrencyID,
          default_payment_terms: {
            advance_amount: 0,
            due_days: paymentTerms,
            remark: paymentRemarks,
          },
        };
        updateCustomer(payload, (createdCustomer) => {
          if (callback) callback(createdCustomer);
          else history.push('/sales/customer');
        });
      }
    }
  }

  isTallyConnected = () => {

    const { user } = this.props;

    return !!user?.tenant_info?.it_id;
  }

  customInputChange(fieldValue, cfId) {
    const { cfCustomersDoc } = this.state;
    const newCustomField = cfCustomersDoc?.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url, type: attachment.type, name: attachment.name, uid: attachment.uid,
            })),

          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return {
        ...customField,
      };
    });
    this.setState({
      cfCustomersDoc: newCustomField,
    });
  }

  render() {
    const {
      getCityState, createCustomerLoading, selectedCustomer, getDocCFV2Loading, updateCustomerLoading, user, CurrenciesResults, getCurrenciesLoading, isUpdate, tallyConfigurations, tallyConfigurationsLoading,
    } = this.props;
    const {
      customerName, legalName, phone, email, addressBilling, selectedCurrencyID,
      districtBilling, stateBilling, formSubmitted, pinCodeBilling, cfCustomersDoc,
      gstin, shippingAddress, billingAddress, addressShipping, stateShipping, districtShipping, pinCodeShipping, checkedAddress, customerTypes, gstTreatmentTypes, selectedGstAdjustmentType, customerTypeValue, countryCode, accountManager,
      contactPerson, showAddressDrawer, selectedAddressType, countryBilling, countryShipping, initialSelectedGstAdjustmentType, paymentTerms, billingAddressTallyType, shippingAddressTallyType, paymentRemarks,
    } = this.state;

    const customer = user?.tenant_info?.sales_config?.sub_modules?.customer?.is_active;
    const restrictCustomerGSTUpdate = user?.tenant_info?.sales_config?.sub_modules?.customer?.settings?.restrict_customer_gst_update;

    return (
      <React.Fragment>
        <Drawer
          open={showAddressDrawer}
          onClose={() => this.setState({ showAddressDrawer: false, selectedAddressType: '' })}
          width="360"
          destroyOnClose
        >
          <AddressSelector
            title={selectedAddressType === 'TENANT_SHIPPING' ? 'Shipping Address' : 'Billing Address'}
            addressType={selectedAddressType?.split('_')[0]}
            selectedAddressId={selectedAddressType === 'TENANT_SHIPPING' ? shippingAddress?.address_id : billingAddress?.address_id}
            onAddressChange={(address) => {
              if (selectedAddressType === 'TENANT_SHIPPING') {
                this.setState({ shippingAddress: address, showAddressDrawer: false });
              } else if (selectedAddressType === 'TENANT_BILLING') {
                this.setState({ billingAddress: address, showAddressDrawer: false });
              }
            }}
            entityType="CUSTOMER"
            entityId={selectedCustomer?.customer_info?.customer_id}
          />
        </Drawer>
        {getDocCFV2Loading ? (
          <div>
            <div className="ant-row">
              {[1, 2, 3, 4, 5, 6]?.map((item, index) => (
                <div key={item} className="ant-col-md-12 ant-col-xs-24">
                  <div className={(index % 2 === 0 ? 'orgInputContainer product__right-input' : 'orgInputContainer product__left-input')}>
                    <div className="loadingBlock" style={{ height: '15px', marginBottom: '6px' }} />
                    <div className="loadingBlock" style={{ height: '32px' }} />
                  </div>
                </div>
              ))}
            </div>

          </div>
        )
          : (
            <div className="org-customer-create__wrapper" style={{ marginBottom: '40px' }}>
              <div className="ant-row">
                <div className="ant-col-md-24">
                  <div style={{ marginBottom: '9px' }}>
                    <div className="add-customer__form-section">Customer Type</div>
                    <Radio.Group
                      disabled={createCustomerLoading || updateCustomerLoading || (selectedCustomer && (initialSelectedGstAdjustmentType == 'REGISTERED_BUSINESS_REGULAR' || initialSelectedGstAdjustmentType == 'REGISTERED') && restrictCustomerGSTUpdate)}
                      onChange={(event) => {
                        // eslint-disable-next-line no-unused-expressions
                        event.target.value === 1
                          ? (this.setState({
                            customerType: customerTypes[event.target.value],
                            customerTypeValue: event.target.value,
                          })) : (this.setState({
                            customerType: customerTypes[event.target.value],
                            customerTypeValue: event.target.value,
                            selectedGstAdjustmentType: 'UNREGISTERED',
                          }));
                      }}
                      value={customerTypeValue}
                    >
                      <Radio value={1}>Business</Radio>
                      <Radio value={2}>Individual</Radio>
                    </Radio.Group>
                  </div>
                </div>

                <div className="ant-col-md-12 ant-col-xs-24">
                  <H3FormInput
                    name="customer name"
                    type="text"
                    containerClassName="orgInputContainer create__right-input"
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    placeholder=""
                    onChange={(event) => this.setState({ customerName: event.target.value })}
                    value={customerName}
                    disabled={createCustomerLoading || updateCustomerLoading}
                    label="Customer Name"
                    maxlength="100"
                    required
                    showError={formSubmitted && !customerName}
                  />
                </div>
                <div className="ant-col-md-12 ant-col-xs-24">
                  <H3FormInput
                    name="legal name"
                    type="text"
                    containerClassName="orgInputContainer create__left-input"
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    placeholder=""
                    disabled={createCustomerLoading || updateCustomerLoading}
                    onChange={(event) => this.setState({ legalName: event.target.value })}
                    value={legalName}
                    label="Legal / Trade Name"
                    maxlength="100"
                    required
                    showError={formSubmitted && !legalName}
                  />
                </div>
                <div className="ant-col-md-12 ant-col-xs-24 ">
                  <div className="" style={{ marginBottom: '9px' }}>
                    <div className="gst_treatment_input">
                      <div className="orgFormLabel">Customer Type</div>
                      {customerTypeValue === 1 && (
                        <PRZSelect
                          size="middle"
                          placeholder="Select GST Type"
                          value={selectedGstAdjustmentType}
                          onChange={(value) => {
                            this.setState({
                              addressBilling: '',
                              addressShipping: '',
                              pinCodeBilling: '',
                              pinCodeShipping: '',
                              districtBilling: '',
                              districtShipping: '',
                              selectedGstAdjustmentType: value,
                              customerCountry: '',
                              stateBilling: '',
                              stateShipping: '',
                            });
                          }}
                          disabled={createCustomerLoading || updateCustomerLoading || (selectedCustomer && (initialSelectedGstAdjustmentType == 'REGISTERED_BUSINESS_REGULAR' || initialSelectedGstAdjustmentType == 'REGISTERED') && restrictCustomerGSTUpdate)}
                        >
                          {gstTreatmentTypes?.map((adjustment) => (
                            <Option key={adjustment?.id} value={adjustment?.TreatmentType}>
                              {adjustment?.TreatmentType?.toProperCase()}
                            </Option>
                          ))}
                        </PRZSelect>
                      )}
                      {customerTypeValue === 2 && (
                        <PRZSelect
                          size="middle"
                          value="Unregistered"
                          disabled
                        />
                      )}
                    </div>

                  </div>
                </div>
                <div className="ant-col-md-12 ant-col-xs-24">
                  <H3FormInput
                    name="contact person name"
                    type="text"
                    containerClassName="orgInputContainer create__left-input"
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    placeholder=""
                    disabled={createCustomerLoading || updateCustomerLoading}
                    onChange={(event) => this.setState({ contactPerson: event.target.value })}
                    value={contactPerson}
                    label="Contact Person Name"
                    maxlength="50"
                  />
                </div>
                <div className="ant-col-md-12 ant-col-xs-24">
                  <div className="orgInputContainer create__right-input">
                    <H3Text
                      text="Phone"
                      className="orgFormLabel"
                    />
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <PRZSelect
                        value={countryCode}
                        showSearch
                        filterOption={(input, option) => {
                          const { label } = option.props;
                          const { value } = option.props;
                          return (
                            label.toLowerCase().includes(input.toLowerCase())
                            || value.toLowerCase().includes(input.toLowerCase())
                          );
                        }}
                        onChange={(value) => this.setState({ countryCode: value })}
                        className={formSubmitted && !countryCode ? "prz-select__error-border" : ""}
                        style={{
                          borderRight: 'none',
                          borderRadius: '2px',
                          height: '28px',
                          width: '90px',
                          fontSize: '13px',
                        }}
                        disabled={createCustomerLoading || updateCustomerLoading}
                      >
                        {CountryCodes?.map((item) => (
                          <Option
                            key={item?.name}
                            value={item?.dial_code}
                            label={item?.name}
                          >
                            {`${item?.flag} +${item?.dial_code}`}
                          </Option>
                        ))}
                      </PRZSelect>
                      <H3FormInput
                        name="a valid phone number"
                        type="text"
                        containerClassName="create__right-right-input"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        placeholder=""
                        onChange={(event) => this.setState({ phone: event.target.value })}
                        value={phone}
                        disabled={createCustomerLoading || updateCustomerLoading}
                        maxlength="50"
                      />
                    </div>
                    {(formSubmitted && phone && !Helpers.validatePhoneNumber(phone)) && (
                      <div className="input-error">
                        *Please enter valid phone number
                      </div>
                    )}
                  </div>
                </div>
                <div className="ant-col-md-12 ant-col-xs-24">
                  <H3FormInput
                    name="a valid email"
                    type="text"
                    containerClassName="orgInputContainer create__left-input"
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    placeholder=""
                    onChange={(event) => this.setState({ email: event.target.value })}
                    value={email}
                    disabled={createCustomerLoading || updateCustomerLoading}
                    label="Email"
                    maxlength="100"
                    showError={formSubmitted && email && !Helpers.validateEmail(email)}
                  />
                </div>
                <div className="ant-col-md-12 ant-col-xs-24 ">
                  <SelectAppUser
                    title="Sales Manager"
                    containerClassName="orgInputContainer create__right-input"
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    selectedUser={accountManager}
                    onChange={(value) => this.setState({ accountManager: value })}
                  />
                </div>
                <div className="ant-col-md-12 ant-col-xs-24">
                  <div className="orgInputContainer  create__left-input">
                    <div className="orgFormLabel">
                      Currencies
                      <span style={{
                        color: 'red',
                        marginLeft: '2px',
                      }}
                      >
                        *
                      </span>

                    </div>
                    <div className="add-seller__form-type">
                      <PRZSelect
                        onChange={(value) => {
                          this.setState({
                            selectedCurrencyID: value,
                          });
                        }}
                        value={selectedCurrencyID}
                        disabled={(selectedCustomer?.ledger_count || 0) !== 0}
                        loading={getCurrenciesLoading}
                        options={CurrenciesResults?.map((currency) => ({
                          value: currency?.org_currency_id, // Use a unique identifier as the value
                          label: `${currency.currency_code} (${currency.currency_symbol})`,
                        }))}
                      />
                    </div>
                  </div>
                </div>
                <div className="ant-col-md-12 ant-col-xs-24">
                  <div className="add-seller__form-type" style={{ marginBottom: '9px' }}>
                    <SelectPaymentTerm
                      selectedPaymentTerm={paymentTerms}
                      onChange={(value) => {
                        this.setState({ paymentTerms: value?.due_days });
                      }}
                      callback={(value) => {
                        this.setState({ paymentTerms: Number(value) });
                      }}
                      containerClassName="create__right-input"
                      labelClassName="orgFormLabel"
                      showError={formSubmitted && !paymentTerms}
                      disabled={createCustomerLoading || updateCustomerLoading}
                      showAddPaymentTerm
                      placeholder="Select Payment Term"
                    />
                  </div>
                </div>
                <div className="ant-col-md-12 ant-col-xs-24">
                  <div className="add-seller__form-type" style={{ marginBottom: '9px' }}>
                    <SelectPaymentRemark
                      selectedPaymentRemark={paymentRemarks}
                      onChange={(value) => {
                        this.setState({ paymentRemarks: value?.message });
                      }}
                      callback={(value) => {
                        this.setState({ paymentRemarks: value });
                      }}
                      containerClassName="create__left-input"
                      labelClassName="orgFormLabel"
                      showError={formSubmitted && !paymentRemarks}
                      disabled={createCustomerLoading || updateCustomerLoading}
                      showAddPaymentRemark
                      placeholder="Select Payment Term"
                    />
                  </div>
                </div>
                {(customerTypeValue === 1 && selectedGstAdjustmentType === 'REGISTERED')
                  && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <H3FormInput
                        name="a valid GST number"
                        type="text"
                        containerClassName="orgInputContainer create__right-input"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        placeholder=""
                        onChange={(event) => this.setState(
                          { gstin: event.target.value?.trim(), panNumber: event.target.value?.substr(2, 10) },
                        )}
                        disabled={createCustomerLoading || updateCustomerLoading || ((initialSelectedGstAdjustmentType == 'REGISTERED_BUSINESS_REGULAR' || initialSelectedGstAdjustmentType == 'REGISTERED') && gstin && restrictCustomerGSTUpdate)}
                        value={gstin}
                        required
                        showError={formSubmitted && !Helpers.validateGstNumber(gstin)}
                        label="GST Number"
                      />
                    </div>
                  )}
                {(customerTypeValue === 1 && selectedGstAdjustmentType === 'OVERSEAS')
                  && (
                    <div className="ant-col-md-12 ant-col-xs-24">
                      <H3FormInput
                        name="a valid GST number"
                        type="text"
                        containerClassName="orgInputContainer create__right-input"
                        labelClassName="orgFormLabel"
                        inputClassName="orgFormInput"
                        placeholder=""
                        onChange={(event) => this.setState(
                          { gstin: event.target.value?.trim(), panNumber: event.target.value?.substr(2, 10) },
                        )}
                        disabled={createCustomerLoading || updateCustomerLoading || (customerTypeValue == 1 && restrictCustomerGSTUpdate)}
                        value={gstin}
                        showError={formSubmitted && gstin && !Helpers.validateGstNumber(gstin)}
                        label="GST Number"
                      />
                    </div>
                  )}
                <div className="ant-col-md-24" />
                {!selectedCustomer
                  ? (
                    <React.Fragment>
                      <div className="ant-col-md-24">
                        <div className="add-customer__form-section">Billing Address</div>
                      </div>
                      <div className="ant-col-md-24">
                        <H3FormInput
                          name="address"
                          type="text"
                          containerClassName="orgInputContainer"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => {
                            if (checkedAddress) {
                              this.setState({ addressBilling: event.target.value, addressShipping: event.target.value });
                            } else {
                              this.setState({ addressBilling: event.target.value });
                            }
                          }}
                          value={addressBilling}
                          label="Street"
                          disabled={createCustomerLoading}
                          showError={formSubmitted && !addressBilling}
                          required
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <H3FormInput
                          name="a valid pincode"
                          type="number"
                          containerClassName="orgInputContainer create__right-input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => {
                            if (event.target.value.length <= 6) {
                              if (checkedAddress) {
                                this.setState({ pinCodeBilling: event.target.value, pinCodeShipping: event.target.value });
                              } else {
                                this.setState({ pinCodeBilling: event.target.value });
                              }
                            }
                          }}
                          onBlur={(event) => ((selectedGstAdjustmentType !== 'OVERSEAS' && event.target.value.length <= 6) ? getCityState(event.target.value) : '')}
                          value={pinCodeBilling}
                          label="Pin Code"
                          disabled={createCustomerLoading}
                          required
                          showError={formSubmitted && !pinCodeBilling}
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <H3FormInput
                          name="district"
                          type="text"
                          containerClassName="orgInputContainer create__mid-input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => {
                            if (checkedAddress) {
                              this.setState({ districtBilling: event.target.value, districtShipping: event.target.value });
                            } else {
                              this.setState({ districtBilling: event.target.value });
                            }
                          }}
                          value={districtBilling}
                          label="District"
                          disabled={createCustomerLoading}
                          showError={formSubmitted && !districtBilling}
                          required
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <H3FormInput
                          name="state"
                          type="text"
                          containerClassName="orgInputContainer create__left-input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => {
                            if (checkedAddress) {
                              this.setState({ stateBilling: event.target.value, stateShipping: event.target.value });
                            } else {
                              this.setState({ stateBilling: event.target.value });
                            }
                          }}
                          value={stateBilling}
                          label="State"
                          disabled={createCustomerLoading}
                          showError={formSubmitted && !stateBilling}
                          required
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <div className="orgInputContainer  create__left-input">
                          <div className="orgFormLabel">
                            Country
                            <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
                          </div>
                          <div className="add-seller__form-type">
                            <PRZSelect
                              disabled={createCustomerLoading || updateCustomerLoading}
                              value={countryBilling}
                              showSearch
                              style={{
                                marginTop: '1px',
                                borderRadius: '2px',
                                width: '100%',
                                height: '28px',
                              }}
                              onChange={(value) => {
                                this.setState({
                                  countryBilling: value,
                                });
                              }}
                            >
                              {Countries?.map((country) => <Option key={country.code} value={country.name}>{country.name?.toProperCase()}</Option>)}
                            </PRZSelect>
                          </div>
                        </div>
                      </div>
                      {this.isTallyConnected() && (
                        <div className="ant-col-md-6">
                          <div className="orgInputContainer  create__left-input">
                            <div className="orgFormLabel">
                              Tally Address Type
                            </div>
                            <div className="add-seller__form-type">
                              <PRZSelect
                                disabled={createCustomerLoading || updateCustomerLoading || tallyConfigurationsLoading}
                                value={billingAddressTallyType}
                                showSearch
                                onChange={(value) => {
                                  if (checkedAddress) {
                                    this.setState({ billingAddressTallyType: value, shippingAddressTallyType: value });
                                  } else {
                                    this.setState({ billingAddressTallyType: value });
                                  }
                                }}
                              >
                                {tallyConfigurations?.tally_address_types?.map((item) => (
                                  <Option key={item?.address_type}>
                                    {item?.adddress_type}
                                  </Option>
                                ))}
                              </PRZSelect>
                            </div>
                          </div>
                        </div>
                      )}
                      <div className="ant-col-md-24">
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div className="add-customer__form-section">Shipping Address</div>
                          <div className="add-customer__form-section">
                            <div className="add-customer__form-section-same">
                              <div className="add-customer__form-section-same-text">Same as Billing Address</div>
                              <Checkbox
                                disabled={createCustomerLoading}
                                onChange={(event) => {
                                  if (checkedAddress) {
                                    this.setState(
                                      {
                                        checkedAddress: event.target.checked,
                                        addressShipping: '',
                                        pinCodeShipping: '',
                                        stateShipping: '',
                                        districtShipping: '',
                                        shippingAddressTallyType: '',
                                      },
                                    );
                                  } else {
                                    this.setState(
                                      {
                                        checkedAddress: event.target.checked,
                                        addressShipping: addressBilling,
                                        pinCodeShipping: pinCodeBilling,
                                        stateShipping: stateBilling,
                                        districtShipping: districtBilling,
                                        shippingAddressTallyType: billingAddressTallyType,
                                      },
                                    );
                                  }
                                }}
                              />
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="ant-col-md-24">
                        <H3FormInput
                          name="shipping address"
                          type="text"
                          containerClassName="orgInputContainer"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => this.setState({ addressShipping: event.target.value })}
                          value={addressShipping}
                          label="Street"
                          disabled={createCustomerLoading || checkedAddress}
                          showError={formSubmitted && !addressShipping}
                          required
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <H3FormInput
                          name="a valid shipping pincode"
                          type="number"
                          containerClassName="orgInputContainer create__right-input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => ((selectedGstAdjustmentType !== 'OVERSEAS' && event.target.value.length <= 6) ? this.setState({ pinCodeShipping: event.target.value, pinCodeShippingChange: true }) : '')}
                          onBlur={(event) => ((selectedGstAdjustmentType !== 'OVERSEAS' && event.target.value.length <= 6) ? getCityState(event.target.value) : '')}
                          value={pinCodeShipping}
                          label="Pin Code"
                          disabled={createCustomerLoading || checkedAddress}
                          required
                          showError={formSubmitted && !pinCodeShipping}
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <H3FormInput
                          name="shipping district"
                          type="text"
                          containerClassName="orgInputContainer create__mid-input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => this.setState({ districtShipping: event.target.value })}
                          value={districtShipping}
                          label="District"
                          disabled={createCustomerLoading || checkedAddress}
                          showError={formSubmitted && !districtShipping}
                          required
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <H3FormInput
                          name="shipping state"
                          type="text"
                          containerClassName="orgInputContainer create__left-input"
                          labelClassName="orgFormLabel"
                          inputClassName="orgFormInput"
                          placeholder=""
                          onChange={(event) => this.setState({ stateShipping: event.target.value })}
                          value={stateShipping}
                          label="State"
                          disabled={createCustomerLoading || checkedAddress}
                          showError={formSubmitted && !stateShipping}
                          required
                        />
                      </div>
                      <div className="ant-col-md-6">
                        <div className="orgInputContainer  create__left-input">
                          <div className="orgFormLabel">
                            Country
                            <span style={{ color: 'red', marginLeft: '2px' }}>*</span>
                          </div>
                          <div className="add-seller__form-type">
                            <PRZSelect
                              disabled={createCustomerLoading || updateCustomerLoading}
                              value={countryBilling}
                              showSearch
                              onChange={(value) => {
                                this.setState({
                                  countryShipping: value,
                                });
                              }}
                            >
                              {Countries?.map((country) => <Option key={country.code} value={country.name}>{country.name?.toProperCase()}</Option>)}

                            </PRZSelect>
                          </div>
                        </div>
                      </div>
                      {this.isTallyConnected() && (
                        <div className="ant-col-md-6">
                          <div className="orgInputContainer  create__left-input">
                            <div className="orgFormLabel">
                              Tally Address Type
                            </div>
                            <div className="add-seller__form-type">
                              <PRZSelect
                                disabled={createCustomerLoading || updateCustomerLoading || tallyConfigurationsLoading}
                                value={shippingAddressTallyType}
                                showSearch
                                onChange={(value) => {
                                  this.setState({
                                    shippingAddressTallyType: value,
                                  });
                                }}
                              >
                                {tallyConfigurations?.tally_address_types?.map((item) => (
                                  <Option key={item?.address_type}>
                                    {item?.adddress_type}
                                  </Option>
                                ))}
                              </PRZSelect>
                            </div>
                          </div>
                        </div>
                      )}
                    </React.Fragment>
                  )
                  : (
                    <React.Fragment>
                      <div />
                    </React.Fragment>
                  )}
                {cfCustomersDoc?.length > 0
                  && (
                    <CustomFieldV2
                      customFields={cfCustomersDoc || []}
                      formSubmitted={formSubmitted}
                      customInputChange={(value, cfId) => this.customInputChange(value, cfId)}
                      disableCase={createCustomerLoading || updateCustomerLoading}
                      isUpdate={isUpdate}
                    />
                  )}

              </div>
              <div className="custom-drawer__footer">
                <div className="ant-col-md-6" style={{ marginTop: '0.5rem' }}>
                  <div className="indent-barcode__wrapper" style={{ display: 'flex' }}>
                    <H3Button
                      buttonType={defaultButtonTypes.BLUE_ROUNDED}
                      text={selectedCustomer ? 'Update Customer' : 'Add Customer'}
                      onClick={() => this.addCustomer()}
                      isLoading={createCustomerLoading || updateCustomerLoading}
                      disabled={createCustomerLoading || updateCustomerLoading || !customer}
                      style={{
                        width: '160px',
                        borderRadius: '5px',
                        padding: '7px 0px',
                        marginLeft: '0',
                      }}
                    />
                    {!customer && (
                      <Popconfirm
                        placement="topRight"
                        title="This feature is not accessible within your current plan to use this feature contact us."
                        onConfirm={() => window.Intercom('showNewMessage')}
                        okText="Contact Us"
                        cancelText="Cancel"
                      >
                        <img
                          className="barcode-restrict"
                          src={cdnUrl("crown2.png", "images")}
                          alt="premium"
                          style={{
                            marginLeft: '8px',
                            marginTop: '5px',
                          }}
                        />
                      </Popconfirm>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

      </React.Fragment>
    );
  }
}
const mapStateToProps = ({
  UserReducers, CustomerReducers, TenantReducers, SellerReducers, CFV2Reducers, CurrenciesReducers, GetTallyConfigurations,
}) => ({
  user: UserReducers.user,
  cityStateLoading: SellerReducers.cityStateLoading,
  pincodeCity: SellerReducers.pincodeCity,
  pincodeState: SellerReducers.pincodeState,
  setCityStateError: SellerReducers.setCityStateError,
  createCustomerLoading: CustomerReducers.createCustomerLoading,
  updateCustomerLoading: CustomerReducers.updateCustomerLoading,
  tenants: TenantReducers.tenants,
  cfV2DocCustomers: CFV2Reducers.cfV2DocCustomers,
  CurrenciesResults: CurrenciesReducers.CurrenciesResults,
  tallyConfigurations: GetTallyConfigurations.data,
  tallyConfigurationsLoading: GetTallyConfigurations.loading,
});

const mapDispatchToProps = (dispatch) => ({
  setCityState: (city, state) => dispatch(SellerActions.setCityState(city, state)),
  getCityState: (pincode) => dispatch(SellerActions.getCityState(pincode)),
  createCustomer: (payload, callback) => dispatch(CustomerActions.createCustomer(payload, callback)),
  updateCustomer: (payload, callback) => dispatch(CustomerActions.updateCustomer(payload, callback)),
  getDocCFV2: (payload, callback) => dispatch(CFV2Actions.getDocCFV2(payload, callback)),
  getDocCFV2Success: (customFields) => dispatch(CFV2Actions.getDocCFV2Success(customFields)),
  getCurrencies: (orgId) => dispatch(CurrenciesActions.getCurrencies(orgId)),
  getTallyConfigurations: (payload, callback) => dispatch(GetTallyConfigurations.actions.request(payload, callback)),
});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(CustomerForm));
