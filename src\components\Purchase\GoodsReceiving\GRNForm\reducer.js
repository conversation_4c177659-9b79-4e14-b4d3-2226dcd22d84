/* eslint-disable unicorn/prefer-structured-clone */
import dayjs from 'dayjs';
import { notification } from 'antd';
import Helpers from '@Apis/helpers';
import { QUANTITY } from '@Apis/constants';
import { computeUpdatedLineCFs, createData, getLineTotals } from './helpers';
import { v4 as uuidv4 } from 'uuid';

function applyDiscount(copyData, discountType, grnTypeValue, value, billFromAddress, billToAddress) {
  if (grnTypeValue !== 'ADHOC') {
    copyData.map((item) => {
      const totalValue = copyData?.reduce((acc, cur) => acc + (cur.received_qty * cur.offer_price), 0);

      const discountValue = discountType === 'Percent'
        ? Number.parseFloat(value)
        : ((item.received_qty * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(value);

      const taxableValue = discountType === 'Percent'
        ? (item.received_qty * item.offer_price) * (1 - discountValue / 100)
        : Math.max(item.received_qty * item.offer_price - discountValue, 0);

      item.discount = discountValue;
      item.child_taxes = Helpers.computeTaxation(
        taxableValue,
        item?.taxInfo,
        billFromAddress,
        billToAddress
      )?.tax_info?.child_taxes;

      return item;
    });
  } else {
    copyData.map((item) => {
      const totalValue = copyData?.reduce((acc, cur) => acc + (cur.quantity * cur.offer_price), 0);

      const discountValue = discountType === 'Percent'
        ? Number.parseFloat(value)
        : ((item.quantity * item.offer_price) / Number.parseFloat(totalValue)) * Number.parseFloat(value);

      const taxableValue = discountType === 'Percent'
        ? (item.quantity * item.offer_price) * (1 - discountValue / 100)
        : Math.max(item.quantity * item.offer_price - discountValue, 0);

      item.discount = discountValue;
      item.child_taxes = Helpers.computeTaxation(
        taxableValue,
        item?.taxInfo,
        billFromAddress,
        billToAddress
      )?.tax_info?.child_taxes;

      return item;
    });
  }

  return { data: copyData, discountPercentage: Number.parseFloat(value) };
}

export const initialState = {
  // Dates
  grnDate: dayjs(),
  invoiceDate: dayjs(),
  dueDate: dayjs(),

  // GRN / PO
  grnTypeValue: 'Purchase Order',
  data: [],
  chargeData: [],
  toggleRejectedBatches: false,
  showModal: false,
  getHistory: '',
  latestGrnId: '',
  selectedPoValue: [],
  discountPercentage: 0,
  selectedPo: null,
  vendorAddress: null,
  formSubmitted: false,

  // Tax & Currency
  taxTypeName: 'TDS',
  selectedCurrencyInfo: '',
  selectedCurrencyID: '',
  charge1Name: 'Freight',
  charge1Value: '',
  discountType: 'Percent',
  freightTaxId: 'Not Applicable',
  freightTax: null,
  openFreightTax: false,
  isAutomaticConversionRate: null,
  currencyConversionRate: '',
  freightTaxData: {
    child_taxes: [
      {
        tax_amount: 0,
        tax_type_name: '',
      },
    ],
  },

  // Stock / Validation
  allStock: true,
  isGRNLevelDiscount: false,
  isMultipleBatchModeEnabled: false,
  multipleBatchValidation: false,
  isBatchValid: false,
  visibleLineCfs: [],
  batchNumbers: new Map([]),
  availableBatches: [],
  selectedLineInfo: {},

  // Tenant & Integration
  tenantDepartmentId: '',
  selectedTenant: null,
  selectedTenantTallyIntegrationId: null,

  // Misc
  updateDocumentReason: '',
  narration: '',
  paymentTerms: 0,
  paymentRemarks: '',
  isAdhocGrnAllowed: false,
};

export const ACTIONS = {
  UPDATE_FIELD: 'UPDATE_FIELD', // generic update for single field
  UPDATE_FIELDS: 'UPDATE_FIELDS', // bulk update (multiple fields at once)
  UPDATE_DATA: 'UPDATE_DATA', // update data
  RESET_FORM: 'RESET_FORM', // reset to initialState
  INIT_STATE: 'INIT_STATE', // initialize with external data (props, API)
  DELETE_ITEM: 'DELETE_GRN_ITEM', // delete grn item and recalculate discount if needed
  DELETE_CHARGE: 'DELETE_CHARGE', // delete charge and recalculate discount if needed
  HANDLE_FULL_QUANTITY: 'HANDLE_FULL_QUANTITY',
  RECORD_FULL_QUANTITY: 'RECORD_FULL_QUANTITY',
  REMOVE_ZERO_QUANTITY: 'REMOVE_ZERO_QUANTITY',
  UPDATE_TABLE_VALUE: 'UPDATE_TABLE_VALUE',
  CUSTOM_INPUT_CHANGE: 'CUSTOM_INPUT_CHANGE',
  CUSTOM_LINE_INPUT_CHANGE: 'CUSTOM_LINE_INPUT_CHANGE',
  HANDLE_PRODUCT_CHANGE: 'HANDLE_PRODUCT_CHANGE',
  HANDLE_PRODUCT_CHANGE_VALUE: 'HANDLE_PRODUCT_CHANGE_VALUE',
  ADD_NEW_ROW: 'ADD_NEW_ROW',
  TOGGLE_BATCH_INNER: 'TOGGLE_BATCH_INNER',
  TOGGLE_BATCH: 'TOGGLE_BATCH',
  ADD_NEW_CHARGE_ROW: 'ADD_NEW_CHARGE_ROW',
  ADD_NEW_BATCH: 'ADD_NEW_BATCH',
  REMOVE_BATCH: 'REMOVE_BATCH',
  SET_SELECTED_BATCH: 'SET_SELECTED_BATCH',
  UPDATE_LANDED_COST: 'UPDATE_LANDED_COST',
  CUSTOM_FIELD_VISIBILITY_CHANGE: 'CUSTOM_FIELD_VISIBILITY_CHANGE',
  BULK_UPLOAD_BATCH: 'BULK_UPLOAD_BATCH',
  UPDATE_BATCH_CFS: 'UPDATE_BATCH_CFS',
  UPDATE_BATCH_CFS_FOR_MULTI_BATCH_MODE: 'UPDATE_BATCH_CFS_FOR_MULTI_BATCH_MODE',
};

export const formReducer = (state, action) => {
  switch (action.type) {
  case ACTIONS.UPDATE_FIELD: {
    return {
      ...state,
      [action.field]: action.value,
    };
  }

  case ACTIONS.UPDATE_FIELDS: {
    return {
      ...state,
      ...action.payload, // payload = { field1: value1, field2: value2 }
    };
  }

  case ACTIONS.UPDATE_DATA: {
    return {
      ...state,
      data: action.payload,
    };
  }

  case ACTIONS.INIT_STATE: {
    return {
      ...state,
      ...action.payload,
    };
  }

  case ACTIONS.DELETE_ITEM: {
    const { key, id, billFromAddress, billToAddress } = action.payload;
    const { data, selectedPoValue, isGRNLevelDiscount, discountPercentage, discountType, grnTypeValue } = state;

    if (data.length <= 1) {
      notification.error({
        message: 'You need to have at least one item in the GRN.',
        duration: 4,
        placement: 'top',
      });
      return state;
    }

    const copyData = data.filter(
      (item) => item.key !== key || item.grn_line_id !== id
    );

    const updatedSelectedPoValue = selectedPoValue?.filter((item) =>
      copyData?.some((data) => data?.po_id === item?.value)
    );

    let newState = {
      ...state,
      data: copyData,
      selectedPoValue: updatedSelectedPoValue,
    };

    if (isGRNLevelDiscount) {
      const { data: discountedData, discountPercentage: dp } = applyDiscount(
        JSON.parse(JSON.stringify(copyData)), // pass the *new* data
        discountType,
        grnTypeValue,
        discountPercentage,
        billFromAddress,
        billToAddress
      );

      newState = {
        ...newState,
        data: discountedData,
        discountPercentage: dp,
      };
    }

    return newState;
  }

  case ACTIONS.DELETE_CHARGE: {
    const { chargeKey } = action.payload;
    return {
      ...state,
      chargeData: state?.chargeData.filter(
        (item) => item.chargeKey !== chargeKey
      ),
    };
  }

  case ACTIONS.HANDLE_FULL_QUANTITY: {
    const { item, billFromAddress, billToAddress } = action.payload;

    // guard: don’t update if negative
    if (Number(item?.quantity) - Number(item?.total_received_qty) < 0) {
      return state;
    }

    const updatedData = state?.data.map((obj) => {
      if (!item?.grn_line_id && (obj?.po_line_id === item?.po_line_id) && item?.po_line_id) {
        const discountValue = Number(item?.discount) || 0;
        const totalPrice = ((Number(item?.quantity) - Number(item?.total_received_qty)) * item.offer_price);

        const taxableValue = item?.lineDiscountType === 'Percent'
          ? totalPrice * (1 - discountValue / 100)
          : Math.max(totalPrice - discountValue, 0);

        return {
          ...obj,
          received_qty: Number(item?.quantity) - Number(item?.total_received_qty),
          child_taxes: Helpers.computeTaxation(
            taxableValue,
            item?.taxInfo,
            billFromAddress,
            billToAddress
          )?.tax_info?.child_taxes,
        };
      }
      return obj;
    });

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.RECORD_FULL_QUANTITY: {
    const { billFromAddress, billToAddress } = action.payload;

    const updatedData = state.data.map((item) => {
      if (Number(item?.quantity) - Number(item?.total_received_qty) >= 0 && item.po_line_id) {
        const discountValue = Number(item?.discount) || 0;
        const totalPrice = (Number(item?.quantity) - Number(item?.total_received_qty)) * item.offer_price;

        const taxableValue = item?.lineDiscountType === 'Percent'
          ? totalPrice * (1 - discountValue / 100)
          : Math.max(totalPrice - discountValue, 0);

        return {
          ...item,
          received_qty: QUANTITY(
            Number(item?.ordered_qty || item?.quantity) - Number(item?.total_received_qty || 0),
            item?.uom_info?.[0]?.precision
          ),
          child_taxes: Helpers.computeTaxation(
            taxableValue,
            item?.taxInfo,
            billFromAddress,
            billToAddress
          )?.tax_info?.child_taxes,
        };
      }
      return item;
    });

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.REMOVE_ZERO_QUANTITY: {
    const updatedData = state?.data.filter(
      (item) => !(item?.received_qty === 0 || item?.received_qty === '')
    );

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.UPDATE_TABLE_VALUE: {
    const { key, value, label } = action.payload;

    const updatedData = state?.data?.map((obj) => {
      if (obj?.po_line_id === key && obj?.po_line_id) {
        return {
          ...obj,
          [label]: value,
        };
      }
      return obj;
    });

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.CUSTOM_INPUT_CHANGE: {
    const { fieldValue, cfId } = action.payload;
    const newCustomField = state?.cfGoodReceivingNotesDoc.map((customField) => {
      if (customField?.cfId === cfId) {
        if (customField?.fieldType === 'ATTACHMENT') {
          return {
            ...customField,
            fieldValue: fieldValue?.map((attachment) => ({
              url: attachment?.response?.response?.location || attachment?.url,
              type: attachment.type,
              name: attachment.name,
              uid: attachment.uid,
            })),
          };
        }
        return {
          ...customField,
          fieldValue,
        };
      }
      return customField;
    });

    return {
      ...state,
      cfGoodReceivingNotesDoc: newCustomField,
    };
  }

  case ACTIONS.CUSTOM_LINE_INPUT_CHANGE: {
    const {
      fieldValue,
      cfId,
      record,
      discountPercentage,
      isGRNLevelDiscount,
      grnTypeValue,
      billFromAddress,
      billToAddress,
    } = action.payload;

    // update matching row’s lineCustomFields and mirror Quantity/Rate into row fields
    const nextData = state.data.map((row) => {
      const isMatch =
          (record?.po_line_id && row.po_line_id === record.po_line_id) ||
          (record?.grn_line_id && row.grn_line_id === record.grn_line_id) ||
          (record?.key && row.key === record.key);

      if (!isMatch) return row;

      const lineCfs = computeUpdatedLineCFs(
        row.lineCustomFields ?? [],
        cfId,
        fieldValue,
      );

      const keyForQty = grnTypeValue !== 'ADHOC' ? 'received_qty' : 'quantity';
      const qtyFromCf = lineCfs?.find((cf) => cf?.fieldName === 'Quantity')?.fieldValue;
      const rateFromCf = lineCfs?.find((cf) => cf?.fieldName === 'Rate')?.fieldValue;
      const invQtyFromCf = lineCfs?.find((cf) => cf?.fieldName === 'Invoice Quantity')?.fieldValue;

      return {
        ...row,
        lineCustomFields: lineCfs,
        ...(qtyFromCf !== undefined ? { [keyForQty]: qtyFromCf } : null),
        ...(rateFromCf !== undefined ? { offer_price: rateFromCf } : null),
        ...(invQtyFromCf !== undefined ? { invoiceQuantity: invQtyFromCf } : null),
      };
    });

    // optionally re-apply discount across rows when line-wise discount is enabled
    if (isGRNLevelDiscount) {
      const { data: discountedData, discountPercentage: dp } = applyDiscount(
        JSON.parse(JSON.stringify(nextData)),
        state.discountType,
        grnTypeValue,
        discountPercentage ?? state.discountPercentage,
        billFromAddress,
        billToAddress
      );
      return {
        ...state,
        data: discountedData,
        discountPercentage: dp,
      };
    }

    return { ...state, data: nextData };
  }

  case ACTIONS.HANDLE_PRODUCT_CHANGE: {
    const {
      tenantSku,
      key,
      productData,
      isMultiMode,
      callback,
      user,
      inventoryLocations,
      cfV2DocGoodReceivingNotes,
      taxesGroup,
      selectedPoForGrn,
      billFromAddress,
      billToAddress,
    } = action.payload;

    const { isGRNLevelDiscount, discountPercentage, selectedSeller, discountType, visibleLineCfs, data, grnTypeValue } = state;

    if (isMultiMode && Array.isArray(tenantSku)) {
      const copyData = tenantSku.map((sku) => {
        const newData = { key: uuidv4(), lineCustomFields: visibleLineCfs };
        const productSKUData = productData?.find(
          (item) => item?.product_sku_id === sku?.product_sku_id
        );

        return createData({
          tenantSku: sku,
          dataItem: newData,
          productData: productSKUData,
          inventoryLocations,
          selectedSeller,
          isGRNLevelDiscount,
          discountPercentage,
          autoPrintDescription:
              user?.tenant_info?.global_config?.settings?.print_description_automatically,
          discountType,
          cfV2DocGoodReceivingNotes,
          taxesGroup,
          selectedPoForGrn,
          user,
          billFromAddress,
          billToAddress,
        });
      });

      if (callback) callback();

      if (isGRNLevelDiscount) {
        const newData = [...state.data, ...copyData];
        const { data: discountedData, discountPercentage: dp } = applyDiscount(
          newData,
          discountType,
          grnTypeValue,
          discountPercentage,
          billFromAddress,
          billToAddress
        );
        return {
          ...state,
          data: discountedData,
          discountPercentage: dp,
          inventoryLocationId: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
            ?.inventory_location_id,
          inventoryLocationPath: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
            ?.inventory_location_path,
        };
      }
      return {
        ...state,
        data: [...state.data, ...copyData],
        inventoryLocationId: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
          ?.inventory_location_id,
        inventoryLocationPath: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
          ?.inventory_location_path,
      };
    } else {
      const copyData = JSON.parse(JSON.stringify(data));
      for (let i = 0; i < copyData?.length; i++) {
        if (copyData[i].key === key) {
          copyData[i] = createData({
            tenantSku,
            dataItem: copyData[i],
            productData,
            inventoryLocations,
            selectedSeller,
            isGRNLevelDiscount,
            discountPercentage,
            autoPrintDescription:
                user?.tenant_info?.global_config?.settings?.print_description_automatically,
            discountType,
            cfV2DocGoodReceivingNotes,
            taxesGroup,
            selectedPoForGrn,
            user,
          });
        }
      }

      if (isGRNLevelDiscount) {
        const { data: discountedData, discountPercentage: dp } = applyDiscount(
          JSON.parse(JSON.stringify(copyData)),
          discountType,
          grnTypeValue,
          discountPercentage,
          billFromAddress,
          billToAddress
        );
        return {
          ...state,
          data: discountedData,
          discountPercentage: dp,
          inventoryLocationId: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
            ?.inventory_location_id,
          inventoryLocationPath: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
            ?.inventory_location_path,
        };
      }

      return {
        ...state,
        data: copyData,
        inventoryLocationId: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
          ?.inventory_location_id,
        inventoryLocationPath: inventoryLocations?.inventory_location?.filter((i) => i?.is_active)?.[0]
          ?.inventory_location_path,
      };
    }
  }

  case ACTIONS.HANDLE_PRODUCT_CHANGE_VALUE: {
    const { value, key } = action.payload;

    const updatedData = state?.data?.map((item) =>
      item?.key === key
        ? { ...item, product_sku_name: value }
        : item
    );

    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.ADD_NEW_ROW: {

    const newRow = {
      key: uuidv4(),
      asset1: '',
      product_sku_name: '',
      quantity: 0,
      selling_price: 0,
      offer_price: 0,
      received_qty: 0,
      taxId: '',
      lot: '',
      product_sku_id: null,
      product_sku_info: null,
      child_taxes: [
        {
          tax_amount: 0,
          tax_type_name: '',
        },
      ],
      lineCustomFields: state?.cfGoodReceivingNotesLine,
      is_adhoc_line: true,
      discount: 0,
    };

    return {
      ...state,
      data: [...state.data, newRow],
    };
  }

  case ACTIONS.TOGGLE_BATCH_INNER: {
    const { record, adjustmentRow } = action.payload;
    const copyData = state?.data?.map((item) => {
      if (item.key === adjustmentRow?.parentKey) {
        const updatedBundleProducts = item.bundle_products?.map((bundle) => {
          const updatedBatches = bundle.product_batches?.map((batch) => {
            if (batch.batch_id === record?.batch_id) {
              return { ...batch, batch_in_use: !batch?.batch_in_use, consumed_qty: 0 };
            }
            return { ...batch, consumed_qty: 0 };
          });
          return { ...bundle, product_batches: updatedBatches };
        });
        return { ...item, quantity: 0, bundle_products: updatedBundleProducts };
      }
      return item;
    });

    return { ...state, data: copyData };
  }

  case ACTIONS.TOGGLE_BATCH: {
    const { record, adjustmentRow } = action.payload;

    const copyData = state.data.map((item) => {
      if (item.key === adjustmentRow?.key) {
        const updatedBatches = item.product_batches?.map((batch) => {
          if (batch.batch_id === record?.batch_id) {
            return { ...batch, batch_in_use: !batch.batch_in_use, consumed_qty: 0 };
          }
          return batch;
        });

        // Recalculate quantity based on batch toggling
        const toggledBatch = item.product_batches?.find(b => b.batch_id === record?.batch_id);
        const newQuantity = item.quantity - (toggledBatch?.consumed_qty || 0);

        return { ...item, quantity: newQuantity, product_batches: updatedBatches };
      }
      return item;
    });

    return { ...state, data: copyData };
  }

  case ACTIONS.ADD_NEW_CHARGE_ROW: {
    const newCharge = {
      chargeKey: uuidv4(),
      charge_name: '',
      charge_amount: 0,
      chargesTaxData: {
        child_taxes: [
          {
            tax_amount: 0,
            tax_type_name: '',
          },
        ],
      },
    };

    return {
      ...state,
      chargeData: [...state.chargeData, newCharge]
    };
  }

  case ACTIONS.ADD_NEW_BATCH: {
    const { batch, callback } = action.payload;
    const grnTableDataCopy = JSON.parse(JSON.stringify(state.data));
    let availableBatches = state.availableBatches;
    let selectedLineInfo = { ...state.selectedLineInfo };

    for (const row of grnTableDataCopy) {

      if (
        (row?.po_line_id && row?.po_line_id === selectedLineInfo?.poLineId) ||
        (row?.key && row?.key === selectedLineInfo?.key) ||
        (row?.grn_line_id && row?.grn_line_id === selectedLineInfo?.grn_line_id)
      ) {
        row.selectedBatch = batch;
        row.available_batches = [batch || [], ...(row?.available_batches || [])];

        availableBatches = row?.available_batches;
        selectedLineInfo = {
          ...selectedLineInfo,
          expiryDays: row?.expiryDays,
          batchUom: row?.product_sku_info?.uom_info || row?.uom_info,
          selectedBatch: batch,
          uomList: row?.product_sku_info?.uom_list || row?.uom_list,
          tenantProductId: row?.product_sku_info?.tenant_product_id || row?.tenant_product_id,
          poLineId: row?.po_line_id,
          key: row?.key,
        };

        break;
      }
    }

    // Call the callback if provided
    if (typeof callback === 'function') callback();

    return {
      ...state,
      data: grnTableDataCopy,
      availableBatches,
      selectedLineInfo,
    };
  }

  case ACTIONS.REMOVE_BATCH: {
    const grnTableDataCopy = JSON.parse(JSON.stringify(state.data));
    let availableBatches = state.availableBatches;
    let selectedLineInfo = { ...state.selectedLineInfo };

    for (const row of grnTableDataCopy) {

      if (
        (row?.po_line_id && row?.po_line_id === selectedLineInfo?.poLineId) ||
        (row?.key && row?.key === selectedLineInfo?.key) ||
        (row?.grn_line_id && row?.grn_line_id === selectedLineInfo?.grn_line_id)
      ) {
        row.selectedBatch = null;
        row.available_batches = [...(row?.available_batches?.filter(item => item?.batch_id) || [])];

        availableBatches = row.available_batches;
        selectedLineInfo = {
          ...selectedLineInfo,
          expiryDays: row?.expiryDays,
          batchUom: row?.product_sku_info?.uom_info || row?.uom_info,
          selectedBatch: null,
          uomList: row?.product_sku_info?.uom_list || row?.uom_list,
          tenantProductId: row?.product_sku_info?.tenant_product_id || row?.tenant_product_id,
          poLineId: row?.po_line_id,
          key: row?.key,
        };

        break;
      }
    }

    return {
      ...state,
      data: grnTableDataCopy,
      availableBatches,
      selectedLineInfo,
    };
  }

  case ACTIONS.SET_SELECTED_BATCH: {
    const { batch } = action.payload;
    const grnTableDataCopy = JSON.parse(JSON.stringify(state.data));
    let selectedLineInfo = { ...state.selectedLineInfo };
    let availableBatches = state.availableBatches;

    for (const row of grnTableDataCopy) {

      if (
        (row?.po_line_id && row?.po_line_id === selectedLineInfo?.poLineId) ||
        (row?.key && row?.key === selectedLineInfo?.key) ||
        (row?.grn_line_id && row?.grn_line_id === selectedLineInfo?.grn_line_id)
      ) {
        row.selectedBatch = batch;
        availableBatches = row?.available_batches;

        selectedLineInfo = {
          expiryDays: row?.expiryDays,
          batchUom: row?.product_sku_info?.uom_info || row?.uom_info,
          selectedBatch: batch,
          uomList: row?.product_sku_info?.uom_list || row?.uom_list,
          tenantProductId: row?.product_sku_info?.tenant_product_id || row?.tenant_product_id,
          poLineId: row?.po_line_id,
          key: row?.key,
        };

        break;
      }
    }

    return {
      ...state,
      data: grnTableDataCopy,
      selectedLineInfo,
      availableBatches,
      showLineBatches: false,
    };
  }

  case ACTIONS.UPDATE_LANDED_COST: {
    const { data, chargeData, charge1Value, discountPercentage, isGRNLevelDiscount, discountType, billFromAddress, billToAddress, grnTypeValue } = action.payload;

    // 1️⃣ Compute totals
    const totals = getLineTotals(data, chargeData, charge1Value);

    // 2️⃣ Update each row with new landed cost values
    const updatedData = data.map((item, i) => ({
      ...item,
      selectedBatch: {
        ...item?.selectedBatch,
        freight_cost: Number(totals.lines[i]?.unit_freight_cost) || 0,
        landed_cost: Number(totals.lines[i]?.unit_landed_cost) || 0,
        other_cost: Number(totals.lines[i]?.unit_other_cost) || 0,
      },
      multiple_batch_info: item?.multiple_batch_info?.map((row) => ({
        ...row,
        freight_cost: Number(totals.lines[i]?.unit_freight_cost) || 0,
        landed_cost: Number(totals.lines[i]?.unit_landed_cost) || 0,
        other_cost: Number(totals.lines[i]?.unit_other_cost) || 0,
      })),
    }));

    // 3️⃣ Apply discount if line-wise discount is enabled
    if (isGRNLevelDiscount) {
      const { data: discountedData, discountPercentage: dp } = applyDiscount(
        JSON.parse(JSON.stringify(updatedData)),
        discountType,
        grnTypeValue,
        discountPercentage,
        billFromAddress,
        billToAddress
      );

      return {
        ...state,
        data: discountedData,
        discountPercentage: dp,
      };
    }

    // 4️⃣ Return updated state without discount changes
    return {
      ...state,
      data: updatedData,
    };
  }

  case ACTIONS.CUSTOM_FIELD_VISIBILITY_CHANGE: {
    const { visible, cfId } = action.payload;

    const updatedVisibleLineCfs = state?.visibleLineCfs?.map((customField) => {
      if (customField?.cfId === cfId) {
        return { ...customField, visible };
      }
      return { ...customField };
    });

    return {
      ...state,
      visibleLineCfs: updatedVisibleLineCfs,
    };
  }

  case ACTIONS.BULK_UPLOAD_BATCH: {
    const { batchData, lineId } = action.payload;

    const mapCustomFieldsForObject = (batchCustomFieldsData, importedItemLines) => {
      return batchCustomFieldsData?.map((batchField) => {
        const matchingField = importedItemLines?.customFields?.find(
          (cf) => cf.id == batchField?.cfEntityId
        );
        let fieldValue = matchingField?.value || '';

        if (batchField?.fieldType === 'DATE' && (Number.isNaN(Date.parse(fieldValue)) || typeof fieldValue === 'number')) {
          fieldValue = null;
        }

        return { ...batchField, fieldValue };
      }) || [];
    };

    const nextData = state.data.map((row) => {
      if (row?.key !== lineId) return row;

      const updatedBatchData = batchData?.map((lines) => ({
        ...lines,
        custom_fields: mapCustomFieldsForObject(row?.multiple_batch_info?.[0]?.custom_fields, lines),
      })) || [];

      const keyName = state.grnTypeValue === 'ADHOC' ? 'quantity' : 'received_qty';
      const totalQty = batchData?.reduce((acc, item) => acc + Number(item?.quantity || 0), 0);

      return {
        ...row,
        multiple_batch_info: updatedBatchData,
        [keyName]: totalQty,
      };
    });

    return { ...state, data: nextData };
  }

  case ACTIONS.UPDATE_BATCH_CFS: {
    const { fieldValue, cfId, record } = action.payload;

    const nextData = state?.data?.map((item) => {
      const isMatch =
      (record?.key && record.key === item.key) ||
      (record?.grn_line_id && record.grn_line_id === item.grn_line_id) ||
      (record?.po_line_id && record.po_line_id === item.po_line_id);

      if (!isMatch) return item;

      const updatedBatchCfs = (item?.selectedBatch?.custom_fields || []).map((cf) => {
        if (cf.cfId === cfId) {
          return {
            ...cf,
            fieldValue:
            cf.fieldType === 'ATTACHMENT'
              ? fieldValue?.map((attachment) => ({
                url: attachment?.response?.response?.location || attachment?.url,
                type: attachment.type,
                name: attachment.name,
                uid: attachment.uid,
              }))
              : fieldValue,
          };
        }
        return cf;
      });

      return {
        ...item,
        selectedBatch: {
          ...item.selectedBatch,
          custom_fields: updatedBatchCfs,
        },
      };
    });

    return { ...state, data: nextData };
  }

  case ACTIONS.UPDATE_BATCH_CFS_FOR_MULTI_BATCH_MODE: {
    const { fieldValue, cfId, record } = action.payload;

    const nextData = state.data.map((item) => {
      const isMatch =
      (record?.parentKey && record.parentKey === item.key) ||
      (record?.grn_line_id && record.grn_line_id === item.grn_line_id) ||
      (record?.po_line_id && record.po_line_id === item.po_line_id);

      if (!isMatch) return item;

      const updatedMultipleBatchInfo = item?.multiple_batch_info?.map((batchItem) => {
        if (batchItem?.key === record.key && batchItem?.custom_fields) {
          const updatedCustomFields = batchItem.custom_fields.map((cf) => {
            if (cf.cfId === cfId) {
              return {
                ...cf,
                fieldValue:
                cf.fieldType === 'ATTACHMENT'
                  ? fieldValue?.map((attachment) => ({
                    url: attachment?.response?.response?.location || attachment?.url,
                    type: attachment.type,
                    name: attachment.name,
                    uid: attachment.uid,
                  }))
                  : fieldValue,
              };
            }
            return cf;
          });
          return { ...batchItem, custom_fields: updatedCustomFields };
        }
        return batchItem;
      });

      return { ...item, multiple_batch_info: updatedMultipleBatchInfo };
    });

    return { ...state, data: nextData };
  }

  case ACTIONS.RESET_FORM: {
    return { ...initialState };
  }

  default: {
    return state;
  }
  }
};
