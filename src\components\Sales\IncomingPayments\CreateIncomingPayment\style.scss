.payment-amount__wrapper-outer {
  width: 100%;

  .payment-amount__checkbox {
    margin-bottom: 10px;
    margin-top: 4px;
    font-size: 12px;
  }

  .payment-amount__wrapper {
    display: flex;
    width: 100%;

    .payment-amount__currency {
      border: 1px solid rgba(68, 130, 218, 0.2);
      border-width: 1px 0px 1px 1px;
      line-height: 30px;
      height: 28px;
      background-color: rgba(45, 124, 247, 0.1);
      width: 35px;
      border-radius: 2px 0px 0px 2px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }

    .create-payment__input-row__input {
      width: calc(100% - 50px);
      margin-bottom: 0px !important;

      input {
        border-radius: 0px 2px 2px 0px !important;
        outline: none;
      }
    }
  }
}

.goods-receiving__po_no-pay {
  color: #2D7DF7;
  font-weight: 600;
  cursor: pointer !important;
  font-size: 10px;
}

.purchase-incoming-payment-totals {
  margin-left: auto;
  background-color: rgba(45, 124, 247, 0.1);
  padding: 10px;
  border-radius: 5px;

  .purchase-incoming-payment__field {
    display: flex;

    .purchase-incoming-payment__field-name {
      width: 200px;
      line-height: 30px;
      font-weight: 500;
    }

    .purchase-incoming-payment__field-value {
      margin-left: auto;
      line-height: 30px;
      font-weight: 500;
    }
  }

  .grn-total_field-total {
    font-size: 18px;
    border-top: 1px solid grey;
    margin-top: 10px;
    padding-top: 10px;
  }
}

.incoming-payment__table {
  margin-top: 20px;
  margin-bottom: 20px;
}