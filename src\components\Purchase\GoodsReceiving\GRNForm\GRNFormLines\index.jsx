import React, { Fragment, useEffect, useState } from 'react';
import { <PERSON>, withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import {
  Table, Select, Checkbox, Popconfirm, Switch, Dropdown, notification,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTrashCan, faGear, faScissors, faPen } from '@fortawesome/free-solid-svg-icons';
import { QUANTITY } from '@Apis/constants';
import ProductFilterV2 from '@Components/Common/ProductFilterV2';
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';
import Helpers from '@Apis/helpers';
import QuickAddButton from '@Components/Common/UILib/QuickAddButton';
import GRNLineCustomField from './GRNLineCustomField';
import ProductDescriptions from '../../../../Common/ProductDescriptions';
import MultipleBatchesSelector from '../MultipleBatches';
import InwardsBatch from '../InwardsBatch';
import SelectTax from '../../../../Admin/Common/SelectTax';
import BulkUploadBatch from './BulkUploadBatch';
import ProductCategoryLabel from '../../../../Common/ProductCategoryLabel';
import BatchCustomFields from '../../../../Common/BatchCustomFields';
import { DEFAULT_CUR_ROUND_OFF } from '@Apis/constants';
import GRNLineSplitStatus from './GRNLineSplitStatus';
import PRZSelect from '../../../../Common/UI/PRZSelect';
import './style.scss';

const { Option } = Select;

const GRNFormLines = ({
  loading, formSubmitted, isGRNLevelDiscount, user, handleDelete, updateTableValue, getClassName, handleFullQuantity, recordFullQuantity, selectedPoValue, setSelectedPoValue,
  removeZeroQuantity, grnId, grnTableData, discountPercentage, discountType, updateGrnTableData, showLineBatches, availableBatches, selectedLineInfo, grnTypeValue,
  handleProductChangeValue, handleProductChange, addNewRow, selectedGRN, MONEY, onChangeLineWiseDiscount, tenantDepartmentId, selectedPoForGrn,
  customClassName, selectedCurrencyName, isMultipleBatchModeEnabled, billFromState, billToState, isBatchValid, setIsBatchValid, visibleLineCfs, customLineInputChange,
  customFieldVisibilityChange, onBulkUploadBatch, inventoryLocationId, inventoryLocationPath, selectedTenant, priceMasking, isCarryForward, handleMultiProductChange, updateBatchCfs, updateBatchCfsForMultiBatchMode, selectedSellerInfo, isApInvoiceEnabled,
}) => {
  const [MultipleBatchMode, setMultipleBatchMode] = useState(false);
  const [multipleBatchQuantity, setMultipleBatchQuantity] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState(null);
  const [selectedRows, setSelectedRows] = useState([]);
  const enabledLocationBasedStock = user?.tenant_info?.inventory_config?.settings?.enable_location_based_stock;
  const enabledWarehouseLocations = user?.tenant_info?.global_config?.sub_modules?.warehouse_setup?.is_active;

  const isVendorOverseas = (selectedSellerInfo?.seller_type === 'OVERSEAS') && user?.tenant_info?.purchase_config?.sub_modules?.vendor?.settings?.hide_tax_for_overseas;

  useEffect(() => {
    if (grnTableData?.[0]?.product_batches?.length > 1 && grnTableData?.[0]?.grn_line_id) {
      setMultipleBatchMode(true);
      isMultipleBatchModeEnabled(true);
    }
  }, [grnTableData]);

  const disablePriceChange = user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.disable_price_change_in_grn_from_po;

  const { isDataMaskingPolicyEnable, isHideCostPrice } = priceMasking;

  const isSecondaryUomEnabled = user?.tenant_info?.global_config?.settings?.enable_secondary_uom;

  const getColumns = () => {
    const grnColumns = [
      {
        title: '#',
        fixed: 'left',
        width: customClassName ? 'auto' : '40px',
        render: (item) => (item?.sq_no ? item?.sq_no : grnTableData?.length),
      },
      {
        title: 'Product Name',
        fixed: 'left',
        width: '230px',
        render: (item) => (
          <div className="table__product-name">
            <ProductFilterV2
              customClass="product_selector"
              record={{
                ...item,
                internal_sku_code: item?.product_sku_info?.internal_sku_code,
                ref_product_code: item?.product_sku_info?.ref_product_code,
              }}
              required
              isTextarea
              isPurchaseProduct
              productTypes={['STORABLE', 'SERVICE', 'NON_STORABLE']}
              handleProductChangeValue={handleProductChangeValue}
              handleProductChange={handleProductChange}
              showClear={grnTypeValue === 'ADHOC'}
              onClear={(key) => {
                const copyData = JSON.parse(JSON.stringify(grnTableData));
                const newData = copyData.map((items) => {
                  if (items.key === key) {
                    return {
                      key: uuidv4(),
                      asset1: '',
                      product_sku_name: '',
                      quantity: '',
                      selling_price: '',
                      taxId: '',
                      lot: '',
                      product_sku_id: null,
                      product_sku_info: null,
                      child_taxes: [{
                        tax_amount: 0,
                        tax_type_name: '',
                      }],
                    };
                  }
                  return {
                    ...items,
                  };
                });
                addNewRow();
                updateGrnTableData(newData);
              }}
              excludedProducts={
                grnTableData?.map((items) => JSON.stringify(items?.tenant_product_id)) || []
              }
              tenantDepartmentId={tenantDepartmentId}
              selectedTenant={selectedTenant}
              showError={formSubmitted && !item?.product_sku_name}
              enableAdvanceSearch
              handleMultiProductChange={handleMultiProductChange}
              loading={loading}
            />
            {item?.productCategoryInfo?.category_path?.length > 0 && (
              <ProductCategoryLabel
                categoryPath={item?.productCategoryInfo?.category_path}
                categoryName={item?.productCategoryInfo?.category_path?.at(
                  -1,
                )}
                containerStyle={{
                  width: 'fit-content',
                }}
              />
            )}
            {item?.po_number &&
              <div>
                <Link
                  to={`/approval?type=po&id=${item?.po_id}`}
                  target="_blank"
                >
                  {`${item?.po_number || ''}`}
                </Link>
              </div>
            }
            {item?.product_sku_info?.product_type === 'SERVICE' && <H3Text text="Service" className="table-subscript-tag" />}
            {item?.product_sku_info?.product_type === 'NON_STORABLE' && <H3Text text="Non Storable" className="table-subscript-tag" />}
            {grnTypeValue === 'MULTIPO' && (<Link to={`/tenant/approval?type=po&id=${item?.source_po_id}`} >{<H3Text text={item?.sourcePONumber} />}</Link>)}
            {
              !item?.remarkRequired
              && (
                <QuickAddButton
                  label="Add Description"
                  onClick={() => {
                    const copyData = JSON.parse(JSON.stringify(grnTableData));
                    const newData = copyData.map((record) => {
                      if (record.key === item.key) {
                        return { ...record, remarkRequired: !item?.remarkRequired };
                      }
                      return record;
                    });
                    if (JSON.stringify(grnTableData) !== JSON.stringify(newData)) {
                      updateGrnTableData(newData);
                    }
                  }}
                />
              )
            }
            {item?.remarkRequired ? (
              <ProductDescriptions
                descriptions={item?.remarks}
                modules={{
                  toolbar: false,
                }}
                onChange={(e) => {
                  const copyData = JSON.parse(JSON.stringify(grnTableData));
                  const newData = copyData.map((record) => {
                    if (item.key === record.key) {
                      return { ...record, remarks: e, remarkRequired: !!e?.replace(/<[^>]+>/g, '') };
                    }
                    return record;
                  });

                  if (JSON.stringify(grnTableData) !== JSON.stringify(newData)) {
                    updateGrnTableData(newData);
                  }
                }}
                className="invoice-table__remarks"
                placeholder="Add description of this item.."
                key={item.key}
              />
            ) : ''}

          </div>
        ),
      },
      {
        title: 'Quantity',
        dataIndex: 'quantity',
        width: '180px',
        render: (text, record) => (
          <div className="create-grn-table__input">
            {isSecondaryUomEnabled && <span className="uom-label">{`Primary (${record.uom_info?.uqc || 'N/A'})`}</span>}
            <div className="create-grn-table__input-wrapper" style={{ marginTop: '3px', padding: '0px', display: 'block' }}>
              <H3FormInput
                value={record?.quantity}
                type="number"
                containerClassName={`orgInputContainer ${(formSubmitted && Number(record.quantity) <= 0) ? 'create-pr__input' : ''}`}
                labelClassName="orgFormLabel"
                inputClassName="orgFormInput"
                disabled={MultipleBatchMode && record?.product_type !== 'SERVICE' && record?.product_type !== 'NON_STORABLE'}
                onChange={(e) => {
                  const copyData = JSON.parse(JSON.stringify(grnTableData));
                  const discountValue = Number(record?.discount) || 0;
                  const totalPrice = (Number(e.target.value) * record.offer_price);
                  const taxableValue = record?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
                    : Math.max(totalPrice - discountValue, 0);

                  copyData.map((item) => {
                    if (record.grn_line_id === item.grn_line_id && item.key === record.key) {
                      const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')?.cfId;
                      item.quantity = e.target.value;
                      item.child_taxes = Helpers.computeTaxation(taxableValue, item?.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                      item.lineCustomFields = customLineInputChange(e.target.value, cfId, item, true);
                    }
                    return grnTableData;
                  });
                  updateGrnTableData(copyData);
                }}
              />
            </div>
            {' '}
            {isSecondaryUomEnabled && record?.secondaryUomUqc && (
              <div>
                <span className="uom-label">{`Secondary (${record?.secondaryUomUqc ? record.secondaryUomUqc : 'N/A'})`}</span>
                <div className="create-grn-table__input-wrapper" style={{ marginTop: '3px', padding: '0px', display: 'block' }}>
                  <H3FormInput
                    value={record.secondary_uom_qty}
                    name="quantity"
                    type="number"
                    containerClassName="orgInputContainer"
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    onChange={(e) => {
                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                      copyData.map((item) => {
                        if (record.grn_line_id === item.grn_line_id) {
                          if (item.key === record.key) {
                            item.secondary_uom_qty = Number(e.target.value);
                          }
                        }
                        return grnTableData;
                      });
                      updateGrnTableData(copyData);
                    }}
                  />
                </div>
              </div>
            )}
            {isApInvoiceEnabled && (
              <div>
                <div className="uom-label">{`Invoice Qty (${record.uom_info?.uqc || 'N/A'})`}</div>
                <div className="create-grn-table__input-wrapper" style={{ marginTop: '3px', padding: '0px', display: 'block' }}>
                  <H3FormInput
                    value={record?.invoiceQuantity ?? 0}
                    type="number"
                    containerClassName={`orgInputContainer ${(formSubmitted && Number(record.invoiceQuantity) <= 0) ? 'create-pr__input' : ''}`}
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    disabled={MultipleBatchMode && record?.product_type !== 'SERVICE' && record?.product_type !== 'NON_STORABLE'}
                    onChange={(e) => {
                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                      copyData.map((item) => {
                        if (record?.grn_line_id === item?.grn_line_id && item?.key === record?.key) {
                          const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Invoice Quantity')?.cfId;
                          item.invoiceQuantity = Number(e.target.value);
                          item.lineCustomFields = customLineInputChange(Number(e.target.value), cfId, item, true);
                        }
                        return grnTableData;
                      });
                      updateGrnTableData(copyData);
                    }}
                  />
                </div>
              </div>
            )}
            <H3Text text="UOM" className="orgFormLabel" />
            <div className="orgInputContainer create-grn-table__input-wrapper" style={{ paddingLeft: '0px' }}>
              <PRZSelect
                filterOption={false}
                className=""
                value={record?.uomId}
                onChange={(value) => {
                  const copyData = JSON.parse(JSON.stringify(grnTableData));
                  copyData.map((data) => {
                    if (data.grn_line_id === record.grn_line_id) {
                      if (data.key === record.key) {
                        data.uomId = value;
                        data.uomInfo = data?.uom_list?.find(
                          (rec) => rec?.uom_id === value,
                        );
                        data.uom_info = data?.uom_list?.find(
                          (rec) => rec?.uom_id === value,
                        );
                      }
                    }
                    return data;
                  });
                  updateGrnTableData(copyData);
                }}
                disabled={!record?.uom_list}
              >
                {record?.uom_list?.map((uom) => (

                  <Option
                    key={uom?.uom_id}
                    value={uom?.uom_id}
                  >
                    {`${uom?.uqc?.toProperCase()} (${uom?.uom_name?.toProperCase()})`}

                  </Option>
                ))}
              </PRZSelect>
            </div>
          </div>
        ),
        hidden: (grnTypeValue !== 'ADHOC'),
      },
      {
        title: 'Quantity',
        width: '180px',
        render: (item) => {
          const quantity = Number(item?.quantity);
          const grnPercent = Number(item?.product_sku_info?.grn_over_flow_percent);
          const overFlowQuantity = ((grnPercent / 100) * quantity) + quantity;

          function checkOverflow() {
            if (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) {
              if (grnPercent > 0) {
                if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
                  return true;
                }
              } else if (grnPercent === 0 || grnPercent === null) {
                if ((overFlowQuantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
                  return false;
                }
              }
            } else if (grnPercent > 0) {
              if ((overFlowQuantity) - Number(item.total_received_qty) - Number(item.received_qty) < 0) {
                return true;
              }
            } else if (grnPercent === 0 || grnPercent === null) {
              if ((quantity - Number(item.total_received_qty) - Number(item.received_qty)) < 0) {
                return true;
              }
            }
            return false;
          }
          return (
            <div className="create-grn-table__input">
              {item.po_line_id && (
                <div className="create-grn-table__input-wrapper">
                  <H3Text text="Ordered" className="create-grn-table__input-label" />
                  <div>
                    {QUANTITY(item?.ordered_qty || item?.quantity, item?.uom_info?.[0]?.precision)}
                    &nbsp;
                    {item?.uom_info?.[0]?.uqc?.toProperCase()}
                  </div>
                </div>
              )}

              {item.po_line_id && item?.po_line_status?.length === 0 && (
                <div className="create-grn-table__input-wrapper" style={{ marginTop: '3px' }}>
                  <H3Text text="Pending" className="create-grn-table__input-label" />
                  <div style={{ width: '80px !important' }}>
                    {QUANTITY(Number(item?.ordered_qty || item?.quantity) - Number(item?.total_received_qty || 0), item?.uom_info?.[0]?.precision)}
                    &nbsp;
                    {item?.uom_info?.[0]?.uqc?.toProperCase()}
                  </div>
                </div>
              )}
              {isSecondaryUomEnabled && item?.secondaryUomUqc && (
                <div className="create-grn-table__input-wrapper create-grn-table__input-wrapper-small" style={{ marginTop: '3px' }}>
                  <H3Text text="Secondary" className="create-grn-table__input-label" />
                  <H3FormInput
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    value={item?.secondary_uom_qty}
                    name="valid quantity"
                    type="number"
                    onChange={(event) => {
                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                      const updatedData = copyData.map((obj) => {
                        if ((obj.po_line_id === item.po_line_id && item.po_line_id) || (obj.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj.key === item?.key && item?.key)) {
                          return {
                            ...obj,
                            secondary_uom_qty: Number(event.target.value),
                          };
                        }
                        return obj;
                      });
                      updateGrnTableData(updatedData);
                    }}
                  />
                  <div style={{ padding: '2px' }}>
                    {`(${item?.secondaryUomUqc?.toProperCase() || ''})`}
                  </div>
                </div>
              )}
              <div className="create-grn-table__input-wrapper create-grn-table__input-wrapper-small" style={{ marginTop: '3px' }}>
                <H3Text text="Received" className="create-grn-table__input-label" />
                <H3FormInput
                  labelClassName="orgFormLabel"
                  inputClassName={`orgFormInput  ${getClassName(user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po, checkOverflow(), item)} ${(formSubmitted && Number(item?.received_qty) <= 0) ? 'orgFormInputError' : ''}`}
                  value={item.received_qty}
                  name="valid quantity"
                  type="number"
                  disabled={(MultipleBatchMode) || (user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po ? false : (overFlowQuantity - Number(item?.total_received_qty)) <= 0) || item?.po_line_status?.length > 0}
                  onChange={(event) => {
                    const copyData = JSON.parse(JSON.stringify(grnTableData));
                    const discountValue = Number(item?.discount) || 0;
                    const totalPrice = (Number(event.target.value) * item.offer_price);
                    const taxableValue = item?.lineDiscountType === 'Percent' ? totalPrice * (1 - discountValue / 100)
                      : Math.max(totalPrice - discountValue, 0);

                    const updatedData = copyData.map((obj) => {
                      if ((obj.po_line_id === item.po_line_id && item.po_line_id) || (obj.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj.key === item?.key && item?.key)) {
                        const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')?.cfId;
                        return {
                          ...obj,
                          received_qty: event.target.value,
                          child_taxes: Helpers.computeTaxation(taxableValue, item?.taxInfo, billFromState, billToState)?.tax_info?.child_taxes,
                          lineCustomFields: customLineInputChange(event.target.value, cfId, item, true),
                        };
                      }
                      return obj;
                    });
                    updateGrnTableData(updatedData);
                  }}
                />
              </div>
              {!item.grn_line_id && !MultipleBatchMode && item?.po_line_status?.length === 0 && (
                <H3Text
                  text={(
                    <div style={{ marginLeft: 'auto' }}>
                      Use Full Quantity
                    </div>
                  )}
                  onClick={() => handleFullQuantity(item)}
                  className="create-grn__table-small-action flex-display"
                />
              )}
              {isApInvoiceEnabled && (
                <div className="create-grn-table__input-wrapper create-grn-table__input-wrapper-small" style={{ marginTop: '3px' }}>
                  <H3Text text="Invoice Qty" className="create-grn-table__input-label" />
                  <H3FormInput
                    labelClassName="orgFormLabel"
                    inputClassName={`orgFormInput ${(formSubmitted && (item?.invoiceQuantity ? Number(item?.invoiceQuantity) <= 0 : Number(item?.received_qty) <= 0)) ? 'orgFormInputError' : ''}`}
                    value={item?.invoiceQuantity ?? 0}
                    name="valid quantity"
                    type="number"
                    onChange={(event) => {
                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                      const updatedData = copyData.map((obj) => {
                        if ((obj?.po_line_id === item?.po_line_id && item?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj.key === item?.key && item?.key)) {
                          const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Invoice Quantity')?.cfId;
                          return {
                            ...obj,
                            invoiceQuantity: Number(event.target.value),
                            lineCustomFields: customLineInputChange(Number(event.target.value), cfId, item, true),
                          };
                        }
                        return obj;
                      });
                      updateGrnTableData(updatedData);
                    }}
                  />
                </div>
              )}
            </div>
          );
        },
        hidden: (grnTypeValue === 'ADHOC'),
      },
      {
        title: 'Pricing',
        width: '100px',
        render: (item) => (
          <div className="create-grn-table__input">
            <div className="create-grn-table__input-wrapper create-grn-table__input-wrapper-small-2" style={{ marginBottom: '3px' }}>
              <H3Text text="Unit Price" className="create-grn-table__input-label" />
              &nbsp;
              <H3FormInput
                labelClassName="orgFormLabel"
                inputClassName={`orgFormInput ${(formSubmitted && (!item?.offer_price || item?.offer_price == 0)) ? 'orgFormInputError' : ''} `}
                value={item?.offer_price}
                name="unit price"
                type="number"
                disabled={(grnId ? false : (Number(item?.quantity) - Number(item?.total_received_qty)) <= 0) || (grnTypeValue === 'ADHOC' ? false : disablePriceChange)}
                onChange={(event) => {
                  const copyData = JSON.parse(JSON.stringify(grnTableData));
                  const discountValue = Number(item.discount) || 0;
                  const totalPrice = (Number(grnTypeValue === 'ADHOC' ? item?.quantity : item?.received_qty)
                    * Number(event.target.value));
                  const taxableValue = item?.lineDiscountType === 'Percent' ? (
                    totalPrice * (1 - discountValue / 100)
                  ) : (
                    totalPrice - discountValue
                  );

                  const updatedData = copyData.map((obj) => {
                    if ((grnId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) && obj.key === item.key) {
                      const cfId = item?.lineCustomFields?.find((cf) => cf.fieldName === 'Rate')?.cfId;
                      return {
                        ...obj,
                        offer_price: event.target.value,
                        child_taxes: Helpers.computeTaxation(taxableValue, item?.taxInfo, billFromState, billToState)?.tax_info?.child_taxes,
                        lineCustomFields: customLineInputChange(event.target.value, cfId, item, true),
                        selectedBatch: {
                          ...obj?.selectedBatch,
                          cost_price: event.target.value,
                        },
                        multiple_batch_info: obj?.multiple_batch_info?.map((row) => ({
                          ...row,
                          cost_price: event.target.value,
                        })),
                      };
                    }
                    return obj;
                  });
                  updateGrnTableData(updatedData);
                }}
                hideInput={isDataMaskingPolicyEnable && isHideCostPrice}
                popOverMessage={"You don't have access to view unit price"}
              />
            </div>
            <div className={isVendorOverseas ? "display-none" : "flex-display create-grn-table__input-wrapper create-grn-table__input-wrapper-small-2"} style={{ alignItems: 'center', marginBottom: '3px', whiteSpace: 'nowrap' }}>
              <H3Text text="Tax" className="create-grn-table__input-label" />
              &nbsp;
              <div
                className="orgInputContainer"
              >
                <SelectTax
                  onChange={(value) => {
                    const copyData = JSON.parse(JSON.stringify(grnTableData));
                    const discountValue = Number(item.discount) || 0;
                    const totalPrice = (Number(grnTypeValue === 'ADHOC' ? item?.quantity : item?.received_qty)
                      * item?.offer_price);
                    const taxableValue = item?.lineDiscountType === 'Percent' ? (
                      totalPrice * (1 - discountValue / 100)
                    ) : (
                      totalPrice - discountValue
                    );

                    const updatedData = copyData.map((obj) => {
                      if (grnId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) {
                        if (obj.key === item.key) {
                          return {
                            ...obj,
                            taxId: value?.tax_id,
                            taxInfo: value,
                            child_taxes: Helpers.computeTaxation(taxableValue, value, billFromState, billToState)?.tax_info?.child_taxes,
                          };
                        }
                      }
                      return obj;
                    });
                    updateGrnTableData(updatedData);
                  }}
                  selectedGST={item.taxId}
                  disabled={(Number(item?.quantity) - Number(item?.total_received_qty)) <= 0}
                />
              </div>
            </div>
            <div className="flex-display create-grn-table__input-wrapper create-grn-table__input-wrapper-small-2" style={{ alignItems: 'center', marginBottom: '3px', whiteSpace: 'nowrap' }}>
              <H3Text text="Discount" className="create-grn-table__input-label" />
              &nbsp;
              {!isGRNLevelDiscount ? (
                <Fragment>
                  <H3FormInput
                    value={item?.discount}
                    type="number"
                    containerClassName={`orgInputContainer ${(formSubmitted && Number(item.discount) < 0) ? 'create-pr__input' : ''}`}
                    labelClassName="orgFormLabel"
                    inputClassName="orgFormInput"
                    onChange={(event) => {
                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                      const discountValue = Number(event.target.value) || 0;
                      const totalPrice = (Number(grnTypeValue === 'ADHOC' ? item?.quantity : item?.received_qty)
                        * item?.offer_price);
                      const taxableValue = item?.lineDiscountType === 'Percent'
                        ? totalPrice * (1 - discountValue / 100)
                        : Math.max(totalPrice - discountValue, 0);

                      const updatedData = copyData.map((obj) => {
                        if (grnId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) {
                          if (obj.key === item.key) {
                            return {
                              ...obj,
                              discount: event.target.value,
                              child_taxes: Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState, billToState)?.tax_info?.child_taxes,
                            };
                          }
                        }
                        return obj;
                      });
                      updateGrnTableData(updatedData);
                    }}
                    disabled={isGRNLevelDiscount || !Number(grnTypeValue === 'ADHOC' ? item?.quantity : item?.received_qty) || !item?.offer_price}
                  />
                  <div className="orgInputContainer discount-type">
                    <PRZSelect
                      value={item?.lineDiscountType}
                      onChange={(value) => {
                        const copyData = JSON.parse(JSON.stringify(grnTableData));
                        const discountValue = Number(item?.discount) || 0;
                        const totalPrice = (Number(grnTypeValue === 'ADHOC' ? item?.quantity : item?.received_qty)
                          * item?.offer_price);
                        const taxableValue = value === 'Percent'
                          ? totalPrice * (1 - discountValue / 100)
                          : Math.max(totalPrice - discountValue, 0);

                        copyData.map((obj) => {
                          if (grnId ? obj.grn_line_id === item.grn_line_id : obj.po_line_id === item.po_line_id) {
                            if (obj.key === item.key) {
                              obj.lineDiscountType = value;
                              obj.child_taxes = Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                            }
                          }
                          return obj;
                        });
                        updateGrnTableData(copyData);
                      }}
                      style={{
                        width: '50px',
                      }}
                      disabled={isGRNLevelDiscount}
                      placeholder=""
                    >
                      <Option key="Amount" value="Amount">
                        {`${selectedCurrencyName?.currency_symbol} `}
                      </Option>
                      <Option key="Percent" value="Percent">
                        %
                      </Option>
                    </PRZSelect>
                  </div>
                </Fragment>
              ) : (
                <div className='orgFormInput'>
                  <H3Text
                    text={
                      item?.discount ? item?.lineDiscountType === 'Percent' ? `${(item?.discount)?.toFixed(DEFAULT_CUR_ROUND_OFF)}%` :
                        MONEY((item?.discount)?.toFixed(DEFAULT_CUR_ROUND_OFF), selectedCurrencyName?.currency_code)
                        : item?.lineDiscountType === 'Percent' ? `0%` : MONEY(0, selectedCurrencyName?.currency_code)
                    }
                  />
                </div>
              )}
            </div>

          </div>
        ),
        hidden: isApInvoiceEnabled,
      },
      {
        title: 'Inward Batches',
        width: '236px',
        render: (item) => {
          const comparisonId = item?.grn_id ? selectedGRN?.tenant_info?.default_store_id : user?.tenant_info?.default_store_id;
          const filteredBatches = item?.available_batches?.filter((batch) => comparisonId === batch?.tenant_department_id);
          return (
            <InwardsBatch
              item={item}
              formSubmitted={formSubmitted}
              grnTableData={grnTableData}
              updateGrnTableData={(value) => updateGrnTableData(value)}
              destDepartmentId={tenantDepartmentId}
              enabledWarehouseLocations={enabledWarehouseLocations && enabledLocationBasedStock}
              availableBatches={(value) => availableBatches(value)}
              selectedLineInfo={(value) => selectedLineInfo(value)}
              showLineBatches={(value) => showLineBatches(value)}
              filteredBatches={filteredBatches}
              setIsBatchValid={setIsBatchValid}
              isBatchValidValue={isBatchValid}
              hideCost
              disableCostPrice={grnTypeValue === 'ADHOC' ? false : disablePriceChange}
              isApInvoiceEnabled={isApInvoiceEnabled}
            />

          );
        },
        hidden: MultipleBatchMode,
      },
      {
        title: 'Batch Custom Fields',
        width: '236px',
        render: (item) => {
          return (
            <React.Fragment>
              {item?.selectedBatch?.custom_fields
                ? (
                  <BatchCustomFields
                    cfWrapperClassName="create-grn__input-row"
                    labelClassName="create-grn__input-row__label"
                    customFields={item?.selectedBatch?.custom_fields || []}
                    formSubmitted={formSubmitted}
                    customInputChange={(value, cfId) => updateBatchCfs(value, cfId, item)}
                    isHorizontalUi
                    hideTitle
                    isForGRNLine
                  />
                ) : '-'}
            </React.Fragment>

          );
        },
        hidden: MultipleBatchMode,
      },
      {
        title: 'Purchase Account',
        render: (item) => (
          <React.Fragment>
            {' '}
            <div className="create-grn-view__input-row">
              <div className="create-grn-view__input-row__input line_level_purchase_account">
                <PRZSelect
                  value={item?.tally_purchase_account}
                  onChange={(value) => {
                    if (grnTypeValue === 'ADHOC') {
                      const copyData = JSON.parse(JSON.stringify(grnTableData));
                      const updatedData = copyData.map((obj) => {
                        if ((obj.po_line_id === item.po_line_id && obj?.po_line_id) || (obj?.grn_line_id === item?.grn_line_id && item?.grn_line_id) || (obj?.key === item?.key && item?.key)) {
                          return { ...obj, tally_purchase_account: value };
                        }
                        return obj;
                      });
                      updateGrnTableData(updatedData);
                    } else {
                      updateTableValue(item.po_line_id, value, 'tally_purchase_account');
                    }
                  }}
                  style={{
                    width: '150px',
                    borderRadius: '2px',
                    marginBottom: '5px',
                  }}
                  showError={formSubmitted && !item?.tally_purchase_account}
                  errorName="*Please select a puchase account"
                  errorClassName="input-error"
                >
                  {user?.tenant_info?.purchase_account_list?.map((purchase) => (
                    <Option key={purchase?.purchase_account_name} value={purchase?.purchase_account_name}>
                      {purchase.purchase_account_name}
                    </Option>
                  ))}
                </PRZSelect>
              </div>
            </div>
          </React.Fragment>
        ),
        className: user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' ? '' : 'display-none',
      },
      {
        title: 'Total',
        render: (item) => {
          const discountValue = Number(item.discount) || 0;

          const totalPrice = (Number(grnTypeValue === 'ADHOC' ? item?.quantity : item?.received_qty)
            * item?.offer_price);

          const taxableValue = item?.lineDiscountType === 'Percent' ? (
            totalPrice * (1 - discountValue / 100)
          ) : (
            totalPrice - discountValue
          );

          return (
            <div style={
              { minWidth: '60px' }
            }
            >
              <H3Text
                text={item.taxInfo
                  ? MONEY(
                    isNaN(Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState, billToState)?.taxed_value)
                      ? 0
                      : Math.max(Helpers.computeTaxation(taxableValue, item.taxInfo, billFromState, billToState)?.taxed_value, 0),
                    selectedCurrencyName?.currency_code,
                  )
                  : 0}
                hideText={isDataMaskingPolicyEnable && isHideCostPrice}
                popOverMessage={"You don't have access to view total amount"}
              />
            </div>
          );
        },
      },
      {
        title: '',
        fixed: 'right',
        render: (text, record) => (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {MultipleBatchMode && (
              <BulkUploadBatch
                customClass="custom-doc-columns__wrapper"
                customStyle={{ marginRight: '5px' }}
                onBulkUploadBatch={(batchData) => onBulkUploadBatch(batchData, (record?.key || record?.po_line_id || record?.grn_line_id))}
                disabled={!record?.product_sku_id}
                parentKey={record?.key}
                inventoryLocationId={inventoryLocationId}
                inventoryLocationPath={inventoryLocationPath}
              />
            )}
            <Popconfirm title="Are you sure you want to remove this item?" onConfirm={() => handleDelete(record?.key, record?.grn_line_id)} placement="topRight">
              <div className="delete-line-button__new" style={{ marginLeft: 'auto' }}>
                <FontAwesomeIcon icon={faTrashCan} />
              </div>
            </Popconfirm>

          </div>
        ),
      },
    ].filter((item) => item.hidden !== true);

    if (visibleLineCfs?.length) {
      grnColumns.splice(isMultipleBatchModeEnabled ? 5 : 6, 0, {
        title: 'CUSTOM FIELDS',
        width: '236px',
        render: (record) => (
          <GRNLineCustomField
            cfWrapperClassName="create-grn__input-row"
            labelClassName="create-grn__input-row__label"
            customFields={
              (record?.lineCustomFields || []).filter(
                (cf) => !['Rate', 'Quantity', 'Invoice Quantity']?.includes(cf?.fieldName)
              )
            }
            formSubmitted={formSubmitted}
            customInputChange={(value, cfId) => customLineInputChange(value, cfId, record)}
            isHorizontalUi
            hideTitle
            isCarryForward={isCarryForward}
          />
        ),
      });
    }

    if (grnTableData.some((line) => line.po_line_status?.length > 0)) {
      grnColumns.splice(3, 0, {
        title: 'SPLIT STATUS',
        // width: '236px',
        render: (record) => (
          <GRNLineSplitStatus
            wrapperClassName="create-grn__input-row"
            labelClassName="create-grn__input-row__label"
            statuses={record?.po_line_status || []}
            formSubmitted={formSubmitted}
            record={record}
            onChangeStatusQty={(value, statusId, name) => onChangeStatusQty(value, record, statusId, name)}
          />
        ),
      });
    }

    return grnColumns;
  };

  function onChangeStatusQty(value, record, statusId, name) {
    const updatedData = grnTableData.map((obj) => {
      const isMatchingLine =
        (obj.po_line_id && obj.po_line_id === record.po_line_id) ||
        (obj.grn_line_id && obj.grn_line_id === record.grn_line_id) ||
        (obj.key && obj.key === record.key);

      if (!isMatchingLine) return obj;
      const discountValue = Number(record?.discount) || 0;
      const offerPrice = Number(record?.offer_price || 0);
      const taxInfo = record?.taxInfo;
      const lineDiscountType = record?.lineDiscountType;
      const lineStatusList = obj?.po_line_status || [];

      let updatedLineStatus = [...lineStatusList];
      let pendingQty = Number(obj['lineStatusPendingQty']) || 0;

      if (name) {
        // Direct value change (e.g. in a named field like 'lineStatusPendingQty')
        const pendingQtyNum = Number(value) || 0;
        const totalReceivedQty = lineStatusList.reduce((acc, curr) => acc + Number(curr?.line_status_received_qty || 0), 0);
        const totalQty = pendingQtyNum + totalReceivedQty;
        const totalPrice = totalQty * offerPrice;

        const taxableValue =
          lineDiscountType === 'Percent'
            ? totalPrice * (1 - discountValue / 100)
            : Math.max(totalPrice - discountValue, 0);
        const cfId = obj?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')?.cfId;
        return {
          ...obj,
          received_qty: totalQty,
          child_taxes: Helpers.computeTaxation(taxableValue, taxInfo, billFromState, billToState)?.tax_info?.child_taxes,
          [name]: value,
          lineCustomFields: customLineInputChange(totalQty, cfId, obj, true),

        };
      } else {
        // Update individual status quantity
        updatedLineStatus = lineStatusList.map((status) =>
          status.po_line_status_id === statusId
            ? { ...status, line_status_received_qty: value }
            : status
        );

        const totalReceivedQty = updatedLineStatus.reduce((acc, curr) => acc + Number(curr?.line_status_received_qty || 0), 0);
        const totalQty = pendingQty + totalReceivedQty;
        const totalPrice = totalQty * offerPrice;

        const taxableValue =
          lineDiscountType === 'Percent'
            ? totalPrice * (1 - discountValue / 100)
            : Math.max(totalPrice - discountValue, 0);
        const cfId = obj?.lineCustomFields?.find((cf) => cf.fieldName === 'Quantity')?.cfId;
        return {
          ...obj,
          received_qty: totalQty,
          po_line_status: updatedLineStatus,
          child_taxes: Helpers.computeTaxation(taxableValue, taxInfo, billFromState, billToState)?.tax_info?.child_taxes,
          lineCustomFields: customLineInputChange(totalQty, cfId, obj, true),
        };
      }
    });

    updateGrnTableData(updatedData);
  }

  const getGrnColumns = () => {
    if (grnTypeValue === 'ADHOC') {
      return getColumns();
    }
    if ((user?.tenant_info?.purchase_account_selection === 'FROM_GRN_LINE' || user?.tenant_info?.purchase_account_selection === 'SAME_AS_PRODUCT_GROUP')) {
      return getColumns();
    }
    return getColumns()?.filter((item) => item.title !== 'Purchase Account');
  };

  const getDataSource = (data) => {
    if (data) {
      const tempData = [];
      const filterData = data?.filter(
        (line) => line.quantity > line.total_received_qty,
      );
      const grnData = (selectedPoForGrn?.po_id && !user?.tenant_info?.purchase_config?.sub_modules?.goods_receiving_note?.settings?.flexible_qty_wrt_po) ? filterData : data;

      // const sortedData = Helpers.sortByParameter({
      //   data: grnData,
      //   parameter: 'product_sku_info.internal_sku_code',
      // });

      // const sortedByProductSkuId = [
      //   ...sortedData.filter((item) => item?.product_sku_id),
      //   ...sortedData.filter((item) => !item?.product_sku_id),
      // ];

      for (let i = 0; i < grnData.length; i++) {
        tempData.push({
          ...grnData[i],
          sq_no: i + 1,
        });
      }
      return tempData;
    }
    return [];
  };

  const getBundleLineKeys = (rows) => {
    const keys = [];
    for (let i = 0; i < rows?.length; i++) {
      if (rows[i]?.bundle_products?.length) {
        keys.push(rows[i]?.key);
      }
    }
    return keys;
  };

  const grnTableDataLength = grnTableData?.length;

  const removeSelectedRows = () => {
    const copyData = JSON.parse(JSON.stringify(grnTableData));
    const updatedData = copyData?.filter((item) => !selectedRowKeys.includes(item.key));
    const updatedSelectedPoValue = selectedPoValue?.filter((item) =>
      updatedData?.some((data) => data?.po_id === item?.value)
    );
    setSelectedPoValue(updatedSelectedPoValue);
    setSelectedRows([]);
    setSelectedRowKeys([]);
    updateGrnTableData(updatedData);
  };

  return (
    <div>
      <Table
        title={() => (
          <div className="create-grn-title__wrapper">
            <H3Text text={`GRN Line Items (${grnTableData?.filter((item) => item.product_sku_name?.length > 0)?.length || '0'})`} className="create-po__input-row__label" />
            <div className="create-grn-title-left">
              <Dropdown
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                overlay={(
                  <div className="line_actions-wrapper">
                    {((!!grnTableDataLength) && (grnTypeValue === 'Purchase Order') && !MultipleBatchMode) && (
                      <div
                        onClick={() => {
                          recordFullQuantity();
                          notification.open({
                            type: 'warning',
                            message: 'All the products have been filled with their Pending quantities.',
                            duration: 3,
                            placement: 'top',
                          });
                        }}
                        className="line_actions"
                      >
                        <FontAwesomeIcon icon={faPen} style={{ marginTop: '4px' }} size="sm" />
                        {' '}
                        &nbsp; Record Full Quantity
                      </div>
                    )}
                    {((!!grnTableDataLength) && (grnTypeValue === 'Purchase Order')) && (
                      <div
                        onClick={() => {
                          removeZeroQuantity();
                          notification.open({
                            type: 'warning',
                            message: 'Products having zero or no filled quantity have been removed.',
                            duration: 3,
                            placement: 'top',
                          });
                        }}
                        className="line_actions"
                      >
                        <FontAwesomeIcon icon={faScissors} style={{ marginTop: '4px' }} size="sm" />
                        {' '}
                        &nbsp; Remove zero products
                      </div>
                    )}
                    {((!!grnTableDataLength) && (grnTypeValue === 'MULTIPO')) && (
                      <div
                        onClick={() => {
                          removeSelectedRows();
                          notification.open({
                            type: 'warning',
                            message: 'Selected products have been removed.',
                            duration: 3,
                            placement: 'top',
                          });
                        }}
                        className="line_actions"
                      >
                        <FontAwesomeIcon icon={faTrashCan} style={{ marginTop: '5px', color: 'red' }} size="sm" />
                        {' '}
                        &nbsp; Delete Selected Lines
                      </div>
                    )}
                    <Checkbox
                      checked={isGRNLevelDiscount}
                      onChange={(e) => {
                        const copyData = JSON.parse(JSON.stringify(grnTableData));
                        copyData.map((i) => {
                          i.discount = 0;
                          i.lineDiscountType = e.target.checked ? discountType : 'Percent';
                          i.child_taxes = Helpers.computeTaxation(i?.quantity * i?.offer_price, i?.taxInfo, billFromState, billToState)?.tax_info?.child_taxes;
                        });
                        onChangeLineWiseDiscount(e.target.checked);
                        updateGrnTableData(copyData);
                        discountPercentage(0);
                      }}
                      disabled={loading}
                      className="line_actions"
                    >
                      GRN Level discount
                    </Checkbox>
                    <div className="line_actions">
                      <Switch
                        checked={MultipleBatchMode}
                        onChange={() => {
                          setMultipleBatchMode(!MultipleBatchMode);
                          isMultipleBatchModeEnabled(!MultipleBatchMode);
                          if (grnTypeValue === 'ADHOC') {
                            grnTableData?.forEach((item) => {
                              item.quantity = 0;
                            });
                          } else {
                            grnTableData?.forEach((item) => {
                              item.received_qty = 0;
                            });
                          }
                        }}
                        checkedChildren="Multi Batch Mode"
                        unCheckedChildren="Single Batch Mode"
                        disabled={grnTableData?.[0]?.product_batches?.length > 1 || grnTableData?.[0]?.grn_line_id}
                        style={{ width: '100%' }}
                      />
                    </div>
                  </div>
                )}
                placement="topRight"
                trigger={['click']}
              >
                <div style={{ marginRight: '5px' }} className="custom-doc-columns__wrapper">
                  Actions
                </div>
              </Dropdown>

              <Dropdown
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                overlay={(
                  <div className="custom-doc-columns__content">
                    <div className="custom-doc-columns__content-section">
                      <H3Text text="Visible Columns" className="custom-doc-columns__content-title" />
                      {
                        visibleLineCfs && visibleLineCfs.map((item) => (
                          <div key={item?.cfId}>
                            <Checkbox
                              disabled={item?.isRequired}
                              checked={item?.visible}
                              onChange={() => {
                                let grnTableDataCopy = JSON.parse(JSON.stringify(grnTableData));
                                grnTableDataCopy = grnTableDataCopy?.map((data) => {
                                  const newLineCustomFields = data?.lineCustomFields?.map((customField) => {
                                    if (customField?.cfId === item?.cfId) {
                                      return { ...customField, visible: !item?.visible };
                                    }
                                    return { ...customField };
                                  });

                                  return { ...data, lineCustomFields: newLineCustomFields };
                                });

                                updateGrnTableData(grnTableDataCopy);
                                customFieldVisibilityChange(!item?.visible, item?.cfId);
                              }}
                            >
                              {item?.fieldName}
                            </Checkbox>
                          </div>
                        ))
                      }
                    </div>
                  </div>
                )}
                trigger={['click']}
                placement="topRight"
              >
                <div style={{ marginLeft: '5px' }} className="custom-doc-columns__wrapper">
                  <FontAwesomeIcon icon={faGear} />
                  &nbsp;
                  More Columns
                </div>
              </Dropdown>
            </div>
          </div>
        )}
        bordered
        showHeader
        loading={loading}
        size="small"
        scroll={{ x: 'max-content' }}
        tableLayout="unset"
        top="none"
        bottom="bottomRight"
        columns={getGrnColumns()}
        dataSource={getDataSource(grnTableData)}
        pagination={false}
        rowSelection={(window.screen.width > 480) && (grnTypeValue === 'MULTIPO') ? {
          onChange: (rowKeys, rows) => {
            setSelectedRows(rows);
            setSelectedRowKeys(rowKeys);
          },
          selectedRowKeys,
        } : false}
        rowKey={(record) => record.key}
        expandable={{
          rowExpandable: (record) => ((MultipleBatchMode && (record?.product_sku_id || record?.product_sku_info?.product_sku_id)) || record?.bundle_products || Helpers.getValueTotalInObject(record?.product_batches, 'available_qty')) && !['SERVICE', 'NON_STORABLE'].includes(record?.product_sku_info?.product_type),
          defaultExpandedRowKeys: MultipleBatchMode ? grnTableData?.map((item) => item.key) : getBundleLineKeys(getDataSource(grnTableData)),
          expandedRowRender: (record) => (
            <Fragment>
              <MultipleBatchesSelector
                grnTableData={grnTableData}
                formSubmitted={formSubmitted}
                updateGrnTableData={(value) => updateGrnTableData(value)}
                destDepartmentId={tenantDepartmentId}
                availableBatches={(value) => availableBatches(value)}
                selectedLineInfo={(value) => selectedLineInfo(value)}
                showLineBatches={(value) => showLineBatches(value)}
                setMultipleBatchQuantity={(value) => setMultipleBatchQuantity(value)}
                multipleBatchQuantity={multipleBatchQuantity}
                grnTypeValue={grnTypeValue}
                enabledWarehouseLocations={enabledLocationBasedStock && enabledLocationBasedStock}
                multipleBatchData={record?.multiple_batch_info}
                isBatchValidValue={isBatchValid}
                setIsBatchValid={setIsBatchValid}
                billFromState={billFromState}
                billToState={billToState}
                customField={record?.selectedBatch?.custom_fields}
                updateBatchCfsForMultiBatchMode={updateBatchCfsForMultiBatchMode}
                isApInvoiceEnabled={isApInvoiceEnabled}
                customInputChangeProps={(value, cfId, key, returnCfData) => customLineInputChange(value, cfId, key, returnCfData)}
              />
            </Fragment>
          ),
        }}
      />
    </div>
  );
};

const mapStateToProps = ({
  UserReducers,
}) => ({
  user: UserReducers.user,
  MONEY: UserReducers.MONEY,
  priceMasking: UserReducers.priceMasking,
});

const mapDispatchToProps = () => ({

});

export default connect(mapStateToProps, mapDispatchToProps)(withRouter(GRNFormLines));
