import React, { Fragment } from 'react';
import { Link } from 'react-router-dom';

// Ant Design Components
import { Table } from 'antd';

// Constants & Helpers
import { toISTDate } from '@Apis/constants';

// UI Components
import H3Text from '@Uilib/h3Text';
import H3FormInput from '@Uilib/h3FormInput';

const CreateOutgoingPaymentLines = ({
  data,
  MONEY,
  amount,
  updateData,
  loading,
  checkMaxAmount,
}) => {
  const columns = [
    {
      title: "REFERENCE #",
      render: (text, record) => {
        return (
          <Link
            to={record?.source === 'grn' ? `/purchase/goods-receiving/view/${record?.entity_id}` : `/purchase/account-payable-invoice/view/${record?.entity_id}`}
            target="_blank"
          >
            {record?.entity_number}
          </Link>
        );
      },
    },
    {
      title: "CREATED TIME",
      render: (text, record) => (
        <div>
          {toISTDate(record.created_at).format("DD/MM/YYYY")}
          <H3Text
            text={
              record?.created_by_info
                ? `${record?.created_by_info.first_name} ${record?.created_by_info.last_name}`
                : "Unknown"
            }
            className="table-subscript"
          />
        </div>
      ),
    },
    {
      title: "DATE",
      render: (text, record) =>
        `${record?.date_time
          ? toISTDate(record?.date_time)?.format("DD/MM/YYYY")
          : ""
        }`,
    },
    {
      title: "TOTAL AMOUNT",
      dataIndex: "seller_name",
      render: (text, record) => MONEY(record?.grand_total_amount),
    },
    {
      title: "AMOUNT DUE",
      dataIndex: "seller_name",
      render: (text, record) =>
        MONEY(
          record?.grand_total_amount -
          record?.debit_note_info?.reduce((acc, dn) => acc + dn?.db_total, 0)
        ),
    },
    {
      title: "PAYMENT",
      render: (text, record) => {
        return (
          <div style={{ width: "120px" }}>
            <H3FormInput
              value={record?.payment}
              type="number"
              labelClassName="orgFormLabel"
              inputClassName="orgFormInput"
              onChange={(e) => {
                handlePaymentChange(e.target.value, record?.id);
              }}
              disabled={
                !amount ||
                (!record?.payment && !checkMaxAmount()?.paymentExceed)
              }
            />
            <H3Text
              text={<div>Pay in Full</div>}
              onClick={() =>
                checkMaxAmount()?.paymentExceed && handleFullPayment(record)
              }
              className="goods-receiving__po_no-pay"
            />
            {(Number(record?.payment) >
              Number(
                record?.grand_total_amount -
                record?.debit_note_info?.reduce(
                  (acc, dn) => acc + dn?.db_total,
                  0
                )
              ) ||
              Number(record?.payment) > Number(amount)) && (
                <div className="input-error">Please enter valid amount*</div>
              )}
          </div>
        );
      },
    },
  ];
  function handlePaymentChange(value, id) {
    const updatedData = JSON.parse(JSON.stringify(data));

    for (let i = 0; i < updatedData.length; i++) {
      if (updatedData[i]?.id === id) {
        updatedData[i].payment = Number(value);
        break;
      }
    }
    updateData(updatedData);
  }

  function handleFullPayment(record) {
    const copyData = data?.map((item) => {
      if (item?.id === record?.id) {
        return {
          ...item,
          payment:
            checkMaxAmount(record?.id)?.remainingAmount >
              Number(
                item?.grand_total_amount -
                item?.payment_made -
                record?.debit_note_info?.reduce(
                  (acc, dn) => acc + dn?.db_total,
                  0
                )
              )
              ? (item?.grand_total_amount ||
                item?.grand_total * (item?.conversion_rate || 1)) -
              item?.payment_made -
              record?.debit_note_info?.reduce(
                (acc, dn) => acc + dn?.db_total,
                0
              )
              : checkMaxAmount(record?.id)?.remainingAmount,
        };
      }
      return {
        ...item,
      };
    });
    updateData(copyData);
  }

  return (
    <Fragment>
      <div className="purchase-payment__table">
        <Table
          size="small"
          scroll="scroll"
          columns={columns}
          loading={loading}
          dataSource={data || []}
          pagination={false}
        />
      </div>
    </Fragment>
  );
};

export default React.memo(CreateOutgoingPaymentLines);
